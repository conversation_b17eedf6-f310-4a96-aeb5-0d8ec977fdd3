# Notification System Patterns from Huly

## Overview

Huly's notification system provides multi-channel alerts, in-app notifications, and external integrations. This is crucial for statsFactory to alert users about data quality issues, analysis completion, and collaborative activities.

## Core Components

### 1. Notification Types

**Huly Pattern:**
```typescript
interface Notification {
  id: string
  type: 'info' | 'warning' | 'error' | 'success'
  category: string
  title: string
  message: string
  timestamp: Date
  read: boolean
  actions?: NotificationAction[]
}
```

**statsFactory Adaptation:**
```typescript
interface StatsNotification extends Notification {
  category: 'data' | 'analysis' | 'collaboration' | 'system'
  severity: 'low' | 'medium' | 'high' | 'critical'
  source: {
    type: 'dataset' | 'analysis' | 'report' | 'user'
    id: string
    name: string
  }
  metadata?: {
    affectedRecords?: number
    analysisId?: string
    collaboratorId?: string
    errorDetails?: any
  }
}
```

### 2. Notification Channels

**Huly Pattern:**
- In-app notifications (toast, notification center)
- Email notifications
- Webhooks for external systems
- Mobile push notifications

**statsFactory Implementation:**
```typescript
interface NotificationChannel {
  id: string
  type: 'in-app' | 'email' | 'webhook' | 'sms' | 'slack'
  enabled: boolean
  config: ChannelConfig
  
  send(notification: StatsNotification): Promise<void>
  validateConfig(): boolean
}

// Example channel configurations
interface EmailChannelConfig {
  smtp: SMTPConfig
  templates: Map<string, EmailTemplate>
  defaultFrom: string
}

interface WebhookChannelConfig {
  url: string
  method: 'POST' | 'PUT'
  headers?: Record<string, string>
  retryPolicy: RetryPolicy
}
```

### 3. Notification Rules Engine

**Huly Pattern:**
- Rule-based notification triggering
- User preference management
- Subscription system for specific events

**statsFactory Rules:**
```typescript
interface NotificationRule {
  id: string
  name: string
  description: string
  
  // Trigger conditions
  trigger: {
    event: string // e.g., 'data.quality.issue', 'analysis.complete'
    conditions?: Condition[] // Additional filters
  }
  
  // Target configuration
  target: {
    users?: string[] // Specific user IDs
    roles?: string[] // User roles
    channels: string[] // Which channels to use
  }
  
  // Message customization
  template: {
    title: string
    message: string
    variables: string[] // Available template variables
  }
  
  // Control settings
  throttle?: {
    maxPerHour?: number
    maxPerDay?: number
  }
  priority: 'low' | 'medium' | 'high'
  enabled: boolean
}
```

## statsFactory-Specific Notifications

### 1. Data Quality Alerts
```typescript
interface DataQualityNotification {
  type: 'data.quality'
  issues: QualityIssue[]
  dataset: DatasetReference
  suggestions: string[]
}

// Example triggers:
// - Missing values exceed threshold
// - Outliers detected
// - Schema validation failures
// - Data freshness violations
```

### 2. Analysis Notifications
```typescript
interface AnalysisNotification {
  type: 'analysis.status'
  status: 'started' | 'completed' | 'failed' | 'warning'
  analysis: AnalysisReference
  duration?: number
  results?: AnalysisSummary
  errors?: Error[]
}
```

### 3. Collaboration Notifications
```typescript
interface CollaborationNotification {
  type: 'collaboration'
  action: 'mentioned' | 'shared' | 'commented' | 'modified'
  actor: UserReference
  target: ResourceReference
  context?: string
}
```

### 4. System Notifications
```typescript
interface SystemNotification {
  type: 'system'
  subtype: 'maintenance' | 'update' | 'performance' | 'security'
  affectedServices?: string[]
  scheduledTime?: Date
  estimatedDuration?: number
}
```

## User Preference Management

**Huly Pattern:**
```typescript
interface NotificationPreferences {
  userId: string
  
  // Global settings
  enabled: boolean
  quietHours?: TimeRange
  
  // Channel preferences
  channels: {
    [channelId: string]: {
      enabled: boolean
      categories?: string[]
    }
  }
  
  // Category preferences
  categories: {
    [category: string]: {
      enabled: boolean
      channels?: string[]
      minSeverity?: string
    }
  }
  
  // Subscription management
  subscriptions: NotificationSubscription[]
}
```

## Implementation Architecture

### 1. Notification Service
```typescript
class NotificationService {
  private channels: Map<string, NotificationChannel>
  private rules: NotificationRule[]
  private queue: NotificationQueue
  
  // Core methods
  async send(notification: StatsNotification): Promise<void>
  async broadcast(event: NotificationEvent): Promise<void>
  
  // Channel management
  registerChannel(channel: NotificationChannel): void
  unregisterChannel(channelId: string): void
  
  // Rule management
  addRule(rule: NotificationRule): void
  updateRule(ruleId: string, updates: Partial<NotificationRule>): void
  
  // User preferences
  getUserPreferences(userId: string): NotificationPreferences
  updateUserPreferences(userId: string, prefs: Partial<NotificationPreferences>): void
}
```

### 2. Notification Queue
```typescript
interface NotificationQueue {
  enqueue(notification: QueuedNotification): Promise<void>
  process(): Promise<void>
  retry(notificationId: string): Promise<void>
  getStatus(notificationId: string): QueueStatus
}
```

### 3. Template Engine
```typescript
interface TemplateEngine {
  compile(template: string, data: any): string
  registerHelper(name: string, helper: Function): void
  validateTemplate(template: string): ValidationResult
}

// Example template
const analysisCompleteTemplate = `
  Hi {{user.name}},
  
  Your {{analysis.type}} analysis on "{{dataset.name}}" has completed successfully.
  
  Summary:
  - Records processed: {{analysis.recordCount}}
  - Duration: {{analysis.duration}}
  - Key findings: {{analysis.keyFindings}}
  
  [View Results]({{analysis.resultUrl}})
`
```

## UI Components

### 1. Toast Notifications
```typescript
interface ToastNotification {
  show(options: {
    type: 'info' | 'success' | 'warning' | 'error'
    title: string
    message?: string
    duration?: number
    actions?: Action[]
  }): void
  
  dismiss(id: string): void
  dismissAll(): void
}
```

### 2. Notification Center
```typescript
interface NotificationCenter {
  notifications: StatsNotification[]
  unreadCount: number
  
  markAsRead(notificationId: string): void
  markAllAsRead(): void
  delete(notificationId: string): void
  
  // Filtering and sorting
  filter(criteria: FilterCriteria): StatsNotification[]
  sort(by: 'date' | 'severity' | 'category'): void
}
```

### 3. React Components
```tsx
// Notification badge
<NotificationBadge count={unreadCount} />

// Notification list
<NotificationList 
  notifications={notifications}
  onRead={handleRead}
  onDelete={handleDelete}
/>

// Notification preferences
<NotificationPreferences
  preferences={userPreferences}
  onChange={updatePreferences}
/>
```

## Integration Examples

### 1. Data Quality Monitoring
```typescript
// Automatically notify on data quality issues
const dataQualityMonitor = {
  async checkDataset(datasetId: string) {
    const issues = await analyzeDataQuality(datasetId)
    
    if (issues.length > 0) {
      await notificationService.send({
        type: 'warning',
        category: 'data',
        title: 'Data Quality Issues Detected',
        message: `Found ${issues.length} issues in dataset`,
        severity: calculateSeverity(issues),
        source: { type: 'dataset', id: datasetId },
        metadata: { issues }
      })
    }
  }
}
```

### 2. Long-Running Analysis
```typescript
// Notify when analysis completes
const analysisRunner = {
  async runAnalysis(config: AnalysisConfig) {
    await notificationService.send({
      type: 'info',
      category: 'analysis',
      title: 'Analysis Started',
      message: `Running ${config.type} analysis...`
    })
    
    try {
      const result = await performAnalysis(config)
      
      await notificationService.send({
        type: 'success',
        category: 'analysis',
        title: 'Analysis Complete',
        message: 'Your analysis has finished successfully',
        actions: [
          { label: 'View Results', url: `/analysis/${result.id}` }
        ]
      })
    } catch (error) {
      await notificationService.send({
        type: 'error',
        category: 'analysis',
        title: 'Analysis Failed',
        message: error.message,
        severity: 'high'
      })
    }
  }
}
```

## Best Practices

1. **Notification Fatigue Prevention**
   - Implement smart throttling
   - Group similar notifications
   - Provide easy unsubscribe options
   - Use severity levels appropriately

2. **Performance Optimization**
   - Queue notifications for batch processing
   - Implement retry logic with exponential backoff
   - Use WebSocket for real-time in-app notifications
   - Cache user preferences

3. **User Experience**
   - Make notifications actionable
   - Provide clear context
   - Allow customization
   - Respect quiet hours

4. **Security**
   - Sanitize notification content
   - Validate webhook URLs
   - Implement rate limiting
   - Encrypt sensitive data in notifications

## Implementation Roadmap

### Phase 1: Basic Infrastructure
1. Create notification service core
2. Implement in-app toast notifications
3. Build notification center UI
4. Add user preference management

### Phase 2: Multi-Channel Support
1. Add email notifications
2. Implement webhook support
3. Create Slack integration
4. Build SMS capabilities

### Phase 3: Advanced Features
1. Implement rules engine
2. Add template system
3. Create notification analytics
4. Build automation workflows

## References

- Huly notification service: `services/notification/src/index.ts`
- Material-UI Snackbar patterns
- Slack notification API
- SendGrid/Twilio best practices
