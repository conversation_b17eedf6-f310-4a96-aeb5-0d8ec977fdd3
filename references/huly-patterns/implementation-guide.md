# Huly Features Implementation Guide for StatsFactory

## Quick Start: Plugin Architecture

### Step 1: Create Plugin Foundation

```typescript
// src/platform/plugin.ts
export type PluginId = string & { __plugin: true }
export type Id = string & { __id: true }
export type Resource<T> = Id & { __resource: T }

export interface PluginModule<T extends Resources> {
  id: PluginId
  resources: T
}

export interface Resources {
  class?: Record<string, Ref<Class<any>>>
  component?: Record<string, AnyComponent>
  string?: Record<string, IntlString>
  function?: Record<string, Resource<any>>
}

// Plugin creation helper
export function plugin<N extends Resources>(id: PluginId, resources: N): PluginModule<N> {
  return { id, resources }
}
```

### Step 2: Convert Features to Plugins

```typescript
// src/plugins/dashboard/index.ts
import { plugin } from '@/platform/plugin'

export const dashboardId = 'dashboard' as PluginId

export default plugin(dashboardId, {
  class: {
    Dashboard: '' as Ref<Class<Dashboard>>,
    Widget: '' as Ref<Class<Widget>>
  },
  component: {
    DashboardView: '' as AnyComponent,
    WidgetEditor: '' as AnyComponent
  },
  string: {
    CreateDashboard: '' as IntlString,
    EditWidget: '' as IntlString
  }
})
```

### Step 3: Plugin Registry

```typescript
// src/platform/registry.ts
interface PluginRegistry {
  plugins: Map<PluginId, PluginModule<any>>
  
  register(plugin: PluginModule<any>): void
  get<T extends Resources>(id: PluginId): PluginModule<T> | undefined
  getResource<T>(id: Resource<T>): T | undefined
}

export const registry: PluginRegistry = {
  plugins: new Map(),
  
  register(plugin) {
    this.plugins.set(plugin.id, plugin)
  },
  
  get(id) {
    return this.plugins.get(id)
  },
  
  getResource(id) {
    const [pluginId, , resourceId] = id.split(':')
    const plugin = this.get(pluginId as PluginId)
    // Implementation to extract resource
  }
}
```

## Notification System Implementation

### Step 1: Core Notification Types

```typescript
// src/notifications/types.ts
export interface NotificationProvider extends Doc {
  id: string
  label: string
  enabled: boolean
  order: number
}

export interface NotificationType extends Doc {
  id: string
  label: string
  group: string
  defaultEnabled: boolean
  providers: string[]
}

export interface InboxNotification extends Doc {
  userId: string
  title: string
  body: string
  isRead: boolean
  createdAt: Date
  metadata?: Record<string, any>
}
```

### Step 2: Notification Service

```typescript
// src/notifications/service.ts
export class NotificationService {
  private providers: Map<string, NotificationProvider> = new Map()
  
  async sendNotification(params: {
    userId: string
    type: NotificationType
    title: string
    body: string
    metadata?: Record<string, any>
  }) {
    // Check user preferences
    const preferences = await this.getUserPreferences(params.userId)
    
    // Send to enabled providers
    for (const providerId of params.type.providers) {
      if (preferences[providerId]?.enabled) {
        await this.sendToProvider(providerId, params)
      }
    }
    
    // Always create inbox notification
    await this.createInboxNotification(params)
  }
  
  private async sendToProvider(providerId: string, params: any) {
    const provider = this.providers.get(providerId)
    
    switch (providerId) {
      case 'email':
        await this.sendEmail(params)
        break
      case 'push':
        await this.sendPush(params)
        break
      case 'browser':
        await this.sendBrowserNotification(params)
        break
    }
  }
}
```

### Step 3: React Hook for Notifications

```typescript
// src/notifications/hooks.ts
export function useNotifications() {
  const [notifications, setNotifications] = useState<InboxNotification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  
  useEffect(() => {
    // Subscribe to real-time notifications
    const channel = supabase
      .channel('notifications')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'notifications',
        filter: `user_id=eq.${userId}`
      }, (payload) => {
        setNotifications(prev => [payload.new, ...prev])
        setUnreadCount(prev => prev + 1)
      })
      .subscribe()
      
    return () => {
      supabase.removeChannel(channel)
    }
  }, [userId])
  
  const markAsRead = async (id: string) => {
    await supabase
      .from('notifications')
      .update({ is_read: true })
      .eq('id', id)
  }
  
  return {
    notifications,
    unreadCount,
    markAsRead
  }
}
```

## Real-Time Features Implementation

### Step 1: WebSocket Infrastructure

```typescript
// src/realtime/client.ts
export class RealtimeClient {
  private ws: WebSocket
  private subscriptions: Map<string, Set<(data: any) => void>> = new Map()
  
  connect(url: string) {
    this.ws = new WebSocket(url)
    
    this.ws.onmessage = (event) => {
      const { channel, data } = JSON.parse(event.data)
      const handlers = this.subscriptions.get(channel)
      
      if (handlers) {
        handlers.forEach(handler => handler(data))
      }
    }
  }
  
  subscribe(channel: string, handler: (data: any) => void) {
    if (!this.subscriptions.has(channel)) {
      this.subscriptions.set(channel, new Set())
    }
    
    this.subscriptions.get(channel)!.add(handler)
    
    // Send subscription message
    this.ws.send(JSON.stringify({
      type: 'subscribe',
      channel
    }))
    
    return () => {
      this.subscriptions.get(channel)?.delete(handler)
    }
  }
}
```

### Step 2: Presence System

```typescript
// src/realtime/presence.ts
export function usePresence(documentId: string) {
  const [users, setUsers] = useState<User[]>([])
  const client = useRealtimeClient()
  
  useEffect(() => {
    const channel = `presence:${documentId}`
    
    // Join presence
    client.send({
      type: 'presence',
      action: 'join',
      channel,
      user: currentUser
    })
    
    // Subscribe to presence updates
    const unsubscribe = client.subscribe(channel, (data) => {
      if (data.type === 'presence_state') {
        setUsers(data.users)
      }
    })
    
    return () => {
      client.send({
        type: 'presence',
        action: 'leave',
        channel
      })
      unsubscribe()
    }
  }, [documentId])
  
  return users
}
```

## Migration Strategy

### Phase 1: Foundation (Week 1)
1. Implement basic plugin system
2. Create plugin for each major feature
3. Set up resource registry

### Phase 2: Notifications (Week 2)
1. Create notification tables in Supabase
2. Implement notification service
3. Add UI components
4. Set up user preferences

### Phase 3: Real-Time (Week 3-4)
1. Add WebSocket server
2. Implement presence system
3. Add real-time updates to dashboards
4. Create activity feed

## Database Schema Changes

```sql
-- Notifications
CREATE TABLE notification_providers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  label TEXT NOT NULL,
  enabled BOOLEAN DEFAULT true,
  order_index INTEGER DEFAULT 0
);

CREATE TABLE notification_types (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  label TEXT NOT NULL,
  group_name TEXT NOT NULL,
  default_enabled BOOLEAN DEFAULT true,
  providers JSONB DEFAULT '[]'
);

CREATE TABLE inbox_notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  type_id UUID REFERENCES notification_types(id),
  title TEXT NOT NULL,
  body TEXT NOT NULL,
  is_read BOOLEAN DEFAULT false,
  metadata JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- User preferences
CREATE TABLE notification_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  provider_id UUID REFERENCES notification_providers(id),
  type_id UUID REFERENCES notification_types(id),
  enabled BOOLEAN DEFAULT true,
  UNIQUE(user_id, provider_id, type_id)
);
```

## Component Examples

### Notification Bell

```tsx
export function NotificationBell() {
  const { notifications, unreadCount } = useNotifications()
  const [open, setOpen] = useState(false)
  
  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge 
              className="absolute -top-1 -right-1 h-5 w-5 p-0"
              variant="destructive"
            >
              {unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80">
        <NotificationList notifications={notifications} />
      </PopoverContent>
    </Popover>
  )
}
```

### Activity Feed

```tsx
export function ActivityFeed({ entityId }: { entityId: string }) {
  const activities = useActivityFeed(entityId)
  
  return (
    <div className="space-y-4">
      {activities.map((activity) => (
        <ActivityItem key={activity.id} activity={activity} />
      ))}
    </div>
  )
}
```

## Testing Strategy

1. Unit tests for plugin system
2. Integration tests for notifications
3. E2E tests for real-time features
4. Performance benchmarks

## Rollback Plan

1. Feature flags for each new system
2. Parallel run old and new systems
3. Gradual migration of users
4. Easy disable switches

## Success Metrics

- Plugin load time < 100ms
- Notification delivery < 1s
- Real-time latency < 200ms
- User engagement increase > 20%
