# Huly Platform Patterns Reference

This directory contains documentation and code patterns from the Huly platform (https://github.com/hcengineering/platform) that we're adapting for statsFactory.

## Purpose

Rather than cloning the entire Huly repository, we document the specific patterns and architectures that are relevant to enhancing statsFactory's collaborative statistical analysis capabilities.

## Key Features to Adapt

1. **Real-time Collaboration System** (Tasks 10, 19)
2. **Task Management for Workflows** (Task 29)
3. **Advanced Notification Framework** (Task 30)
4. **Plugin Architecture** (Task 31)

## Structure

- `real-time-patterns.md` - WebSocket and live collaboration patterns
- `plugin-architecture.md` - Extensible plugin system design
- `notification-system.md` - In-app and external notification patterns
- `task-workflow.md` - Task management and workflow orchestration
- `ui-patterns/` - Reusable UI/UX patterns from Huly

## Important Notes

- Huly uses Svelte, but we're adapting patterns to React/TypeScript
- Focus on architectural patterns, not direct code copying
- <PERSON>ly is licensed under EPL-2.0, ensure proper attribution

## Reference Links

- Huly Platform: https://github.com/hcengineering/platform
- Huly Demo: https://huly.io
- Huly Docs: https://huly.io/docs
