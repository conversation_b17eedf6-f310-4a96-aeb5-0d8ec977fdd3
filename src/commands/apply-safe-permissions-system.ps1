# Apply the safe permissions system script
# This script fixes the infinite recursion issue in RLS policies

# Change to the project root directory
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$projectRoot = (Get-Item $scriptDir).Parent.Parent.FullName
Set-Location $projectRoot

# Check if Node.js is installed
try {
    $nodeVersion = node -v
    Write-Host "Using Node.js $nodeVersion"
} catch {
    Write-Host "Error: Node.js is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

# Run the apply script directly
# Our JavaScript script now handles both initialization and migration
Write-Host "Applying safe permissions system..." -ForegroundColor Cyan
node src/commands/apply-safe-permissions-system.js

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to apply the permission system" -ForegroundColor Red
    Write-Host "Please check the error messages above" -ForegroundColor Red
    exit 1
} else {
    Write-Host "✅ Permission system successfully applied!" -ForegroundColor Green
    Write-Host "Now you can use the role testing tools in the Admin Panel" -ForegroundColor Green
}
