/**
 * <PERSON><PERSON>t to migrate deprecated hooks to their modern equivalents
 * 
 * This script:
 * 1. Finds and replaces usePermissionsContext with usePermissions
 * 2. Updates variable names and imports
 * 3. Documents the changes made
 * 
 * Usage: 
 * node src/commands/migrate-deprecated-hooks.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

// Get current file directory (equivalent to __dirname in CommonJS)
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m',
};

console.log(`${colors.magenta}🛠  Preparing to migrate deprecated hooks...${colors.reset}\n`);

// List of files to scan - we need to find all TypeScript/React files
const fileExtensions = ['.tsx', '.ts', '.jsx', '.js'];
const srcDir = path.join(process.cwd(), 'src');
let filesToProcess = [];

/**
 * Recursively find all files with specified extensions
 */
function findFilesRecursively(dir, extensions) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // Skip node_modules and other non-source directories
      if (!file.startsWith('.') && file !== 'node_modules' && file !== 'dist' && file !== 'build') {
        findFilesRecursively(filePath, extensions);
      }
    } else if (extensions.includes(path.extname(file))) {
      filesToProcess.push(filePath);
    }
  });
}

try {
  // Find all source files to process
  findFilesRecursively(srcDir, fileExtensions);
  
  console.log(`${colors.blue}🔍 Found ${filesToProcess.length} files to scan${colors.reset}`);
  
  let modifiedFilesCount = 0;
  let importUpdates = 0;
  let hookInstances = 0;
  let variableRenames = 0;
  
  // Process each file
  filesToProcess.forEach(filePath => {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      let newContent = content;
      let fileModified = false;
      
      // 1. Update imports
      if (newContent.includes('usePermissionsContext') && newContent.includes('import')) {
        // Handle multiple import patterns
        
        // Case 1: import { usePermissions } from '@/hooks/usePermissions';
        const importRegex1 = /import\s*\{\s*usePermissionsContext\s*\}\s*from\s*['"]@\/hooks\/usePermissions['"];/g;
        if (importRegex1.test(newContent)) {
          newContent = newContent.replace(importRegex1, `import { usePermissions } from '@/hooks/usePermissions';`);
          importUpdates++;
          fileModified = true;
        }
        
        // Case 2: import { ..., ... } from '@/hooks/usePermissions';
        const importRegex2 = /import\s*\{\s*(.*),\s*usePermissionsContext\s*(,.*?)?\s*\}\s*from\s*['"]@\/hooks\/usePermissions['"];/g;
        if (importRegex2.test(newContent)) {
          newContent = newContent.replace(importRegex2, (match, before, after) => {
            // If usePermissions is already imported, just remove usePermissionsContext
            if (match.includes('usePermissions')) {
              return `import { ${before}${after || ''} } from '@/hooks/usePermissions';`;
            } else {
              return `import { ${before}, usePermissions${after || ''} } from '@/hooks/usePermissions';`;
            }
          });
          importUpdates++;
          fileModified = true;
        }
        
        // Case 3: import { usePermissions as SomeAlias } from '@/hooks/usePermissions';
        const importRegex3 = /import\s*\{\s*usePermissionsContext\s+as\s+(\w+)\s*\}\s*from\s*['"]@\/hooks\/usePermissions['"];/g;
        const aliasMatches = [...newContent.matchAll(/import\s*\{\s*usePermissionsContext\s+as\s+(\w+)\s*\}\s*from\s*['"]@\/hooks\/usePermissions['"];/g)];
        
        if (aliasMatches.length > 0) {
          for (const match of aliasMatches) {
            const alias = match[1];
            newContent = newContent.replace(match[0], `import { usePermissions as ${alias} } from '@/hooks/usePermissions';`);
          }
          importUpdates++;
          fileModified = true;
        }
      }
      
      // 2. Update hook usage
      // Replace: const permissions = usePermissions();
      // With:    const permissions = usePermissions();
      const hookRegex = /const\s+(\w+)\s*=\s*usePermissionsContext\(\);/g;
      const hookMatches = [...newContent.matchAll(hookRegex)];
      
      if (hookMatches.length > 0) {
        for (const match of hookMatches) {
          const variableName = match[1];
          
          // Create a more appropriate variable name based on the original
          let newVariableName;
          if (variableName === 'permissions') {
            newVariableName = 'permissions';
          } else if (variableName.endsWith('Context')) {
            newVariableName = variableName.replace(/Context$/, '');
          } else {
            newVariableName = variableName;
          }
          
          newContent = newContent.replace(
            match[0], 
            `const ${newVariableName} = usePermissions();`
          );
          
          // If variable name changed, update all usages
          if (newVariableName !== variableName) {
            // Use regex with word boundaries to avoid partial matches
            const varRegex = new RegExp(`\\b${variableName}\\b`, 'g');
            const beforeReplaceContent = newContent;
            newContent = newContent.replace(varRegex, newVariableName);
            
            // Count how many replacements were made
            const replacementCount = (beforeReplaceContent.match(varRegex) || []).length - 1; // -1 because we already replaced the declaration
            variableRenames += replacementCount > 0 ? replacementCount : 0;
          }
          
          hookInstances++;
          fileModified = true;
        }
      }
      
      // Only write back if changes were made
      if (fileModified) {
        fs.writeFileSync(filePath, newContent);
        console.log(`${colors.green}✓ Updated${colors.reset} ${path.relative(process.cwd(), filePath)}`);
        modifiedFilesCount++;
      }
    } catch (err) {
      console.error(`${colors.red}Error processing file ${filePath}:${colors.reset}`, err);
    }
  });
  
  console.log(`\n${colors.green}✅ Migration complete!${colors.reset}`);
  console.log(`${colors.cyan}Summary:${colors.reset}`);
  console.log(`   - Scanned: ${filesToProcess.length} files`);
  console.log(`   - Modified: ${modifiedFilesCount} files`);
  console.log(`   - Updated imports: ${importUpdates}`);
  console.log(`   - Updated hook instances: ${hookInstances}`);
  console.log(`   - Updated variable references: ${variableRenames}`);
  
  // Create documentation
  const docsPath = path.join(process.cwd(), 'docs', 'HOOK-MIGRATION.md');
  
  if (!fs.existsSync(docsPath)) {
    const docsContent = `# Hook Migration Guide

## Overview

The codebase has been migrated from using deprecated hooks to their modern equivalents. This document outlines the changes made and provides guidance for any future development.

## Migrated Hooks

### \`usePermissionsContext\` → \`usePermissions\`

The \`usePermissionsContext\` hook was deprecated and has been replaced with \`usePermissions\`. This change improves performance and reduces console warnings.

#### Before

\`\`\`tsx
import { usePermissions } from '@/hooks/usePermissions';

const MyComponent = () => {
  const permissions = usePermissionsContext();
  
  // Using permissions
  const canEdit = permissions.hasCapability('edit');
  
  return (
    // Component JSX
  );
};
\`\`\`

#### After

\`\`\`tsx
import { usePermissions } from '@/hooks/usePermissions';

const MyComponent = () => {
  const permissions = usePermissions();
  
  // Using permissions
  const canEdit = permissions.hasCapability('edit');
  
  return (
    // Component JSX
  );
};
\`\`\`

## Migration Summary

The automatic migration process made the following changes:

- Updated ${importUpdates} import statements
- Migrated ${hookInstances} hook usage instances
- Renamed ${variableRenames} variable references

A total of ${modifiedFilesCount} files were modified during this process.

## Benefits

- Reduced console warnings
- Improved performance
- Prevention of duplicate context subscriptions
- Better code maintainability

## Future Development

When developing new components or features:

1. Always use \`usePermissions\` instead of \`usePermissionsContext\`
2. Follow the pattern of naming the returned value \`permissions\` instead of \`permissions\`
3. Leverage the circuit-breaker and error handling features built into the new hook
`;
    
    fs.writeFileSync(docsPath, docsContent);
    console.log(`\n${colors.green}✅ Created documentation: docs/HOOK-MIGRATION.md${colors.reset}`);
  } else {
    console.log(`\n${colors.yellow}⚠️ Documentation file already exists: docs/HOOK-MIGRATION.md${colors.reset}`);
  }
  
} catch (err) {
  console.error(`${colors.red}Error during migration:${colors.reset}`, err);
  process.exit(1);
}
