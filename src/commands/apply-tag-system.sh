#!/bin/bash

# Shell script to execute the tag system migration script

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "Error: Node.js is not installed. Please install Node.js to run this script."
    exit 1
fi

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Execute the JavaScript file
node "$SCRIPT_DIR/apply-tag-system.js" "$@"
