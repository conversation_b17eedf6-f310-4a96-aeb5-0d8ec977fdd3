/**
 * Fix RPC Function Exposure Script
 * 
 * This script helps to fix exposure issues with RPC functions in Supabase.
 * It supports:
 * 1. Running a verification check on RPC functions
 * 2. Generating SQL to fix the exposure issues
 * 3. Applying the SQL to fix the functions
 * 
 * Usage: 
 * node fix-rpc-function-exposure.mjs --function=get_dynamic_user_capabilities --verify
 * node fix-rpc-function-exposure.mjs --function=get_dynamic_user_capabilities --fix
 * node fix-rpc-function-exposure.mjs --all --verify
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import dotenv from 'dotenv';

// Initialize dotenv
dotenv.config();

// Get current file directory (ESM equivalent of __dirname)
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables with fallbacks and validation
const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_KEY = process.env.VITE_SUPABASE_KEY || process.env.VITE_SUPABASE_ANON_KEY;
const SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY; // For admin operations

// Function to safely get Supabase URL without exposing it in logs
function getSafeSupabaseUrl() {
  if (!SUPABASE_URL) return '<missing-url>';
  // Return masked version for logs to avoid exposing the full URL
  const urlParts = SUPABASE_URL.split('.');
  if (urlParts.length >= 3) {
    const projectId = urlParts[0].split('//')[1];
    return projectId ? `${projectId.substr(0, 4)}...` : '<invalid-url>';
  }
  return '<invalid-url-format>';
}

// Critical RPC functions that should always be accessible
const CriticalFunctions = [
  'get_dynamic_user_capabilities',
  'get_user_caps',
  'get_role_permissions_matrix',
  'get_leaderboard'
];

// Ensure we have the necessary keys
if (!SUPABASE_URL || (!SUPABASE_KEY && !SERVICE_KEY)) {
  console.error('Error: Missing Supabase configuration.');
  console.error('Make sure VITE_SUPABASE_URL and either VITE_SUPABASE_KEY/ANON_KEY or SUPABASE_SERVICE_KEY are set.');
  console.error(`Current project: ${getSafeSupabaseUrl()}`); // Use the safe URL function
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(
  SUPABASE_URL,
  SERVICE_KEY || SUPABASE_KEY,
  { auth: { persistSession: false } }
);

// Parse command line arguments
const args = process.argv.slice(2);
const verifyMode = args.includes('--verify');
const fixMode = args.includes('--fix');
const allFunctions = args.includes('--all');
let specificFunction = null;

for (const arg of args) {
  if (arg.startsWith('--function=')) {
    specificFunction = arg.split('=')[1];
    break;
  }
}

// Show usage if no proper arguments
if ((!verifyMode && !fixMode) || (!allFunctions && !specificFunction)) {
  console.log(`
Usage:
  node fix-rpc-function-exposure.mjs --function=FUNCTION_NAME --verify
  node fix-rpc-function-exposure.mjs --function=FUNCTION_NAME --fix
  node fix-rpc-function-exposure.mjs --all --verify
  node fix-rpc-function-exposure.mjs --all --fix
  
Examples:
  node fix-rpc-function-exposure.mjs --function=get_dynamic_user_capabilities --verify
  node fix-rpc-function-exposure.mjs --function=get_dynamic_user_capabilities --fix
  node fix-rpc-function-exposure.mjs --all --verify
  `);
  process.exit(0);
}

/**
 * Checks if an RPC function exists and is accessible
 * @param {string} functionName - The name of the function to check
 * @returns {Promise<{exists: boolean, accessible: boolean, error: string|null}>}
 */
async function checkFunctionExposure(functionName) {
  try {
    // Try calling the function via RPC
    // Use a dummy parameter that should work for most functions
    const { data, error } = await supabase.rpc(functionName, { 
      p_user_id: '00000000-0000-0000-0000-000000000000'
    });
    
    if (error) {
      // If error code is 404, function is not exposed via REST
      if (error.code === '404') {
        return { 
          exists: false, // We don't actually know if it exists in the DB
          accessible: false,
          error: `Function not accessible via REST API: ${error.message}`
        };
      }
      
      // Other errors could mean the function exists but our parameters are wrong
      return { 
        exists: true, 
        accessible: true, 
        error: `Function exists but returned error: ${error.message}`
      };
    }
    
    // Function worked!
    return { exists: true, accessible: true, error: null };
    
  } catch (e) {
    return { 
      exists: false, 
      accessible: false, 
      error: `Error checking function: ${e.message}`
    };
  }
}

/**
 * Checks if a function exists in the database directly
 * @param {string} functionName - The function name to check
 * @returns {Promise<boolean>}
 */
async function checkFunctionExistenceInDb(functionName) {
  try {
    const { data, error } = await supabase
      .from('pg_proc')
      .select('proname')
      .eq('proname', functionName)
      .maybeSingle();
      
    if (error) {
      console.error(`Error checking function in DB: ${error.message}`);
      return false;
    }
    
    return !!data;
  } catch (e) {
    console.error(`Exception checking function in DB: ${e.message}`);
    return false;
  }
}

/**
 * Gets function definition details from the database
 */
async function getFunctionDefinition(functionName) {
  try {
    // This SQL query works for PostgreSQL to get function details
    const { data, error } = await supabase.rpc('get_function_details', {
      p_function_name: functionName
    });
    
    if (error) {
      console.error(`Error getting function definition: ${error.message}`);
      return null;
    }
    
    return data;
  } catch (e) {
    console.error(`Exception getting function definition: ${e.message}`);
    return null;
  }
}

/**
 * Generates SQL to fix RPC function exposure issues
 */
function generateFixSql(functionName, existsInDb) {
  // Base SQL - ensures the function is STABLE and has proper permissions
  let sql = '';
  
  // If we don't know if the function exists for sure, add a conditional check
  if (!existsInDb) {
    sql += `-- Check if function exists first
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_proc WHERE proname = '${functionName}'
  ) THEN
`;
  }
  
  // Add the core fixes - making function STABLE and granting permissions
  sql += `
-- Make sure the function is stable to help PostgreSQL optimize
ALTER FUNCTION ${functionName}(uuid) STABLE;

-- Set security definer and search path
ALTER FUNCTION ${functionName}(uuid) SECURITY DEFINER SET search_path = public;

-- Grant execute permission to BOTH anon and authenticated users
GRANT EXECUTE ON FUNCTION ${functionName}(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION ${functionName}(uuid) TO anon;
GRANT EXECUTE ON FUNCTION ${functionName}(uuid) TO service_role;

/* 
 * Create custom trigger to make the function accessible via REST API
 * This is a special case needed for some deployments where functions might
 * not be automatically exposed through the REST API
 */
DO $$
BEGIN
  -- Add a comment to the function to help with REST API detection
  COMMENT ON FUNCTION ${functionName}(uuid) IS 
  'Function to get all capabilities or permissions for a user. 
  Returns role_id, role_name, capability_id, capability_code, capability_name, 
  capability_category, and is_granted.';
  
  -- Notify about the change
  RAISE NOTICE '${functionName} function updated with REST API exposure';
END$$;`;

  // Close the conditional block if we added it
  if (!existsInDb) {
    sql += `
  ELSE
    RAISE NOTICE 'Function ${functionName} does not exist in the database.';
  END IF;
END$$;`;
  }
  
  return sql;
}

/**
 * Verifies a single function
 */
async function verifyFunction(functionName) {
  console.log(`\nVerifying function: ${functionName}`);
  
  // First check if we can access it via REST API
  const { exists, accessible, error } = await checkFunctionExposure(functionName);
  
  if (accessible) {
    console.log(`✅ Function ${functionName} is accessible via REST API`);
    return true;
  }
  
  console.log(`❌ Function ${functionName} issue: ${error}`);
  
  // Check if function actually exists in database
  const existsInDb = await checkFunctionExistenceInDb(functionName);
  
  if (existsInDb) {
    console.log(`⚠️ Function ${functionName} exists in the database but is not exposed via REST API`);
    
    // Show the SQL that would fix it
    console.log('\nFix SQL:');
    console.log(generateFixSql(functionName, true));
  } else {
    console.log(`⚠️ Function ${functionName} might not exist in the database`);
    
    // Show a more conservative SQL that first checks if the function exists
    console.log('\nFix SQL (with existence check):');
    console.log(generateFixSql(functionName, false));
  }
  
  return false;
}

/**
 * Fixes a single function
 */
async function fixFunction(functionName) {
  console.log(`\nFixing function: ${functionName}`);
  
  // Check if function already works
  const { accessible, error } = await checkFunctionExposure(functionName);
  
  if (accessible) {
    console.log(`✅ Function ${functionName} is already accessible, no fix needed`);
    return true;
  }
  
  // Check if function exists in DB
  const existsInDb = await checkFunctionExistenceInDb(functionName);
  
  // Generate SQL to fix the function
  const fixSql = generateFixSql(functionName, existsInDb);
  
  // Generate a temporary SQL file name
  const sqlFileName = path.join(process.cwd(), `temp-fix-${functionName}.sql`);
  
  try {
    // Write the SQL to a file
    fs.writeFileSync(sqlFileName, fixSql);
    console.log(`Generated SQL file: ${sqlFileName}`);
    
    // Execute the SQL - requires having supabase CLI installed
    // If no supabase CLI, you'll need another way to execute this SQL
    try {
      console.log('Attempting to apply fix...');
      execSync(`supabase db execute --file ${sqlFileName}`, { stdio: 'inherit' });
      console.log(`✅ Fix applied for function: ${functionName}`);
      
      // Verify the fix worked
      const { accessible: nowAccessible } = await checkFunctionExposure(functionName);
      
      if (nowAccessible) {
        console.log(`✅ Verification successful: ${functionName} is now accessible`);
        return true;
      } else {
        console.log(`❌ Fix did not resolve the issue for ${functionName}`);
        return false;
      }
    } catch (execErr) {
      console.error(`Error applying fix: ${execErr.message}`);
      return false;
    }
  } catch (fileErr) {
    console.error(`Error writing SQL file: ${fileErr.message}`);
    return false;
  } finally {
    // Clean up the temporary SQL file
    if (fs.existsSync(sqlFileName)) {
      fs.unlinkSync(sqlFileName);
    }
  }
}

/**
 * Main function to run verification or fixes
 */
async function main() {
  console.log('RPC Function Exposure Tool');
  console.log('=========================');
  
  if (allFunctions) {
    console.log(`Running in ${verifyMode ? 'VERIFY' : 'FIX'} mode for ALL critical functions`);
    
    // Process all critical functions
    let allSuccess = true;
    for (const func of CriticalFunctions) {
      let success = false;
      if (verifyMode) {
        success = await verifyFunction(func);
      } else if (fixMode) {
        success = await fixFunction(func);
      }
      
      allSuccess = allSuccess && success;
    }
    
    console.log('\nSummary:');
    console.log(`${allSuccess ? '✅ All' : '❌ Not all'} functions ${verifyMode ? 'verified' : 'fixed'} successfully`);
    
  } else if (specificFunction) {
    console.log(`Running in ${verifyMode ? 'VERIFY' : 'FIX'} mode for function: ${specificFunction}`);
    
    let success = false;
    if (verifyMode) {
      success = await verifyFunction(specificFunction);
    } else if (fixMode) {
      success = await fixFunction(specificFunction);
    }
    
    console.log('\nSummary:');
    console.log(`${success ? '✅ Function' : '❌ Function'} ${specificFunction} ${verifyMode ? 'verified' : 'fixed'} ${success ? 'successfully' : 'with issues'}`);
  }
}

// Run the main function
main()
  .catch(err => {
    console.error(`Error in main function: ${err.message}`);
    process.exit(1);
  });
