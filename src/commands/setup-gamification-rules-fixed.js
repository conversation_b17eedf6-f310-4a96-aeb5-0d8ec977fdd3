import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

// Initialize dotenv
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Supabase URL or Key is missing in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Set up gamification rules that map events to achievements and points
 */
async function setupGamificationRules() {
  console.log("Setting up gamification rules...");
  
  try {
    // Check connection
    await checkConnection();
    
    // Define the rules for achievements and points
    const rules = [
      {
        name: "First Product View",
        name_ar: "أول مشاهدة للمنتج",
        description: "Rule that grants an achievement for viewing the first product",
        description_ar: "قاعدة تمنح إنجازًا لمشاهدة المنتج الأول",
        conditions: {
          event_type: "product_viewed",
          count: 1
        },
        actions: [
          {
            type: "grant_achievement",
            achievement_id: "first_product_view"
          },
          {
            type: "add_points",
            points: 10,
            reason: "Viewed first product"
          }
        ],
        category: "products",
        is_active: true,
        priority: 100
      },
      {
        name: "Explorer",
        name_ar: "مستكشف",
        description: "Rule that grants an achievement for viewing 5 different products",
        description_ar: "قاعدة تمنح إنجازًا لمشاهدة 5 منتجات مختلفة",
        conditions: {
          event_type: "product_viewed",
          count: 5,
          unique_property: "product_id"
        },
        actions: [
          {
            type: "grant_achievement",
            achievement_id: "explorer"
          },
          {
            type: "add_points",
            points: 25,
            reason: "Explored 5 different products"
          }
        ],
        category: "products",
        is_active: true,
        priority: 90
      },
      {
        name: "Product Master",
        name_ar: "سيد المنتجات",
        description: "Rule that grants an achievement for viewing 20 different products",
        description_ar: "قاعدة تمنح إنجازًا لمشاهدة 20 منتجًا مختلفًا",
        conditions: {
          event_type: "product_viewed",
          count: 20,
          unique_property: "product_id"
        },
        actions: [
          {
            type: "grant_achievement",
            achievement_id: "product_master"
          },
          {
            type: "add_points",
            points: 100,
            reason: "Mastered products by viewing 20 different ones"
          }
        ],
        category: "products",
        is_active: true,
        priority: 80
      },
      {
        name: "Creator",
        name_ar: "منشئ",
        description: "Rule that grants an achievement for creating the first product",
        description_ar: "قاعدة تمنح إنجازًا لإنشاء المنتج الأول",
        conditions: {
          event_type: "product_created",
          count: 1
        },
        actions: [
          {
            type: "grant_achievement",
            achievement_id: "creator"
          },
          {
            type: "add_points",
            points: 50,
            reason: "Created first product"
          }
        ],
        category: "products",
        is_active: true,
        priority: 100
      },
      {
        name: "Profile Completer",
        name_ar: "مكمل الملف الشخصي",
        description: "Rule that grants achievement for completing profile",
        description_ar: "قاعدة تمنح إنجازًا لإكمال الملف الشخصي",
        conditions: {
          event_type: "profile_updated",
          data: {
            completed_profile: true
          },
          count: 1
        },
        actions: [
          {
            type: "grant_achievement",
            achievement_id: "profile_completer"
          },
          {
            type: "add_points",
            points: 20,
            reason: "Completed profile information"
          }
        ],
        category: "profile",
        is_active: true,
        priority: 100
      },
      {
        name: "First Login",
        name_ar: "أول تسجيل دخول",
        description: "Rule that grants achievement for first login",
        description_ar: "قاعدة تمنح إنجازًا لأول تسجيل دخول",
        conditions: {
          event_type: "user_login",
          count: 1
        },
        actions: [
          {
            type: "grant_achievement",
            achievement_id: "welcome_aboard"
          },
          {
            type: "add_points",
            points: 5,
            reason: "First login"
          }
        ],
        category: "system",
        is_active: true,
        priority: 100
      }
    ];
    
    // First, check if the gamification_rules table exists
    const { error: checkError } = await supabase.from('gamification_rules').select('id').limit(1);
    
    if (checkError && checkError.message.includes('relation "gamification_rules" does not exist')) {
      console.error('Error: gamification_rules table does not exist. Run the setup tables script first.');
      return false;
    }
    
    // Insert the rules
    console.log(`Inserting ${rules.length} gamification rules...`);
    for (const rule of rules) {
      const { data, error } = await supabase
        .from('gamification_rules')
        .upsert(rule, {
          onConflict: 'name',
          ignoreDuplicates: false
        });
        
      if (error) {
        console.error(`Error inserting rule "${rule.name}":`, error.message);
      } else {
        console.log(`Rule "${rule.name}" inserted or updated successfully.`);
      }
    }
    
    console.log("Gamification rules setup completed.");
    return true;
  } catch (error) {
    console.error("Error setting up gamification rules:", error);
    return false;
  }
}

/**
 * Check database connection
 */
async function checkConnection() {
  try {
    const { data, error } = await supabase.from('gamification_rules').select('id').limit(1);
    
    if (error && !error.message.includes('relation "gamification_rules" does not exist')) {
      console.warn('Warning: Connection issue. Check your Supabase credentials.');
      console.warn(error.message);
    } else {
      console.log('Supabase connection successful');
    }
  } catch (err) {
    console.warn('Warning: Connection test failed. Check your Supabase credentials.');
    console.warn(err.message);
  }
}

// Run the setup if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  setupGamificationRules()
    .then(success => {
      if (success) {
        console.log("✅ Gamification rules setup completed successfully.");
        process.exit(0);
      } else {
        console.error("❌ Gamification rules setup failed.");
        process.exit(1);
      }
    })
    .catch(err => {
      console.error("Fatal error:", err);
      process.exit(1);
    });
}

export { setupGamificationRules };
