// <PERSON>ript to check the status of a dependency in the database
// Usage: node src/commands/check-dependency-status.js [dependency_id]

import { createClient } from '@supabase/supabase-js';

// Define Supabase client directly to avoid TypeScript import issues
// Get Supabase connection info from environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_KEY;

// Ensure environment variables are set
if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Missing Supabase environment variables.');
  console.error('Make sure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in your environment.');
  console.error('Create a .env file based on .env.example or set these variables in your environment.');
  process.exit(1);
}
const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

const checkDependencyStatus = async (dependencyId) => {
  console.log(`=== Dependency Status Check ===`);
  
  try {
    // If no dependency ID is provided, check all dependencies
    if (!dependencyId) {
      console.log("\nChecking all dependencies...");
      
      // Get all dependencies
      const { data: allDependencies, error: allError } = await supabase
        .from('product_iteration_dependencies')
        .select(`
          id, 
          title,
          details,
          dependency_type,
          approval_status,
          created_at,
          updated_at,
          product_iteration_id,
          dependent_on_product_id,
          product_iterations:product_iteration_id (
            id, 
            name,
            product_owner_id, 
            product_manager_id
          ),
          dependent_products:dependent_on_product_id (
            id,
            name
          )
        `)
        .order('created_at', { ascending: false })
        .limit(10);
        
      if (allError) {
        console.error('Error fetching dependencies:', allError);
        return;
      }
      
      if (!allDependencies || allDependencies.length === 0) {
        console.log('No dependencies found in the database.');
        return;
      }
      
      console.log(`Found ${allDependencies.length} dependencies:`);
      
      allDependencies.forEach(dep => {
        const productIteration = dep.product_iterations;
        const dependentProduct = dep.dependent_products;
        
        console.log(`\n- ID: ${dep.id}`);
        console.log(`  Title: ${dep.title || 'No title'}`);
        console.log(`  Type: ${dep.dependency_type}`);
        console.log(`  Status: ${dep.approval_status}`);
        console.log(`  Created: ${new Date(dep.created_at).toLocaleString()}`);
        console.log(`  Updated: ${dep.updated_at ? new Date(dep.updated_at).toLocaleString() : 'Never'}`);
        console.log(`  Product Iteration: ${productIteration ? productIteration.name || productIteration.id : 'Unknown'}`);
        console.log(`  Dependent Product: ${dependentProduct ? dependentProduct.name || dependentProduct.id : 'Unknown'}`);
        console.log(`  Product Owner ID: ${productIteration ? productIteration.product_owner_id || 'Not set' : 'Unknown'}`);
        console.log(`  Product Manager ID: ${productIteration ? productIteration.product_manager_id || 'Not set' : 'Unknown'}`);
      });
      
      return;
    }
    
    // Check specific dependency
    console.log(`\nChecking dependency with ID: ${dependencyId}`);
    
    // Step 1: Check if the dependency exists
    const { data: dependency, error } = await supabase
      .from('product_iteration_dependencies')
      .select(`
        id, 
        title,
        details,
        dependency_type,
        approval_status,
        created_at,
        updated_at,
        product_iteration_id,
        dependent_on_product_id,
        product_iterations:product_iteration_id (
          id, 
          name,
          product_owner_id, 
          product_manager_id
        ),
        dependent_products:dependent_on_product_id (
          id,
          name
        )
      `)
      .eq('id', dependencyId)
      .maybeSingle();
      
    if (error) {
      console.error('Error fetching dependency:', error);
      return;
    }
    
    if (!dependency) {
      console.log(`No dependency found with ID: ${dependencyId}`);
      return;
    }
    
    const productIteration = dependency.product_iterations;
    const dependentProduct = dependency.dependent_products;
    
    console.log('\nDependency details:');
    console.log(`- ID: ${dependency.id}`);
    console.log(`- Title: ${dependency.title || 'No title'}`);
    console.log(`- Type: ${dependency.dependency_type}`);
    console.log(`- Status: ${dependency.approval_status}`);
    console.log(`- Created: ${new Date(dependency.created_at).toLocaleString()}`);
    console.log(`- Updated: ${dependency.updated_at ? new Date(dependency.updated_at).toLocaleString() : 'Never'}`);
    console.log(`- Product Iteration: ${productIteration ? productIteration.name || productIteration.id : 'Unknown'}`);
    console.log(`- Dependent Product: ${dependentProduct ? dependentProduct.name || dependentProduct.id : 'Unknown'}`);
    console.log(`- Product Owner ID: ${productIteration ? productIteration.product_owner_id || 'Not set' : 'Unknown'}`);
    console.log(`- Product Manager ID: ${productIteration ? productIteration.product_manager_id || 'Not set' : 'Unknown'}`);
    
    // Step 2: Check approval history
    console.log('\nChecking approval history...');
    
    const { data: history, error: historyError } = await supabase
      .from('approval_stage_history')
      .select(`
        id,
        action,
        status,
        comments,
        created_at,
        actor_id,
        actors:actor_id (id, name, email)
      `)
      .eq('approval_stage_id', dependency.id)
      .order('created_at', { ascending: false });
      
    if (historyError) {
      console.error('Error fetching approval history:', historyError);
    } else if (!history || history.length === 0) {
      console.log('No approval history found for this dependency.');
    } else {
      console.log(`Found ${history.length} approval history entries:`);
      
      history.forEach(entry => {
        const actor = entry.actors;
        
        console.log(`\n- Action: ${entry.action}`);
        console.log(`  Status: ${entry.status}`);
        console.log(`  Date: ${new Date(entry.created_at).toLocaleString()}`);
        console.log(`  Actor: ${actor ? actor.name || actor.email || actor.id : 'Unknown'}`);
        
        if (entry.comments) {
          console.log(`  Comments: ${entry.comments}`);
        }
      });
    }
    
  } catch (error) {
    console.error('Error checking dependency status:', error);
  }
};

// Get dependency ID from command line arguments
const dependencyId = process.argv[2];

// Run the check
checkDependencyStatus(dependencyId).catch(console.error);
