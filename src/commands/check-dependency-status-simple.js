// Simplified script to check the status of dependencies in the database
// Usage: node src/commands/check-dependency-status-simple.js

import { createClient } from '@supabase/supabase-js';

// Define Supabase client
const SUPABASE_URL = "https://wgsnpiskyczxhojlrwtr.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.qjE4Z2CwDpXW4C5VZ5tHrhLQV2uK6ut8Mnrgr0snhwc";

const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

async function checkDependencies() {
  console.log("=== Checking Dependencies ===");
  
  try {
    // Get all dependencies
    const { data, error } = await supabase
      .from('product_iteration_dependencies')
      .select('*')
      .order('created_at', { ascending: false });
      
    if (error) {
      console.error("Error fetching dependencies:", error);
      return;
    }
    
    if (!data || data.length === 0) {
      console.log("No dependencies found in the database.");
      return;
    }
    
    console.log(`Found ${data.length} dependencies:`);
    
    // Print basic info about each dependency
    data.forEach((dep, index) => {
      console.log(`\n${index + 1}. ID: ${dep.id}`);
      console.log(`   Title: ${dep.title || 'No title'}`);
      console.log(`   Type: ${dep.dependency_type || 'Unknown'}`);
      console.log(`   Status: ${dep.approval_status || 'Unknown'}`);
      console.log(`   Created: ${new Date(dep.created_at).toLocaleString()}`);
      console.log(`   Product Iteration ID: ${dep.product_iteration_id || 'Not set'}`);
      console.log(`   Dependent On Product ID: ${dep.dependent_on_product_id || 'Not set'}`);
    });
    
    // Check for the specific dependency ID from the screenshot
    const specificId = "246d537f-e9c5-4c81-a10a-011e41d45d52";
    const specificDep = data.find(dep => dep.id === specificId);
    
    if (specificDep) {
      console.log(`\n=== Found the specific dependency from the screenshot ===`);
      console.log(`ID: ${specificDep.id}`);
      console.log(`Title: ${specificDep.title || 'No title'}`);
      console.log(`Type: ${specificDep.dependency_type || 'Unknown'}`);
      console.log(`Status: ${specificDep.approval_status || 'Unknown'}`);
      console.log(`Created: ${new Date(specificDep.created_at).toLocaleString()}`);
      
      // If it's not pending, that explains why it's not showing up
      if (specificDep.approval_status !== 'pending') {
        console.log(`\nThis dependency is not showing up in the dashboard because its status is '${specificDep.approval_status}' instead of 'pending'.`);
      }
    } else {
      console.log(`\nThe specific dependency from the screenshot (ID: ${specificId}) was not found in the database.`);
      console.log("It may have been deleted or the ID is incorrect.");
    }
    
  } catch (error) {
    console.error("Error:", error);
  }
}

checkDependencies();
