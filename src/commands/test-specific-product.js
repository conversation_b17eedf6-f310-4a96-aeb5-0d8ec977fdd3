// <PERSON><PERSON>t to test the specific product that was causing the issue
// Usage: node src/commands/test-specific-product.js

import { createClient } from '@supabase/supabase-js';

// Define Supabase client directly to avoid TypeScript import issues
// Get Supabase connection info from environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_KEY;

// Ensure environment variables are set
if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Missing Supabase environment variables.');
  console.error('Make sure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in your environment.');
  console.error('Create a .env file based on .env.example or set these variables in your environment.');
  process.exit(1);
}
const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

// The specific product ID and iteration that was causing the issue
const PRODUCT_ID = 'bbe88976-5283-4752-b8f0-b307fcaf4fb7';
const ITERATION = '2.0';

// Function to check if the product exists
const checkProductExists = async (productId) => {
  console.log(`Checking if product ${productId} exists...`);
  
  try {
    const { data, error } = await supabase
      .from('product_bases')
      .select('id, name')
      .eq('id', productId)
      .single();
      
    if (error) {
      console.error(`Error checking product existence: ${error.message}`);
      if (error.code === 'PGRST116') {
        console.error(`Product with ID ${productId} does not exist in the database`);
        return false;
      }
      throw error;
    }
    
    if (!data) {
      console.error(`Product with ID ${productId} not found`);
      return false;
    }
    
    console.log(`Product exists: ${data.name} (${data.id})`);
    return true;
  } catch (err) {
    console.error(`Error checking product: ${err.message}`);
    return false;
  }
};

// Function to check if the iteration exists for the product
const checkIterationExists = async (productId, iterationVersion) => {
  console.log(`Checking if iteration ${iterationVersion} exists for product ${productId}...`);
  
  try {
    const { data, error } = await supabase
      .from('product_iterations')
      .select('id, version, iteration')
      .eq('product_base_id', productId)
      .or(`version.eq.${iterationVersion},iteration.eq.${iterationVersion}`);
      
    if (error) {
      console.error(`Error checking iteration existence: ${error.message}`);
      throw error;
    }
    
    if (!data || data.length === 0) {
      console.error(`Iteration ${iterationVersion} not found for product ${productId}`);
      return false;
    }
    
    console.log(`Iteration exists: ${data[0].version || data[0].iteration} (${data[0].id})`);
    return true;
  } catch (err) {
    console.error(`Error checking iteration: ${err.message}`);
    return false;
  }
};

// Function to get all iterations for a product
const getProductIterations = async (productId) => {
  console.log(`Getting all iterations for product ${productId}...`);
  
  try {
    const { data, error } = await supabase
      .from('product_iterations')
      .select('id, version, iteration, created_at, updated_at')
      .eq('product_base_id', productId)
      .order('created_at', { ascending: false });
      
    if (error) {
      console.error(`Error getting iterations: ${error.message}`);
      throw error;
    }
    
    if (!data || data.length === 0) {
      console.log(`No iterations found for product ${productId}`);
      return [];
    }
    
    console.log(`Found ${data.length} iterations for product ${productId}`);
    return data;
  } catch (err) {
    console.error(`Error getting iterations: ${err.message}`);
    return [];
  }
};

// Main function
const main = async () => {
  try {
    console.log('Testing specific product that was causing the issue...');
    console.log(`Product ID: ${PRODUCT_ID}`);
    console.log(`Iteration: ${ITERATION}`);
    
    // Check if the product exists
    const productExists = await checkProductExists(PRODUCT_ID);
    
    if (!productExists) {
      console.error('Product does not exist!');
      return;
    }
    
    // Check if the iteration exists
    const iterationExists = await checkIterationExists(PRODUCT_ID, ITERATION);
    
    if (!iterationExists) {
      console.error('Iteration does not exist!');
      
      // Get all iterations for the product
      const iterations = await getProductIterations(PRODUCT_ID);
      
      if (iterations.length > 0) {
        console.log('\nAvailable iterations:');
        iterations.forEach(iteration => {
          console.log(`- ${iteration.version || iteration.iteration} (${iteration.id})`);
        });
      }
      
      return;
    }
    
    console.log('\nProduct and iteration exist in the database.');
    console.log('The issue is likely with how the application is handling the URL parameters or the routing.');
    
  } catch (error) {
    console.error('Error in main:', error);
  }
};

// Run the main function
main();
