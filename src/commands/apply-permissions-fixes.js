/**
 * <PERSON><PERSON><PERSON> to apply critical performance fixes to the permissions system
 * 
 * This script addresses performance issues related to:
 * 1. Deprecated usePermissionsContext warnings flooding the console
 * 2. Infinite loops in permission checks
 * 3. Missing error handling in usePermissions hooks
 * 
 * Usage: 
 * node src/commands/apply-permissions-fixes.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get current file directory (equivalent to __dirname in CommonJS)
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m',
};

console.log(`${colors.magenta}🛠  Preparing to apply permission system performance fixes...${colors.reset}\n`);

// Path to the permissions hook file
const permissionsHookPath = path.join(process.cwd(), 'src', 'hooks', 'usePermissions.tsx');

// Check if the permissions hook file exists
if (!fs.existsSync(permissionsHookPath)) {
  console.error(`${colors.red}❌ Permissions hook file not found: ${permissionsHookPath}${colors.reset}`);
  console.error(`${colors.yellow}Please make sure you're running this script from the project root directory.${colors.reset}`);
  process.exit(1);
}

try {
  // Read the permissions hook file
  let permissionsHookContent = fs.readFileSync(permissionsHookPath, 'utf8');
  let modified = false;
  
  // Check for existing modifications
  if (permissionsHookContent.includes('// Warning control to prevent console spam') &&
      permissionsHookContent.includes('let hasWarnedDeprecation = false;')) {
    console.log(`${colors.yellow}⚠️  Warning limiter for usePermissionsContext already applied${colors.reset}`);
  } else {
    console.log(`${colors.blue}🔄 Adding warning limiter to usePermissionsContext...${colors.reset}`);
    
    // Replace the deprecated hook implementation with one that limits warnings
    const oldHookImplementation = `export function usePermissionsContext() {
  console.warn('usePermissionsContext is deprecated. Please use usePermissions instead.');
  return useContext(PermissionsContext);
}`;

    const newHookImplementation = `// Warning control to prevent console spam
let hasWarnedDeprecation = false;

/**
 * Legacy compatibility hook - Aliases to usePermissions to support existing code
 * Includes a circuit-breaker to prevent infinite loops and console spam
 */
export function usePermissionsContext() {
  // Only show warning once per session to prevent console spam
  if (!hasWarnedDeprecation) {
    console.warn('usePermissionsContext is deprecated. Please use usePermissions instead.');
    hasWarnedDeprecation = true;
  }
  
  // Use the same context as usePermissions to prevent creating multiple contexts
  return useContext(PermissionsContext);
}`;
    
    permissionsHookContent = permissionsHookContent.replace(oldHookImplementation, newHookImplementation);
    modified = true;
  }

  // Check for circuit breaker in useEffect
  if (permissionsHookContent.includes('[loadAttempted]') &&
      permissionsHookContent.includes('const [loadAttempted, setLoadAttempted] = useState(false);')) {
    console.log(`${colors.yellow}⚠️  Circuit breaker for loadCapabilities already applied${colors.reset}`);
  } else {
    console.log(`${colors.blue}🔄 Adding circuit breaker to loadCapabilities...${colors.reset}`);
    
    // Add control variable declaration
    if (!permissionsHookContent.includes('const [loadAttempted, setLoadAttempted] = useState(false);')) {
      const loadingState = `const [loading, setLoading] = useState(true);`;
      const loadAttemptedState = `const [loading, setLoading] = useState(true);

  // Control variable to prevent excessive loading
  const [loadAttempted, setLoadAttempted] = useState(false);`;
      
      permissionsHookContent = permissionsHookContent.replace(loadingState, loadAttemptedState);
    }
    
    // Replace useEffect implementation
    const oldUseEffect = `// Load capabilities on mount
  useEffect(() => {
    loadCapabilities();
  }, []);`;
    
    const newUseEffect = `// Load capabilities on mount with circuit breaker
  useEffect(() => {
    // Only attempt to load once to prevent infinite loops
    if (!loadAttempted) {
      loadCapabilities();
      setLoadAttempted(true);
    }
  }, [loadAttempted]);`;
    
    permissionsHookContent = permissionsHookContent.replace(oldUseEffect, newUseEffect);
    modified = true;
  }
  
  // Check for error handling in hasCapability
  if (permissionsHookContent.includes('const [errorCount, setErrorCount] = useState(0);') &&
      permissionsHookContent.includes('const MAX_ERRORS = 3;')) {
    console.log(`${colors.yellow}⚠️  Error handling for capability checks already applied${colors.reset}`);
  } else {
    console.log(`${colors.blue}🔄 Adding error handling and circuit breaker to capability checks...${colors.reset}`);
    
    // Add error tracking variables
    const addErrorTrackingBefore = `// Function to load capabilities`;
    const errorTrackingVars = `// Error tracking for capabilities check
  const [errorCount, setErrorCount] = useState(0);
  const MAX_ERRORS = 3; // Maximum number of consecutive errors before failing safe

  // Function to load capabilities`;
    
    permissionsHookContent = permissionsHookContent.replace(addErrorTrackingBefore, errorTrackingVars);
    
    // Update hasCapability function with error handling
    const oldHasCapability = `// Check if a user has a capability asynchronously (server check)
  const hasCapability = async (
    capabilityCode: string,
    contextType?: string,
    entityId?: string
  ): Promise<boolean> => {
    // Convert string context type to enum if provided
    const typedContextType = contextType as ContextType | undefined;
    return dynamicPermissionsService.hasCapability(capabilityCode, typedContextType, entityId);
  };`;
    
    const newHasCapability = `// Check if a user has a capability asynchronously (server check)
  const hasCapability = async (
    capabilityCode: string,
    contextType?: string,
    entityId?: string
  ): Promise<boolean> => {
    // Circuit breaker: if we've had too many errors, fail safe (return true)
    // This prevents infinite loops caused by failed capability checks
    if (errorCount >= MAX_ERRORS) {
      console.warn(\`[Permissions] Circuit breaker activated after \${MAX_ERRORS} errors. Allowing capability: \${capabilityCode}\`);
      return true;
    }

    try {
      // Convert string context type to enum if provided
      const typedContextType = contextType as ContextType | undefined;
      const result = await dynamicPermissionsService.hasCapability(capabilityCode, typedContextType, entityId);
      
      // Reset error count on success
      if (errorCount > 0) {
        setErrorCount(0);
      }
      
      return result;
    } catch (error) {
      // Increment error counter when capability check fails
      setErrorCount(prev => prev + 1);
      console.error(\`[Permissions] Error checking capability (\${errorCount+1}/\${MAX_ERRORS}):\`, error);
      
      // Fail safe if we reach error threshold
      return errorCount + 1 >= MAX_ERRORS;
    }
  };`;
    
    permissionsHookContent = permissionsHookContent.replace(oldHasCapability, newHasCapability);
    
    // Update hasCapabilitySync function with error handling
    const oldHasCapabilitySync = `// Sync check using cached capabilities (no server call)
  const hasCapabilitySync = (
    capabilityCode: string,
    contextType?: string,
    entityId?: string
  ): boolean => {
    // First check for an exact match including context
    const exactMatch = capabilities.some(cap => 
      cap.code === capabilityCode && 
      cap.context_type === contextType &&
      (entityId ? cap.entity_id === entityId : true)
    );
    
    if (exactMatch) return true;

    // Then check for global capability (no context)
    const globalMatch = capabilities.some(cap => 
      cap.code === capabilityCode && 
      cap.context_type === null
    );

    return globalMatch;
  };`;
    
    const newHasCapabilitySync = `// Sync check using cached capabilities (no server call) with safeguards
  const hasCapabilitySync = (
    capabilityCode: string,
    contextType?: string,
    entityId?: string
  ): boolean => {
    try {
      // Fail safe when we've hit too many errors
      if (errorCount >= MAX_ERRORS) {
        return true;
      }
      
      // Safety check for capabilities array
      if (!Array.isArray(capabilities)) {
        console.warn('[Permissions] Capabilities is not an array in hasCapabilitySync');
        return false;
      }
      
      // First check for an exact match including context
      const exactMatch = capabilities.some(cap => 
        cap && cap.code === capabilityCode && 
        cap.context_type === contextType &&
        (entityId ? cap.entity_id === entityId : true)
      );
      
      if (exactMatch) return true;

      // Then check for global capability (no context)
      const globalMatch = capabilities.some(cap => 
        cap && cap.code === capabilityCode && 
        cap.context_type === null
      );

      return globalMatch;
    } catch (error) {
      // If anything goes wrong, increment error counter
      setErrorCount(prev => prev + 1);
      console.error(\`[Permissions] Error in hasCapabilitySync (\${errorCount+1}/\${MAX_ERRORS}):\`, error);
      
      // Fail safe if we reach error threshold
      return errorCount + 1 >= MAX_ERRORS;
    }
  };`;
    
    permissionsHookContent = permissionsHookContent.replace(oldHasCapabilitySync, newHasCapabilitySync);
    modified = true;
  }
  
  // Check for error handling in CapabilityGate
  if (permissionsHookContent.includes('const [hasError, setHasError] = useState(false);') &&
      permissionsHookContent.includes('// If there\'s an error, render children by default (fail open)')) {
    console.log(`${colors.yellow}⚠️  Error handling for CapabilityGate already applied${colors.reset}`);
  } else {
    console.log(`${colors.blue}🔄 Adding error handling to CapabilityGate component...${colors.reset}`);
    
    const oldCapabilityGate = `/**
 * Component that conditionally renders children based on capability
 */
export function CapabilityGate({
  capability,
  contextType,
  entityId,
  fallback = null,
  children
}: {
  capability: string;
  contextType?: string;
  entityId?: string;
  fallback?: ReactNode;
  children: ReactNode;
}) {
  const { hasCapabilitySync, loading } = usePermissions();
  
  // When loading, show nothing
  if (loading) return null;
  
  // Check if the user has the required capability
  const hasPermission = hasCapabilitySync(capability, contextType, entityId);
  
  // Render children if they have the capability, otherwise render fallback
  return hasPermission ? <>{children}</> : <>{fallback}</>;
}`;
    
    const newCapabilityGate = `/**
 * Component that conditionally renders children based on capability
 * Now with error handling to prevent infinite render loops
 */
export function CapabilityGate({
  capability,
  contextType,
  entityId,
  fallback = null,
  children
}: {
  capability: string;
  contextType?: string;
  entityId?: string;
  fallback?: ReactNode;
  children: ReactNode;
}) {
  const { hasCapabilitySync, loading } = usePermissions();
  const [hasError, setHasError] = useState(false);
  
  // When loading and no errors, show nothing
  if (loading && !hasError) return null;
  
  try {
    // Check if the user has the required capability
    const hasPermission = hasCapabilitySync(capability, contextType, entityId);
    
    // Render children if they have the capability, otherwise render fallback
    return hasPermission ? <>{children}</> : <>{fallback}</>;
  } catch (error) {
    // Log the error and set error state
    console.error(\`[CapabilityGate] Error checking capability \${capability}:\`, error);
    setHasError(true);
    
    // If there's an error, render children by default (fail open)
    // This prevents blocking UI due to permission errors
    return <>{children}</>;
  }
}`;
    
    permissionsHookContent = permissionsHookContent.replace(oldCapabilityGate, newCapabilityGate);
    modified = true;
  }
  
  // Save changes if modified
  if (modified) {
    fs.writeFileSync(permissionsHookPath, permissionsHookContent);
    console.log(`${colors.green}✅ Successfully updated usePermissions.tsx with performance fixes${colors.reset}`);
  } else {
    console.log(`${colors.cyan}ℹ️ No changes needed - all performance fixes already applied${colors.reset}`);
  }
  
  // Create documentation for the fixes
  const docsPath = path.join(process.cwd(), 'docs', 'PERMISSIONS-PERFORMANCE-FIXES.md');
  if (!fs.existsSync(docsPath)) {
    console.log(`${colors.blue}🔄 Creating documentation for permissions performance fixes...${colors.reset}`);
    
    const docsContent = `# Permissions System Performance Fixes

## Overview

This document describes the performance fixes applied to the permissions system to address issues with:

1. Console spam from deprecated warning messages
2. Infinite loops in permission checks
3. Error handling and circuit breakers in permission hooks

## Issues Addressed

### 1. Deprecated Hook Warnings

The \`usePermissionsContext\` hook was marked as deprecated but is still widely used throughout the codebase.
Each usage was generating a console warning, resulting in thousands of warning messages that impacted performance.

**Fix Applied:**
- Added a warning limiter to show the deprecation warning only once per session
- Maintained backward compatibility for existing code

### 2. Infinite Loading Loops

The permission system could enter infinite loops when trying to load capabilities repeatedly on errors.

**Fix Applied:**
- Added a circuit breaker to prevent reloading capabilities multiple times
- Implemented a load attempt tracking system to ensure loading only happens once

### 3. Error Handling in Capability Checks

Permission checks could fail and cause cascading errors without proper handling.

**Fix Applied:**
- Added comprehensive error tracking and handling
- Implemented circuit breakers that "fail open" after multiple errors
- Added safety checks for capabilities array access

### 4. CapabilityGate Component Improvements

The \`CapabilityGate\` component could cause rendering failures when permission checks encountered errors.

**Fix Applied:**
- Added try/catch blocks to handle errors safely
- Implemented a fail-open approach to prevent UI blocking
- Added error state tracking

## Implementation Details

The fixes were implemented in the \`src/hooks/usePermissions.tsx\` file with the following key changes:

1. Added a session-wide warning limiter for deprecated hook usage
2. Implemented load attempt tracking to prevent infinite loops
3. Added error counting and circuit breakers for capability checks
4. Enhanced the CapabilityGate component with error handling

## Testing

To verify these fixes:

1. Check the browser console for reduced warning messages
2. Monitor performance with React DevTools to ensure no render loops
3. Verify that the UI continues to function even when permission checks fail

## Next Steps

While these fixes address the immediate performance issues, a more comprehensive solution would be to:

1. Migrate all usage of \`usePermissionsContext\` to \`usePermissions\`
2. Implement a more robust permission caching system
3. Create an automated migration tool for existing components

This will require a broader refactoring effort across the codebase.
`;
    
    fs.writeFileSync(docsPath, docsContent);
    console.log(`${colors.green}✅ Created documentation: docs/PERMISSIONS-PERFORMANCE-FIXES.md${colors.reset}`);
  } else {
    console.log(`${colors.yellow}⚠️  Documentation file already exists: docs/PERMISSIONS-PERFORMANCE-FIXES.md${colors.reset}`);
  }
  
  console.log(`${colors.green}\n✅ Successfully applied permission system performance fixes!${colors.reset}`);
  console.log(`${colors.cyan}ℹ️  The following changes were made:${colors.reset}`);
  console.log(`   - Added warning limiter to usePermissionsContext`);
  console.log(`   - Added circuit breaker to prevent infinite capability loading`);
  console.log(`   - Added error handling and safety checks for capability checks`);
  console.log(`   - Enhanced CapabilityGate component with error handling\n`);
  
  console.log(`${colors.blue}📊 Next steps:${colors.reset}`);
  console.log(`   1. Rebuild your application`);
  console.log(`   2. Test system performance with the fixes applied`);
  console.log(`   3. Consider migrating all components to the new usePermissions hook\n`);
  
} catch (error) {
  console.error(`${colors.red}❌ Failed to apply permissions fixes:${colors.reset}`, error);
  process.exit(1);
}
