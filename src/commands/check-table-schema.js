// <PERSON>ript to check the schema of a table
// Usage: node src/commands/check-table-schema.js [table_name]

import { createClient } from '@supabase/supabase-js';

// Define Supabase client
// Get Supabase connection info from environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_KEY;

// Ensure environment variables are set
if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Missing Supabase environment variables.');
  console.error('Make sure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in your environment.');
  console.error('Create a .env file based on .env.example or set these variables in your environment.');
  process.exit(1);
}
const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

async function checkTableSchema(tableName) {
  console.log(`=== Checking Schema for Table: ${tableName || 'product_iterations'} ===`);
  
  try {
    // If no table name is provided, use product_iterations
    if (!tableName) {
      tableName = 'product_iterations';
    }
    
    // Execute a simple query to get one row from the table
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .limit(1);
      
    if (error) {
      console.error(`Error fetching from ${tableName}:`, error);
      return;
    }
    
    if (!data || data.length === 0) {
      console.log(`No data found in table: ${tableName}`);
      return;
    }
    
    // Get the column names from the first row
    const columns = Object.keys(data[0]);
    
    console.log(`\nColumns in table ${tableName}:`);
    columns.forEach(column => {
      console.log(`- ${column}: ${typeof data[0][column]}`);
    });
    
    // Print a sample of the data
    console.log('\nSample data:');
    console.log(JSON.stringify(data[0], null, 2));
    
  } catch (error) {
    console.error("Error:", error);
  }
}

// Get table name from command line arguments
const tableName = process.argv[2];

// Run the check
checkTableSchema(tableName).catch(console.error);
