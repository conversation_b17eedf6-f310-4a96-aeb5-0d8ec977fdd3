/**
 * <PERSON><PERSON><PERSON> to suppress capability RPC function warnings
 * 
 * This script applies enhanced warning suppression for the
 * get_dynamic_user_capabilities 404 errors to clean up the console output.
 * 
 * Usage:
 * node src/commands/suppress-capability-warnings.js
 */

// Fix the import path issue
const { suppressRpcFunctionWarnings } = require('../integrations/supabase/reducedWarnings.js');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

/**
 * Apply warning suppression for capability RPC functions
 */
function suppressCapabilityWarnings() {
  console.log(`${colors.blue}Applying enhanced warning suppression for RPC functions...${colors.reset}`);
  
  try {
    // Suppress warnings for the primary function
    suppressRpcFunctionWarnings('get_dynamic_user_capabilities');
    console.log(`${colors.green}Successfully suppressed warnings for get_dynamic_user_capabilities${colors.reset}`);
    
    // Also suppress warnings for potential wrapper functions
    suppressRpcFunctionWarnings('get_user_caps');
    console.log(`${colors.green}Successfully suppressed warnings for get_user_caps${colors.reset}`);
    
    console.log(`\n${colors.cyan}All capability RPC function warnings will now be suppressed.${colors.reset}`);
    console.log(`${colors.cyan}The fallback mechanisms will continue to work silently.${colors.reset}`);
    
    // Add info about how to verify
    console.log(`\n${colors.yellow}To verify suppression:${colors.reset}`);
    console.log(`1. Navigate to any page that uses user capabilities`);
    console.log(`2. Open browser console and check that 404 errors are no longer displayed`);
    console.log(`3. The application should function normally with the fallbacks working in the background`);
    
  } catch (error) {
    console.error(`${colors.red}Error applying warning suppression:${colors.reset}`, error.message);
    process.exit(1);
  }
}

// Only execute if called directly (not imported)
if (require.main === module) {
  suppressCapabilityWarnings();
}

module.exports = { suppressCapabilityWarnings };
