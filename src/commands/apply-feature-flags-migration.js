// <PERSON>ript to apply the feature flags migration
// Run with: node src/commands/apply-feature-flags-migration.js

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read .env file to get environment variables
const envPath = path.resolve(__dirname, '../../.env');
let supabaseUrl = process.env.VITE_SUPABASE_URL;
let supabaseKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

// If environment variables are not set, try to read from .env file
if (!supabaseUrl || !supabaseKey) {
  try {
    if (fs.existsSync(envPath)) {
      const envContent = fs.readFileSync(envPath, 'utf8');
      const envVars = Object.fromEntries(
        envContent
          .split('\n')
          .filter(line => line.trim() !== '' && !line.startsWith('#'))
          .map(line => line.split('=').map(part => part.trim()))
      );
      
      supabaseUrl = supabaseUrl || envVars.VITE_SUPABASE_URL;
      supabaseKey = supabaseKey || envVars.VITE_SUPABASE_SERVICE_ROLE_KEY;
    }
  } catch (err) {
    console.error('Error reading .env file:', err);
  }
}

// Validate that we have the required environment variables
if (!supabaseUrl) {
  console.error('Error: VITE_SUPABASE_URL is not set. Please set it in your environment or .env file.');
  process.exit(1);
}

if (!supabaseKey) {
  console.error('Error: VITE_SUPABASE_SERVICE_ROLE_KEY is not set. Please set it in your environment or .env file.');
  process.exit(1);
}

// Initialize Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseKey);

async function applyMigration() {
  console.log('Starting feature flags migration...');
  
  try {
    // Read the SQL migration file
    const migrationPath = path.resolve(__dirname, '../../supabase/migrations/20250323_feature_flags.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Split the migration into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      const { error } = await supabase.rpc('execute_sql', { query: statement + ';' });
      
      if (error) {
        console.error(`Error executing statement ${i + 1}:`, error);
        // Continue with the next statement
      } else {
        console.log(`Statement ${i + 1} executed successfully`);
      }
    }
    
    console.log('Migration completed successfully!');
    
    // Verify that the feature_flags table exists and has data
    const { data, error } = await supabase
      .from('feature_flags')
      .select('feature_key, enabled_globally, enabled_for_admins, enabled_for_early_adopters');
    
    if (error) {
      console.error('Error verifying feature_flags table:', error);
    } else {
      console.log(`Feature flags table contains ${data.length} flags:`);
      console.table(data);
    }
    
  } catch (error) {
    console.error('Migration failed:', error);
  }
}

// Run the migration
applyMigration()
  .catch(error => {
    console.error('Unexpected error during migration:', error);
    process.exit(1);
  });
