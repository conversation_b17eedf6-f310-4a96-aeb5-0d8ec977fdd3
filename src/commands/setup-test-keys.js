// Script to set up unique test keys for Edge Functions
import fs from 'fs';
import path from 'path';

// Generate a unique key with timestamp and random string
const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
const randomString = Math.random().toString(36).substring(2, 10);
const testKey = `TEST_KEY_${timestamp}_${randomString}`;

console.log(`Generated test key: ${testKey}`);

// Update .env.local
const envLocalPath = '.env.local';
let envLocalContent = fs.readFileSync(envLocalPath, 'utf8');
envLocalContent = envLocalContent.replace(
  /VITE_EDGE_FUNCTION_SECRET=.*/,
  `VITE_EDGE_FUNCTION_SECRET=${testKey}`
);
fs.writeFileSync(envLocalPath, envLocalContent);
console.log(`Updated ${envLocalPath}`);

// Update Edge Function .env files
const edgeFunctions = ['debug', 'sdg-etl'];
edgeFunctions.forEach(func => {
  const envPath = path.join('supabase', 'functions', func, '.env');
  let envContent = fs.readFileSync(envPath, 'utf8');
  envContent = envContent.replace(
    /EDGE_FUNCTION_SECRET=.*/,
    `EDGE_FUNCTION_SECRET=${testKey}`
  );
  fs.writeFileSync(envPath, envContent);
  console.log(`Updated ${envPath}`);
});

// Create test program
const testDebugPath = 'src/commands/test-with-key.js';
const testDebug = `// Test program with the unique test key
import fetch from 'node-fetch';

const SUPABASE_URL = 'https://wgsnpiskyczxhojlrwtr.supabase.co';
const TEST_KEY = '${testKey}';

async function testDebug() {
  console.log('Testing debug function with test key');
  
  try {
    const response = await fetch(\`\${SUPABASE_URL}/functions/v1/debug\`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': \`Bearer \${TEST_KEY}\`
      }
    });
    
    console.log(\`Response status: \${response.status}\`);
    const data = await response.json();
    console.log('Response data:', JSON.stringify(data, null, 2));
    
    return data;
  } catch (error) {
    console.error('Error:', error);
  }
}

testDebug();
`;

fs.writeFileSync(testDebugPath, testDebug);
console.log(`Created test program: ${testDebugPath}`);

console.log('\nNext steps:');
console.log('1. Deploy both Edge Functions:');
console.log('   node scripts/deploy-debug-function.js');
console.log('   node scripts/deploy-sdg-edge-function.js');
console.log('2. Test the debug function:');
console.log('   node src/commands/test-with-key.js');
