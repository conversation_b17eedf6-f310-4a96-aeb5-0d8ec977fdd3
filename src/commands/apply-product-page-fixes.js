/**
 * This script applies fixes to the Products page rendering issues
 * It specifically addresses permission system integration and error handling
 */

const fs = require('fs');
const path = require('path');

// Paths to modify
const PRODUCTS_PAGE = path.join(__dirname, '..', 'pages', 'ProductsPage.tsx');
const APP_TSX = path.join(__dirname, '..', 'App.tsx');

// Files to create if they don't exist
const newFiles = [
  {
    path: path.join(__dirname, '..', 'pages', 'ProductsFallback.tsx'),
    contents: `import React from "react";
import { Button } from "@/components/ui/button";
import { Sparkles } from "lucide-react";
import { useNavigate } from "react-router-dom";

/**
 * Simple fallback component for the Products page
 * Used as a last resort when the main ProductsPage component fails to render
 */
export default function ProductsFallback() {
  const navigate = useNavigate();
  
  // Get error information from error boundary if available
  const errorInfo = window.__PRODUCTS_ERROR || "Unknown error during rendering";
  
  const goToSafeMode = () => {
    navigate('/products/safe');
  };
  
  return (
    <div className="container mx-auto p-8 flex flex-col items-center">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
          <Sparkles className="h-6 w-6 text-primary" />
          Statistical Products (Fallback)
        </h1>
        <p className="text-muted-foreground mt-2">
          This is a simplified fallback view of the products page
        </p>
      </div>
      
      <div className="bg-white dark:bg-gray-900 rounded-xl shadow-sm border border-gray-100 dark:border-gray-800 p-6 w-full max-w-3xl">
        <h2 className="text-xl font-bold mb-4">Troubleshooting Information</h2>
        <p className="mb-4">
          The main ProductsPage component encountered an error during rendering.
          This simplified view is shown as a fallback to prevent a blank screen.
        </p>
        <div className="p-4 bg-gray-100 dark:bg-gray-800 rounded-md mb-4">
          <p className="font-mono text-sm">
            Time: {new Date().toLocaleString()}
          </p>
          {typeof errorInfo === 'string' && (
            <p className="font-mono text-sm mt-2 break-words">
              {errorInfo}
            </p>
          )}
        </div>
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button onClick={() => window.location.reload()}>
            Reload Page
          </Button>
          <Button variant="outline" onClick={goToSafeMode}>
            Try Safe Mode
          </Button>
        </div>
      </div>
    </div>
  );
}

// Add a global window property for error information
declare global {
  interface Window {
    __PRODUCTS_ERROR?: string;
  }
}`
  },
  {
    path: path.join(__dirname, '..', 'pages', 'ProductsSafeMode.tsx'),
    contents: `import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Sparkles } from "lucide-react";
import { useLanguage } from "@/context/LanguageContext";
import { useToast } from "@/components/ui/use-toast";

/**
 * Simplified version of the ProductsPage for troubleshooting
 * This component has minimal dependencies and rendering logic to isolate issues
 */
export default function ProductsSafeMode() {
  const { isArabic } = useLanguage();
  const { toast } = useToast();
  const [debugInfo, setDebugInfo] = useState<string>("");

  // Collect debug information on mount
  useEffect(() => {
    const collectDebugInfo = async () => {
      try {
        // Import the auth and query hooks dynamically to prevent import errors
        const authModule = await import("@/hooks/useAuth");
        const permissionsModule = await import("@/hooks/usePermissions");
        
        const authInfo = JSON.stringify(authModule ? "Auth module loaded" : "Auth module not loaded");
        const permissionsInfo = JSON.stringify(permissionsModule ? "Permissions module loaded" : "Permissions module not loaded");
        
        // Build debug info
        const info = \`
          Auth Module: \${authInfo}
          Permissions Module: \${permissionsInfo}
          Is Arabic: \${isArabic}
          Time: \${new Date().toISOString()}
        \`;
        
        setDebugInfo(info);
      } catch (error) {
        console.error("Error collecting debug info:", error);
        setDebugInfo(\`Error collecting debug info: \${error instanceof Error ? error.message : String(error)}\`);
      }
    };

    collectDebugInfo();
  }, [isArabic]);

  const handleFullReload = () => {
    window.location.href = '/products';
  };

  const handleShowDebugInfo = () => {
    toast({
      title: "Debug Information",
      description: debugInfo,
      duration: 10000,
    });
  };

  return (
    <div className="container mx-auto p-8 flex flex-col items-center">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
          <Sparkles className="h-6 w-6 text-primary" />
          {isArabic ? "المنتجات (الوضع الآمن)" : "Products (Safe Mode)"}
        </h1>
        <p className="text-muted-foreground mt-2">
          {isArabic 
            ? "عرض مبسط للمنتجات لاستكشاف المشكلات وإصلاحها" 
            : "Simplified products view for troubleshooting"}
        </p>
      </div>
      
      <div className="bg-white dark:bg-gray-900 rounded-xl shadow-sm border border-gray-100 dark:border-gray-800 p-6 w-full max-w-3xl">
        <h2 className="text-xl font-bold mb-4">
          {isArabic ? "المنتجات الإحصائية" : "Statistical Products"}
        </h2>
        <p className="mb-6">
          {isArabic 
            ? "هذه نسخة مبسطة من صفحة المنتجات مع الحد الأدنى من المكونات. إذا ظهرت هذه الصفحة بشكل صحيح، فهناك مشكلة في أحد مكونات صفحة المنتجات الأصلية." 
            : "This is a simplified version of the Products page with minimal components. If this page renders correctly, there's an issue with one of the components in the original Products page."}
        </p>
        
        <div className="p-4 bg-gray-100 dark:bg-gray-800 rounded-md mb-4">
          <p className="font-mono text-sm">
            {isArabic ? "الوقت" : "Time"}: {new Date().toLocaleString()}
          </p>
        </div>
        
        <div className="flex flex-col md:flex-row gap-4 justify-center">
          <Button onClick={handleFullReload}>
            {isArabic ? "إعادة تحميل الصفحة الكاملة" : "Reload Full Page"}
          </Button>
          <Button variant="outline" onClick={handleShowDebugInfo}>
            {isArabic ? "عرض معلومات التصحيح" : "Show Debug Info"}
          </Button>
        </div>
      </div>
    </div>
  );
}`
  },
  {
    path: path.join(__dirname, '..', 'components', 'ui', 'error-boundary.tsx'),
    contents: `import React, { Component, ErrorInfo, ReactNode } from 'react';

interface ErrorBoundaryProps {
  fallback: ReactNode;
  children: ReactNode;
  errorKey?: string; // Optional identifier for the error
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null
    };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error to console for debugging
    console.error('Error caught by error boundary:', error);
    console.error('Component stack trace:', errorInfo.componentStack);
    
    // Store the error in a global variable for access by the fallback component
    // This allows the fallback component to display error details
    if (this.props.errorKey) {
      (window as any)[\`__\${this.props.errorKey}_ERROR\`] = 
        \`\${error.name}: \${error.message}\\n\${errorInfo.componentStack}\`;
    }
    
    // You could also log to an error reporting service here
  }

  componentWillUnmount(): void {
    // Clean up the global error when the component unmounts
    if (this.props.errorKey && (window as any)[\`__\${this.props.errorKey}_ERROR\`]) {
      delete (window as any)[\`__\${this.props.errorKey}_ERROR\`];
    }
  }

  render(): ReactNode {
    if (this.state.hasError) {
      return this.props.fallback;
    }

    return this.props.children;
  }
}

export function ErrorBoundaryWithFallback({ 
  fallback, 
  children,
  errorKey = 'PRODUCTS' // Default error key for backward compatibility
}: ErrorBoundaryProps): JSX.Element {
  return (
    <ErrorBoundary fallback={fallback} errorKey={errorKey}>
      {children}
    </ErrorBoundary>
  );
}`
  },
  {
    path: path.join(__dirname, '..', 'utils', 'permissionsHelpers.ts'),
    contents: `/**
 * Utilities for working with permissions and capabilities
 */

// Avoid directly importing the type to prevent circular dependencies
// The permissions is passed in as 'any' and safely checked

/**
 * Safe wrapper around permission checking to avoid runtime errors
 * @param context The permissions context
 * @param capability The capability to check
 * @param contextType Optional context type
 * @param entityId Optional entity ID
 * @returns True if the user has the capability, false otherwise or if context is invalid
 */
export function safeHasCapability(
  context: any,
  capability: string,
  contextType?: string,
  entityId?: string
): boolean {
  if (!context) {
    console.warn('Permissions context is undefined');
    return false;
  }

  try {
    // First try the new API
    if (typeof context.hasCapabilitySync === 'function') {
      return context.hasCapabilitySync(capability, contextType, entityId);
    }
    
    // Fallback to legacy API
    if (typeof context.hasPermission === 'function') {
      return context.hasPermission(capability);
    }
    
    // No valid permission checking function found
    console.warn('No valid permission checking function found in context');
    return false;
  } catch (error) {
    console.error('Error checking capability:', error);
    // Fail safe
    return false;
  }
}

/**
 * Module-level permission helper - provides a simplified interface
 * @param context The permissions context
 * @param module The module name
 * @param level The permission level (read, create, update, delete, admin)
 * @returns True if the user has the capability
 */
export function hasModulePermission(
  context: any,
  module: string,
  level: 'read' | 'create' | 'update' | 'delete' | 'admin'
): boolean {
  const capability = \`\${level}:\${module}\`;
  return safeHasCapability(context, capability);
}`
  },
  {
    path: path.join(__dirname, '..', 'components', 'permissions', 'SafePermissionGate.tsx'),
    contents: `import React, { ReactNode } from 'react';
import { usePermissions } from '@/hooks/usePermissions';
import { safeHasCapability } from '@/utils/permissionsHelpers';

interface SafePermissionGateProps {
  /**
   * The system module to check permissions for
   */
  module: string;
  
  /**
   * The required permission level (read, create, update, delete, admin)
   */
  level: 'read' | 'create' | 'update' | 'delete' | 'admin';
  
  /**
   * Optional context type for contextual permissions
   */
  contextType?: string;
  
  /**
   * Optional entity ID for entity-specific permissions
   */
  entityId?: string;
  
  /**
   * The content to render if the user has permission
   */
  children: ReactNode;
  
  /**
   * Optional fallback content to render if the user does not have permission
   */
  fallback?: ReactNode;
}

/**
 * A safer version of PermissionGate that uses the newer capability-based
 * permission checking and falls back gracefully if permissions are unavailable
 */
export const SafePermissionGate: React.FC<SafePermissionGateProps> = ({
  module,
  level,
  contextType,
  entityId,
  children,
  fallback = null,
}) => {
  const permissions = usePermissions();
  
  // Generate the capability code based on level and module
  const capabilityCode = \`\${level}:\${module}\`;
  
  // Use the safe capability checker that handles errors and undefined contexts
  const hasPermission = safeHasCapability(
    permissions, 
    capabilityCode,
    contextType,
    entityId
  );
  
  // Render children if user has permission, otherwise render fallback
  return hasPermission ? <>{children}</> : <>{fallback}</>;
};

export default SafePermissionGate;`
  }
];

// Modifications to apply to existing files
const fileModifications = [
  {
    file: APP_TSX,
    // Find the import section and add our new imports
    find: `import React, { useState, useEffect, useRef } from "react";`,
    replace: `import React, { useState, useEffect, useRef } from "react";
import ProductsFallback from "./pages/ProductsFallback";
import ProductsSafeMode from "./pages/ProductsSafeMode";
import { ErrorBoundaryWithFallback } from "./components/ui/error-boundary";`
  },
  {
    file: APP_TSX,
    // Find the Products route and update it with error boundary
    find: `<Route
                  path="/products"
                  element={
                    <RequireAuth>
                      <AppLayout>
                        <ProductsPage />
                      </AppLayout>
                    </RequireAuth>
                  }
                />`,
    replace: `<Route
                  path="/products"
                  element={
                    <RequireAuth>
                      <AppLayout>
                        <React.Suspense fallback={<div>Loading products...</div>}>
                          <ErrorBoundaryWithFallback fallback={<ProductsFallback />}>
                            <ProductsPage />
                          </ErrorBoundaryWithFallback>
                        </React.Suspense>
                      </AppLayout>
                    </RequireAuth>
                  }
                />
                <Route
                  path="/products/safe"
                  element={
                    <RequireAuth>
                      <AppLayout>
                        <ProductsSafeMode />
                      </AppLayout>
                    </RequireAuth>
                  }
                />`
  },
  {
    file: PRODUCTS_PAGE,
    // Update imports in Products page
    find: `import { usePermissions } from '@/hooks/usePermissions';`,
    replace: `import { usePermissions } from '@/hooks/usePermissions';
import { safeHasCapability } from "@/utils/permissionsHelpers";
import { SafePermissionGate } from "@/components/permissions/SafePermissionGate";`
  },
  {
    file: PRODUCTS_PAGE,
    // Replace hasPermission with permissions to enable safer access
    find: `const { hasPermission } = usePermissionsContext();`,
    replace: `const permissions = usePermissionsContext();`
  },
  {
    file: PRODUCTS_PAGE,
    // Replace PermissionGate with SafePermissionGate
    find: `<PermissionGate module="product_management" level="create">`,
    replace: `<SafePermissionGate module="product_management" level="create">`
  },
  {
    file: PRODUCTS_PAGE,
    // Close the SafePermissionGate tag
    find: `</PermissionGate>`,
    replace: `</SafePermissionGate>`
  }
];

// Main function
async function applyFixes() {
  try {
    console.log('Applying Products page fixes...');
    
    // Create new files
    for (const file of newFiles) {
      if (!fs.existsSync(file.path)) {
        console.log(`Creating file: ${file.path}`);
        // Create directory if it doesn't exist
        const dir = path.dirname(file.path);
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true });
        }
        fs.writeFileSync(file.path, file.contents);
      } else {
        console.log(`File already exists: ${file.path}, skipping...`);
      }
    }
    
    // Apply modifications to existing files
    for (const mod of fileModifications) {
      if (fs.existsSync(mod.file)) {
        console.log(`Modifying file: ${mod.file}`);
        let content = fs.readFileSync(mod.file, 'utf8');
        
        // Check if the content already has the modifications
        if (!content.includes(mod.replace)) {
          // Apply the modification
          content = content.replace(mod.find, mod.replace);
          fs.writeFileSync(mod.file, content);
        } else {
          console.log(`Modification already applied to: ${mod.file}, skipping...`);
        }
      } else {
        console.error(`File not found: ${mod.file}`);
      }
    }
    
    console.log('Products page fixes applied successfully!');
  } catch (error) {
    console.error('Error applying Products page fixes:', error);
  }
}

// Run the function
applyFixes();
