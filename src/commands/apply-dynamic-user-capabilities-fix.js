/**
 * <PERSON><PERSON><PERSON> to apply the fix for "column reference role_id is ambiguous" error
 * in the get_dynamic_user_capabilities function
 * 
 * This script applies the SQL migration that fixes the ambiguous column issue
 * which causes 400 Bad Request errors when calling get_dynamic_user_capabilities
 * 
 * Usage: 
 * node src/commands/apply-dynamic-user-capabilities-fix.js
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import { getSupabaseConnectionString } from './utils/connection-helper.js';

// Load environment variables from .env file
dotenv.config();

// Get current file directory (equivalent to __dirname in CommonJS)
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

console.log(`${colors.blue}⏳ Preparing to apply dynamic user capabilities fix...${colors.reset}\n`);

// Path to the SQL migration file
const migrationFilePath = path.join(process.cwd(), 'supabase', 'migrations', '20250424_fix_dynamic_user_capabilities.sql');

// Check if the migration file exists
if (!fs.existsSync(migrationFilePath)) {
  console.error(`${colors.red}❌ Migration file not found: ${migrationFilePath}${colors.reset}`);
  process.exit(1);
}

// Get PostgreSQL connection string (either from environment or by constructing it)
const connectionString = getSupabaseConnectionString();

if (!connectionString) {
  // Log Supabase configuration details for REST API usage
  console.log(`${colors.yellow}⚠️ No direct PostgreSQL connection string found.${colors.reset}`);
  console.log(`${colors.cyan}ℹ️  Detected Supabase configuration:${colors.reset}`);
  console.log(`   - URL: ${process.env.SUPABASE_URL || '(not configured)'}`);
  console.log(`   - API Key: ${process.env.SUPABASE_ANON_KEY ? '(configured)' : '(not configured)'}`);
  console.log(`   - Service Role Key: ${process.env.SUPABASE_SERVICE_ROLE_KEY ? '(configured)' : '(not configured)'}`);
  
  console.error(`\n${colors.red}❌ Cannot apply migrations without direct database access.${colors.reset}`);
  console.error(`${colors.yellow}💡 You have two options:${colors.reset}`);
  console.error(`   1. Get a direct PostgreSQL connection string from your Supabase dashboard`);
  console.error(`      and add it to .env as SUPABASE_CONNECTION_STRING`);
  console.error(`   2. Use the Supabase UI console to manually run the SQL migrations in:`);
  console.error(`      supabase/migrations/20250424_fix_dynamic_user_capabilities.sql\n`);
  
  process.exit(1);
}

console.log(`${colors.cyan}📄 Found migration file: 20250424_fix_dynamic_user_capabilities.sql${colors.reset}`);
console.log(`${colors.yellow}⚠️  This script will apply the fix for:${colors.reset}`);
console.log(`   - "column reference role_id is ambiguous" error in get_dynamic_user_capabilities\n`);

try {
  // Apply the SQL migration
  console.log(`${colors.blue}🔄 Applying database fix...${colors.reset}`);
  
  const command = process.platform === 'win32' 
    ? `type "${migrationFilePath}" | psql "${connectionString}"` 
    : `cat "${migrationFilePath}" | psql "${connectionString}"`;
    
  execSync(command, { stdio: 'inherit' });
  
  console.log(`\n${colors.green}✅ Successfully applied dynamic user capabilities fix!${colors.reset}`);
  console.log(`${colors.cyan}ℹ️  The following error should now be resolved:${colors.reset}`);
  console.log(`   - Error getting user capabilities: column reference "role_id" is ambiguous`);
  console.log(`   - POST .../rest/v1/rpc/get_dynamic_user_capabilities 400 (Bad Request)\n`);
  
  console.log(`${colors.blue}📊 Refresh your application to see the improved performance${colors.reset}`);
} catch (error) {
  console.error(`\n${colors.red}❌ Failed to apply fix:${colors.reset}`, error.message);
  console.error(`${colors.yellow}💡 Try running the migrations manually with:${colors.reset}`);
  console.error(`   psql "your-connection-string" -f supabase/migrations/20250424_fix_dynamic_user_capabilities.sql`);
  process.exit(1);
}
