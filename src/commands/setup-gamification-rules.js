import { getSupabaseClient } from '../integrations/supabase/gateway.ts';

/**
 * Set up gamification rules that map events to achievements and points
 */
async function setupGamificationRules() {
  console.log("Setting up gamification rules...");
  
  try {
    const supabase = getSupabaseClient();
    
    // Define the rules for achievements and points
    const rules = [
      {
        name: "First Product View",
        name_ar: "أول مشاهدة للمنتج",
        description: "Rule that grants an achievement for viewing the first product",
        description_ar: "قاعدة تمنح إنجازًا لمشاهدة المنتج الأول",
        conditions: {
          event_type: "product_viewed",
          count: 1
        },
        actions: [
          {
            type: "grant_achievement",
            achievement_id: "first_product_view"
          },
          {
            type: "add_points",
            points: 10,
            reason: "Viewed first product"
          }
        ],
        category: "products",
        is_active: true,
        priority: 100
      },
      {
        name: "Explorer",
        name_ar: "مستكشف",
        description: "Rule that grants an achievement for viewing 5 different products",
        description_ar: "قاعدة تمنح إنجازًا لمشاهدة 5 منتجات مختلفة",
        conditions: {
          event_type: "product_viewed",
          count: 5,
          unique_property: "product_id"
        },
        actions: [
          {
            type: "grant_achievement",
            achievement_id: "explorer"
          },
          {
            type: "add_points",
            points: 25,
            reason: "Explored 5 different products"
          }
        ],
        category: "products",
        is_active: true,
        priority: 90
      },
      {
        name: "Product Master",
        name_ar: "سيد المنتجات",
        description: "Rule that grants an achievement for viewing 20 different products",
        description_ar: "قاعدة تمنح إنجازًا لمشاهدة 20 منتجًا مختلفًا",
        conditions: {
          event_type: "product_viewed",
          count: 20,
          unique_property: "product_id"
        },
        actions: [
          {
            type: "grant_achievement",
            achievement_id: "product_master"
          },
          {
            type: "add_points",
            points: 100,
            reason: "Mastered products by viewing 20 different ones"
          }
        ],
        category: "products",
        is_active: true,
        priority: 80
      },
      {
        name: "Creator",
        name_ar: "منشئ",
        description: "Rule that grants an achievement for creating the first product",
        description_ar: "قاعدة تمنح إنجازًا لإنشاء المنتج الأول",
        conditions: {
          event_type: "product_created",
          count: 1
        },
        actions: [
          {
            type: "grant_achievement",
            achievement_id: "creator"
          },
          {
            type: "add_points",
            points: 50,
            reason: "Created first product"
          }
        ],
        category: "products",
        is_active: true,
        priority: 100
      },
      {
        name: "Profile Completer",
        name_ar: "مكمل الملف الشخصي",
        description: "Rule that grants achievement for completing profile",
        description_ar: "قاعدة تمنح إنجازًا لإكمال الملف الشخصي",
        conditions: {
          event_type: "profile_updated",
          data: {
            completed_profile: true
          },
          count: 1
        },
        actions: [
          {
            type: "grant_achievement",
            achievement_id: "profile_completer"
          },
          {
            type: "add_points",
            points: 20,
            reason: "Completed profile information"
          }
        ],
        category: "profile",
        is_active: true,
        priority: 100
      },
      {
        name: "First Login",
        name_ar: "أول تسجيل دخول",
        description: "Rule that grants achievement for first login",
        description_ar: "قاعدة تمنح إنجازًا لأول تسجيل دخول",
        conditions: {
          event_type: "user_login",
          count: 1
        },
        actions: [
          {
            type: "grant_achievement",
            achievement_id: "welcome_aboard"
          },
          {
            type: "add_points",
            points: 5,
            reason: "First login"
          }
        ],
        category: "system",
        is_active: true,
        priority: 100
      }
    ];
    
    // Clear existing rules if needed
    const { error: deleteError } = await supabase
      .from('gamification_rules')
      .delete()
      .eq('name', rules[0].name);
      
    if (deleteError) {
      console.warn("Warning when clearing existing rules:", deleteError.message);
    }
    
    // Insert the rules
    console.log(`Inserting ${rules.length} gamification rules...`);
    for (const rule of rules) {
      const { data, error } = await supabase
        .from('gamification_rules')
        .upsert(rule, {
          onConflict: 'name',
          ignoreDuplicates: false
        });
        
      if (error) {
        console.error(`Error inserting rule "${rule.name}":`, error.message);
      } else {
        console.log(`Rule "${rule.name}" inserted successfully.`);
      }
    }
    
    console.log("Gamification rules setup completed.");
    return true;
  } catch (error) {
    console.error("Error setting up gamification rules:", error);
    return false;
  }
}

// Run the setup if this script is executed directly
if (process.argv[1].includes('setup-gamification-rules')) {
  setupGamificationRules()
    .then(success => {
      if (success) {
        console.log("✅ Gamification rules setup completed successfully.");
      } else {
        console.error("❌ Gamification rules setup failed.");
        process.exit(1);
      }
    });
}

export { setupGamificationRules };
