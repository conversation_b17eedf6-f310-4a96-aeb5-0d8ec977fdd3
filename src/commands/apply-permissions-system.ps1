# PowerShell script to apply the permission system changes
# Usage: .\src\commands\apply-permissions-system.ps1

# Function to write colored output
function Write-ColorOutput {
    param (
        [Parameter(Mandatory=$true)]
        [string]$Message,
        
        [Parameter(Mandatory=$true)]
        [string]$Color
    )
    
    $originalColor = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $Color
    Write-Output $Message
    $host.UI.RawUI.ForegroundColor = $originalColor
}

Write-ColorOutput "=== Domain-Specific Permission System Setup ===" "Blue"
Write-Output ""
Write-Output "This script will apply the permission system database changes."
Write-Output "It creates all necessary tables, functions, and initial data for"
Write-Output "the domain-specific permission system."
Write-Output ""

# Check if node is installed
try {
    $nodeVersion = node -v
} catch {
    Write-ColorOutput "Error: Node.js is not installed." "Red"
    Write-Output "Please install Node.js before running this script."
    exit 1
}

Write-ColorOutput "Step 1: Checking for required files..." "Yellow"
if (-not (Test-Path "src\commands\apply-permissions-system.js")) {
    Write-ColorOutput "Error: src\commands\apply-permissions-system.js not found." "Red"
    exit 1
}

if (-not (Test-Path "supabase\migrations\20250324_create_permission_system.sql")) {
    Write-ColorOutput "Error: Migration file not found." "Red"
    Write-Output "Please ensure that supabase\migrations\20250324_create_permission_system.sql exists."
    exit 1
}

# Create the exec_sql function if needed
Write-ColorOutput "Step 2: Checking for exec_sql function..." "Yellow"
Write-Output "Note: You may need to create this function in your Supabase project if it doesn't exist."
Write-Output "SQL to create the function:"
Write-ColorOutput "CREATE OR REPLACE FUNCTION exec_sql(sql TEXT) RETURNS VOID AS `$`$" "Blue"
Write-ColorOutput "BEGIN" "Blue"
Write-ColorOutput "  EXECUTE sql;" "Blue"
Write-ColorOutput "END;" "Blue"
Write-ColorOutput "`$`$ LANGUAGE plpgsql SECURITY DEFINER;" "Blue"

# Run the permission system application script
Write-ColorOutput "Step 3: Applying permission system changes..." "Yellow"
node src\commands\apply-permissions-system.js

# Check if the script was successful
if ($LASTEXITCODE -eq 0) {
  Write-ColorOutput "✓ Permission system changes applied successfully!" "Green"
  Write-Output ""
  Write-Output "Next steps:"
  Write-Output "1. Update UI components to check permissions before showing action buttons"
  Write-Output "2. Test with different user roles (use the testing tools in Admin panel)"
  Write-Output "3. See src/docs/DOMAIN-SPECIFIC-PERMISSIONS.md for more information"
} else {
  Write-ColorOutput "✗ Error applying permission system changes." "Red"
  Write-Output "Please check the error messages above."
}

Write-Output ""
Write-ColorOutput "=== Script Completed ===" "Blue"
