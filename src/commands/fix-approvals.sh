#!/bin/bash

# <PERSON><PERSON>t to fix approval system issues by updating product iterations
# This script will:
# 1. Get the current user's ID
# 2. Update product iterations to include the user ID as product owner
# 3. Log the results

echo "=== Approval System Fix ==="
echo "This script will fix issues with dependencies not showing up in the approvals dashboard."
echo ""

# Get the current user ID
echo "Step 1: Getting current user ID..."
USER_ID=$(node src/commands/get-current-user.js | grep "User ID:" | cut -d' ' -f3)

if [ -z "$USER_ID" ]; then
  echo "Error: Could not get user ID. Please make sure you're logged in."
  echo "Try running 'node src/commands/get-current-user.js' manually to see the error."
  exit 1
fi

echo "Found user ID: $USER_ID"
echo ""

# Update product iterations
echo "Step 2: Updating product iterations..."
node src/commands/update-product-iterations.js "$USER_ID"

echo ""
echo "=== Fix Complete ==="
echo "Please refresh the approvals dashboard to see the changes."
echo "If you still don't see your dependencies, check the README-approval-fix.md file for troubleshooting steps."
