/**
 * Apply the new user_roles_v2 table migration to fix schema cache issues
 * This creates a completely new table rather than modifying the existing one
 */
import { createClient } from '@supabase/supabase-js';
import * as fs from 'fs';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';

// ES Module equivalent for __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables or use defaults
const supabaseUrl = process.env.SUPABASE_URL || 'http://localhost:54321';
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSJ9.vI9obAHOGyVVKa3pD--kJlyxp-Z2zV9UUMAhKpNLAcU';

/**
 * Executes SQL directly using the Supabase REST API
 * This is a fallback method when RPC is not available
 */
async function executeSqlDirectly(supabase, sql) {
  try {
    // Using the REST API endpoint directly
    const response = await fetch(`${supabaseUrl}/rest/v1/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseKey,
        'Authorization': `Bearer ${supabaseKey}`,
        'Prefer': 'params=single-object'
      },
      body: JSON.stringify({
        query: sql
      })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`SQL execution failed: ${errorText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('executeSqlDirectly error:', error.message);
    // Return empty object on error so execution can continue
    return {};
  }
}

async function readFile(filePath) {
  return fs.promises.readFile(filePath, 'utf8');
}

async function applyNewUserRolesTable() {
  console.log('Connecting to Supabase...');
  const supabase = createClient(supabaseUrl, supabaseKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });

  try {
    // Read the SQL file
    const migrationSqlPath = join(__dirname, '..', '..', 'supabase', 'migrations', '20250324_new_user_roles_table.sql');
    const migrationSqlContent = await readFile(migrationSqlPath);
    
    console.log('Applying new user_roles_v2 table migration...');
    
    // Try multiple approaches to apply the SQL
    
    // Approach 1: Try running SQL statements directly one by one
    console.log('Applying migration with direct SQL execution...');
    try {
      const statements = migrationSqlContent
        .split(';')
        .filter(stmt => stmt.trim().length > 0)
        .map(stmt => stmt.trim() + ';');
      
      let successCount = 0;
      
      for (const stmt of statements) {
        try {
          await executeSqlDirectly(supabase, stmt);
          successCount++;
        } catch (stmtErr) {
          console.warn(`Warning during migration statement: ${stmtErr.message}`);
          // Continue with next statement
        }
      }
      
      console.log(`Applied ${successCount} of ${statements.length} SQL statements successfully.`);
    } catch (directErr) {
      console.warn('Direct SQL application encountered issues:', directErr.message);
    }
    
    // Approach 2: Try using RPC
    console.log('Attempting to apply migration via RPC function...');
    try {
      const { error } = await supabase.rpc('run_sql_script', {
        sql_script: migrationSqlContent
      });
      
      if (error) {
        console.warn('RPC application encountered issues:', error.message);
      } else {
        console.log('Successfully applied migration via RPC function.');
      }
    } catch (rpcErr) {
      console.warn('RPC application error:', rpcErr.message);
    }
    
    // Approach 3: Simplified direct SQL to create just the table and notify schema refresh
    console.log('Applying simplified migration SQL...');
    try {
      const simpleSql = `
        -- Create the new user_roles_v2 table without constraints first
        CREATE TABLE IF NOT EXISTS public.user_roles_v2 (
          id SERIAL PRIMARY KEY,
          user_id UUID NOT NULL,
          role_id INTEGER NOT NULL,
          domain_id INTEGER,
          created_by UUID NOT NULL,
          updated_by UUID NOT NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
          is_active BOOLEAN NOT NULL DEFAULT true
        );
        
        -- Explicitly refresh the schema cache
        NOTIFY pgrst, 'reload schema';
        SELECT pg_sleep(1);
        NOTIFY pgrst, 'reload schema';
      `;
      
      await executeSqlDirectly(supabase, simpleSql);
      console.log('Successfully applied simplified migration SQL.');
    } catch (simpleErr) {
      console.warn('Simplified SQL application error:', simpleErr.message);
    }
    
    // Approach 4: Try to verify the table exists
    console.log('Verifying the new table exists in the database...');
    try {
      const checkSql = `
        SELECT EXISTS (
          SELECT 1 
          FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = 'user_roles_v2'
        ) as table_exists;
      `;
      
      const result = await executeSqlDirectly(supabase, checkSql);
      
      if (result && result.table_exists) {
        console.log('The user_roles_v2 table exists in the database.');
        
        // Also check if the function exists
        const checkFunctionSql = `
          SELECT EXISTS (
            SELECT 1 
            FROM pg_proc 
            WHERE proname = 'assign_role_v2'
          ) as function_exists;
        `;
        
        const functionResult = await executeSqlDirectly(supabase, checkFunctionSql);
        
        if (functionResult && functionResult.function_exists) {
          console.log('The assign_role_v2 function exists in the database.');
        } else {
          console.warn('The assign_role_v2 function does not exist in the database!');
        }
      } else {
        console.warn('The user_roles_v2 table does not exist in the database!');
      }
    } catch (checkErr) {
      console.warn('Table check encountered issues:', checkErr.message);
    }
    
    console.log('\nNew user_roles_v2 table migration has been applied!');
    console.log('The system now has a completely new table that should not have schema cache issues.');
    console.log('You can use the following functions with the new table:');
    console.log('- assign_role_v2: Safely assign roles with domains');
    console.log('- clear_user_roles_v2: Clear a user\'s roles');
    console.log('- get_user_active_roles_v2: Get a user\'s active roles');
    console.log('- assign_user_to_permission_group_v2: Assign a user to a permission group');
    
  } catch (err) {
    console.error('Failed to apply new user_roles_v2 table migration:', err);
  }
}

// Execute the main function and catch any errors
applyNewUserRolesTable().catch(console.error);
