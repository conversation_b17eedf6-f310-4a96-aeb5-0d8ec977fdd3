import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Initialize environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Missing Supabase credentials');
  console.error('Make sure VITE_SUPABASE_URL and VITE_SUPABASE_SERVICE_KEY (or VITE_SUPABASE_ANON_KEY) are set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Assign essential capabilities to the admin role
 * 
 * This script ensures that admin users have all the necessary capabilities,
 * even if the dynamic permissions system is being rolled out incrementally.
 */
async function assignAdminCapabilities() {
  console.log('Starting admin capabilities assignment...');

  try {
    // 1. Check if the dynamic permissions tables exist
    const { error: tableCheckError } = await supabase
      .from('dynamic_roles')
      .select('id', { count: 'exact', head: true });

    if (tableCheckError) {
      if (tableCheckError.code === '42P01') {
        console.log('Dynamic permissions tables do not exist yet. Creating them first...');
        
        // This suggests the dynamic permissions system is not set up yet
        // You need to run the appropriate setup scripts first
        console.log('Please run apply-dynamic-permissions-system.js first to set up the permissions system.');
        process.exit(1);
      } else {
        throw tableCheckError;
      }
    }

    // 2. Get/Create the admin role
    let adminRoleId;
    const { data: adminRole, error: adminRoleError } = await supabase
      .from('dynamic_roles')
      .select('id')
      .eq('name', 'admin')
      .maybeSingle();

    if (adminRoleError) {
      throw adminRoleError;
    }

    if (adminRole) {
      adminRoleId = adminRole.id;
      console.log(`Found existing admin role with ID: ${adminRoleId}`);
    } else {
      // Create the admin role if it doesn't exist
      const { data: newRole, error: createRoleError } = await supabase
        .from('dynamic_roles')
        .insert({
          name: 'admin',
          name_ar: 'مسؤول',
          description: 'Full system administrator with all capabilities',
          is_active: true
        })
        .select()
        .single();

      if (createRoleError) {
        throw createRoleError;
      }

      adminRoleId = newRole.id;
      console.log(`Created new admin role with ID: ${adminRoleId}`);
    }

    // 3. Define essential capabilities for admins
    const essentialCapabilities = [
      { code: 'manage:roles', name: 'Manage Roles', category: 'system', description: 'Ability to view and manage roles and permissions' },
      { code: 'manage:users', name: 'Manage Users', category: 'system', description: 'Ability to view and manage users' },
      { code: 'manage:system', name: 'Manage System', category: 'system', description: 'Ability to manage system settings' },
      { code: 'view:analytics', name: 'View Analytics', category: 'analytics', description: 'Ability to view system analytics' }
    ];

    // 4. Create capabilities that don't exist yet
    for (const capability of essentialCapabilities) {
      // Check if the capability already exists
      const { data: existingCap, error: checkCapError } = await supabase
        .from('capabilities')
        .select('id')
        .eq('code', capability.code)
        .maybeSingle();

      if (checkCapError) {
        throw checkCapError;
      }

      let capabilityId;

      if (existingCap) {
        capabilityId = existingCap.id;
        console.log(`Found existing capability: ${capability.code} (${capabilityId})`);
      } else {
        // Create the capability
        const { data: newCap, error: createCapError } = await supabase
          .from('capabilities')
          .insert({
            code: capability.code,
            name: capability.name,
            category: capability.category,
            description: capability.description
          })
          .select()
          .single();

        if (createCapError) {
          throw createCapError;
        }

        capabilityId = newCap.id;
        console.log(`Created new capability: ${capability.code} (${capabilityId})`);
      }

      // 5. Assign capability to admin role if not already assigned
      const { data: existingAssignment, error: checkAssignmentError } = await supabase
        .from('role_capabilities')
        .select('id')
        .eq('role_id', adminRoleId)
        .eq('capability_id', capabilityId)
        .maybeSingle();

      if (checkAssignmentError) {
        throw checkAssignmentError;
      }

      if (existingAssignment) {
        console.log(`Capability ${capability.code} already assigned to admin role`);
      } else {
        // Assign the capability to the admin role
        const { error: assignCapError } = await supabase
          .from('role_capabilities')
          .insert({
            role_id: adminRoleId,
            capability_id: capabilityId,
            is_granted: true,
            constraints: {}
          });

        if (assignCapError) {
          throw assignCapError;
        }

        console.log(`Assigned capability ${capability.code} to admin role`);
      }
    }

    // 6. Set all users with 'admin' role in profiles.roles to have the dynamic admin role
    console.log('Syncing users with admin role in profiles table...');
    
    // First, find users with admin role
    const { data: adminUsers, error: findUsersError } = await supabase
      .from('profiles')
      .select('id')
      .contains('roles', ['admin']);

    if (findUsersError) {
      console.warn('Warning: Could not read profiles table:', findUsersError.message);
      console.warn('Skipping user role sync. Please assign roles manually.');
    } else if (adminUsers.length > 0) {
      console.log(`Found ${adminUsers.length} users with admin role in profiles.roles array`);
      
      // Assign dynamic admin role to each of these users
      for (const user of adminUsers) {
        // Check if user already has the role
        const { data: existingUserRole, error: checkUserRoleError } = await supabase
          .from('dynamic_user_roles')
          .select('id')
          .eq('user_id', user.id)
          .eq('role_id', adminRoleId)
          .maybeSingle();

        if (checkUserRoleError) {
          console.warn(`Warning: Error checking role for user ${user.id}:`, checkUserRoleError.message);
          continue;
        }

        if (existingUserRole) {
          console.log(`User ${user.id} already has admin role`);
        } else {
          // Assign the dynamic admin role
          const { error: assignRoleError } = await supabase
            .from('dynamic_user_roles')
            .insert({
              user_id: user.id,
              role_id: adminRoleId,
              constraints: {},
              valid_from: new Date().toISOString()
            });

          if (assignRoleError) {
            console.warn(`Warning: Error assigning role to user ${user.id}:`, assignRoleError.message);
          } else {
            console.log(`Assigned admin role to user ${user.id}`);
          }
        }
      }
    } else {
      console.log('No users with admin role found in profiles table');
    }

    console.log('Admin capabilities assignment completed successfully!');
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

// Run the script
assignAdminCapabilities();
