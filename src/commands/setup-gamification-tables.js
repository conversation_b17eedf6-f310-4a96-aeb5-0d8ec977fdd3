import * as dotenv from 'dotenv';
import { getSupabaseClient } from '../integrations/supabase/gateway.ts';

// Skip Node.js specific modules for browser compatibility
// import { fileURLToPath } from 'url';
// import { dirname } from 'path';

// Initialize dotenv
dotenv.config();

// Use browser-friendly alternatives to Node.js path functions
// const __filename = fileURLToPath(import.meta.url);
// const __dirname = dirname(__filename);

// Initialize Supabase client
const supabase = getSupabaseClient();

/**
 * Main function to set up gamification tables
 */
async function setupGamificationTables() {
  console.log('Setting up gamification system tables...');

  try {
    // Check connection
    const { data: connectionTest, error: connectionError } = await supabase.from('auth.users').select('id').limit(1);
    
    if (connectionError) {
      console.warn('Warning: Connection test failed. Check your Supabase credentials.');
      console.warn(connectionError.message);
    } else {
      console.log('Supabase connection successful');
    }

    // Create tables
    await createUserProfilesTable();
    await createAchievementsTable();
    await createUserAchievementsTable();
    await createGamificationEventsTable();
    await createGamificationConfigTable();
    await createGamificationRulesTable();
    
    // Set up initial data
    await setupInitialAchievements();
    await setupInitialConfig();
    
    // We'll skip RLS policies for now as they require admin access
    console.log('Note: RLS policies setup skipped. You may need to set them up manually.');
    
    console.log('✅ Gamification tables setup complete!');
    
  } catch (err) {
    console.error('Error setting up gamification tables:', err);
    process.exit(1);
  }
}

/**
 * Create user profiles table to store gamification-specific user data
 */
async function createUserProfilesTable() {
  console.log('Creating user_gamification_profiles table...');
  
  // First check if the table already exists
  const { error: checkError } = await supabase.from('user_gamification_profiles').select('id').limit(1);
  
  if (!checkError || !checkError.message.includes('relation "user_gamification_profiles" does not exist')) {
    console.log('✓ user_gamification_profiles table already exists');
    return;
  }
  
  // Create the table through API
  const { error } = await supabase.auth.admin.createTable({
    name: 'user_gamification_profiles',
    columns: [
      {
        name: 'id',
        type: 'uuid',
        primaryKey: true,
        references: 'auth.users(id)',
        onDelete: 'CASCADE'
      },
      {
        name: 'points',
        type: 'integer',
        default: 0,
        notNull: true
      },
      {
        name: 'level',
        type: 'integer',
        default: 1,
        notNull: true
      },
      {
        name: 'created_at',
        type: 'timestamp with time zone',
        default: 'timezone(\'utc\', now())',
        notNull: true
      },
      {
        name: 'updated_at',
        type: 'timestamp with time zone',
        default: 'timezone(\'utc\', now())',
        notNull: true
      }
    ],
    indices: [
      {
        name: 'user_gamification_profiles_points_idx',
        columns: ['points'],
        type: 'btree',
        order: 'DESC'
      }
    ]
  });
  
  if (error) {
    console.warn(`Warning: Could not create user_gamification_profiles table automatically: ${error.message}`);
    console.log('You may need to create this table manually through the Supabase dashboard.');
    return;
  }
  
  console.log('✓ user_gamification_profiles table created successfully');
}

/**
 * Create achievements table
 */
async function createAchievementsTable() {
  console.log('Creating achievements table...');
  
  // First check if the table already exists
  const { error: checkError } = await supabase.from('achievements').select('id').limit(1);
  
  if (!checkError || !checkError.message.includes('relation "achievements" does not exist')) {
    console.log('✓ achievements table already exists');
    return;
  }
  
  // Create the table through API
  const { error } = await supabase.auth.admin.createTable({
    name: 'achievements',
    columns: [
      {
        name: 'id',
        type: 'uuid',
        primaryKey: true,
        default: 'gen_random_uuid()'
      },
      {
        name: 'name',
        type: 'text',
        notNull: true
      },
      {
        name: 'name_ar',
        type: 'text'
      },
      {
        name: 'description',
        type: 'text',
        notNull: true
      },
      {
        name: 'description_ar',
        type: 'text'
      },
      {
        name: 'icon',
        type: 'text',
        default: '\'trophy\'',
        notNull: true
      },
      {
        name: 'points',
        type: 'integer',
        default: 50,
        notNull: true
      },
      {
        name: 'category',
        type: 'text',
        default: '\'general\'',
        notNull: true
      },
      {
        name: 'is_active',
        type: 'boolean',
        default: true,
        notNull: true
      },
      {
        name: 'created_at',
        type: 'timestamp with time zone',
        default: 'timezone(\'utc\', now())',
        notNull: true
      },
      {
        name: 'updated_at',
        type: 'timestamp with time zone',
        default: 'timezone(\'utc\', now())',
        notNull: true
      },
      {
        name: 'created_by',
        type: 'uuid',
        references: 'auth.users(id)'
      },
      {
        name: 'updated_by',
        type: 'uuid',
        references: 'auth.users(id)'
      }
    ],
    indices: [
      {
        name: 'achievements_category_idx',
        columns: ['category'],
        type: 'btree'
      }
    ]
  });
  
  if (error) {
    console.warn(`Warning: Could not create achievements table automatically: ${error.message}`);
    console.log('You may need to create this table manually through the Supabase dashboard.');
    return;
  }
  
  console.log('✓ achievements table created successfully');
}

/**
 * Create user achievements table (junction table)
 */
async function createUserAchievementsTable() {
  console.log('Creating user_achievements table...');
  
  const { error } = await supabase.rpc('execute_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS user_achievements (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
        achievement_id UUID NOT NULL REFERENCES achievements(id) ON DELETE CASCADE,
        earned_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL,
        UNIQUE(user_id, achievement_id)
      );
      
      CREATE INDEX IF NOT EXISTS user_achievements_user_id_idx ON user_achievements (user_id);
      CREATE INDEX IF NOT EXISTS user_achievements_achievement_id_idx ON user_achievements (achievement_id);
    `
  });
  
  if (error) {
    throw new Error(`Failed to create user_achievements table: ${error.message}`);
  }
  
  console.log('✓ user_achievements table created or already exists');
}

/**
 * Create gamification events table
 */
async function createGamificationEventsTable() {
  console.log('Creating gamification_events table...');
  
  const { error } = await supabase.rpc('execute_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS gamification_events (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
        event_type TEXT NOT NULL,
        event_data JSONB,
        occurred_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL
      );
      
      CREATE INDEX IF NOT EXISTS gamification_events_user_id_idx ON gamification_events (user_id);
      CREATE INDEX IF NOT EXISTS gamification_events_event_type_idx ON gamification_events (event_type);
      CREATE INDEX IF NOT EXISTS gamification_events_occurred_at_idx ON gamification_events (occurred_at DESC);
    `
  });
  
  if (error) {
    throw new Error(`Failed to create gamification_events table: ${error.message}`);
  }
  
  console.log('✓ gamification_events table created or already exists');
}

/**
 * Create gamification config table
 */
async function createGamificationConfigTable() {
  console.log('Creating gamification_config table...');
  
  const { error } = await supabase.rpc('execute_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS gamification_config (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        key TEXT UNIQUE NOT NULL,
        category TEXT DEFAULT 'general' NOT NULL,
        value JSONB NOT NULL,
        description TEXT,
        is_active BOOLEAN DEFAULT true NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL,
        created_by UUID REFERENCES auth.users(id),
        updated_by UUID REFERENCES auth.users(id)
      );
      
      CREATE INDEX IF NOT EXISTS gamification_config_key_idx ON gamification_config (key);
      CREATE INDEX IF NOT EXISTS gamification_config_category_idx ON gamification_config (category);
    `
  });
  
  if (error) {
    throw new Error(`Failed to create gamification_config table: ${error.message}`);
  }
  
  console.log('✓ gamification_config table created or already exists');
}

/**
 * Create gamification rules table
 */
async function createGamificationRulesTable() {
  console.log('Creating gamification_rules table...');
  
  const { error } = await supabase.rpc('execute_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS gamification_rules (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name TEXT NOT NULL,
        name_ar TEXT,
        description TEXT,
        description_ar TEXT,
        conditions JSONB NOT NULL,
        actions JSONB NOT NULL,
        category TEXT DEFAULT 'general' NOT NULL,
        is_active BOOLEAN DEFAULT true NOT NULL,
        priority INTEGER DEFAULT 100 NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL,
        created_by UUID REFERENCES auth.users(id),
        updated_by UUID REFERENCES auth.users(id)
      );
      
      CREATE INDEX IF NOT EXISTS gamification_rules_category_idx ON gamification_rules (category);
      CREATE INDEX IF NOT EXISTS gamification_rules_priority_idx ON gamification_rules (priority DESC);
    `
  });
  
  if (error) {
    throw new Error(`Failed to create gamification_rules table: ${error.message}`);
  }
  
  console.log('✓ gamification_rules table created or already exists');
}

/**
 * Insert initial achievements
 */
async function setupInitialAchievements() {
  console.log('Setting up initial achievements...');
  
  // First, check if achievements already exist
  const { data: existingAchievements, error: checkError } = await supabase
    .from('achievements')
    .select('id')
    .limit(1);
  
  if (checkError) {
    throw new Error(`Failed to check existing achievements: ${checkError.message}`);
  }
  
  if (existingAchievements && existingAchievements.length > 0) {
    console.log('Achievements already exist, skipping initial setup');
    return;
  }
  
  // Define initial achievements
  const initialAchievements = [
    {
      name: 'Welcome Aboard',
      description: 'Created your account and joined the system',
      icon: 'Award',
      points: 10,
      category: 'onboarding',
    },
    {
      name: 'First Product View',
      description: 'Viewed a product for the first time',
      icon: 'Eye',
      points: 5,
      category: 'products',
    },
    {
      name: 'Explorer',
      description: 'Visited 5 different pages in the system',
      icon: 'Compass',
      points: 20,
      category: 'navigation',
    },
    {
      name: 'Contributor',
      description: 'Created your first product',
      icon: 'Plus',
      points: 50,
      category: 'products',
    },
    {
      name: 'Team Player',
      description: 'Collaborated on a product with others',
      icon: 'Users',
      points: 30,
      category: 'collaboration',
    },
  ];
  
  // Insert the achievements
  const { error: insertError } = await supabase
    .from('achievements')
    .insert(initialAchievements);
  
  if (insertError) {
    throw new Error(`Failed to insert initial achievements: ${insertError.message}`);
  }
  
  console.log('✓ Initial achievements created successfully');
}

/**
 * Setup initial config
 */
async function setupInitialConfig() {
  console.log('Setting up initial configuration...');
  
  // First, check if config already exists
  const { data: existingConfig, error: checkError } = await supabase
    .from('gamification_config')
    .select('id')
    .limit(1);
  
  if (checkError) {
    throw new Error(`Failed to check existing config: ${checkError.message}`);
  }
  
  if (existingConfig && existingConfig.length > 0) {
    console.log('Config already exists, skipping initial setup');
    return;
  }
  
  // Define initial config values
  const initialConfig = [
    {
      key: 'gamification_enabled',
      category: 'system',
      value: { enabled: true },
      description: 'Master switch for the gamification system',
    },
    {
      key: 'level_formula',
      category: 'points',
      value: { formula: 'Math.floor(Math.sqrt(points / 100)) + 1' },
      description: 'Formula to calculate user level based on points',
    },
    {
      key: 'achievement_notification',
      category: 'notifications',
      value: { enabled: true },
      description: 'Show notifications when users earn achievements',
    },
    {
      key: 'level_up_notification',
      category: 'notifications',
      value: { enabled: true },
      description: 'Show notifications when users level up',
    },
    {
      key: 'points_notification',
      category: 'notifications',
      value: { enabled: true, threshold: 10 },
      description: 'Show notifications when users earn points (with minimum threshold)',
    },
    {
      key: 'leaderboard_enabled',
      category: 'social',
      value: { enabled: true },
      description: 'Enable or disable leaderboard functionality',
    },
  ];
  
  // Insert the config
  const { error: insertError } = await supabase
    .from('gamification_config')
    .insert(initialConfig);
  
  if (insertError) {
    throw new Error(`Failed to insert initial config: ${insertError.message}`);
  }
  
  console.log('✓ Initial configuration created successfully');
}

/**
 * Instructions for setting up RLS policies manually
 */
function printRlsPoliciesInstructions() {
  console.log('----------------------------------------------------------------');
  console.log('MANUAL STEPS FOR SETTING UP ROW LEVEL SECURITY (RLS) POLICIES');
  console.log('----------------------------------------------------------------');
  console.log('To set up proper security for your gamification tables, run these');
  console.log('SQL commands in the Supabase SQL Editor:');
  console.log('\n```sql');
  console.log(`
-- Enable RLS on tables
ALTER TABLE user_gamification_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE gamification_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE gamification_config ENABLE ROW LEVEL SECURITY;
ALTER TABLE gamification_rules ENABLE ROW LEVEL SECURITY;

-- Policy for user_gamification_profiles
DROP POLICY IF EXISTS user_profiles_select_policy ON user_gamification_profiles;
CREATE POLICY user_profiles_select_policy ON user_gamification_profiles
  FOR SELECT USING (true); -- Anyone can view profiles

DROP POLICY IF EXISTS user_profiles_insert_policy ON user_gamification_profiles;
CREATE POLICY user_profiles_insert_policy ON user_gamification_profiles
  FOR INSERT WITH CHECK (auth.uid() = id); -- Users can only insert their own profile

DROP POLICY IF EXISTS user_profiles_update_policy ON user_gamification_profiles;
CREATE POLICY user_profiles_update_policy ON user_gamification_profiles
  FOR UPDATE USING (
    auth.uid() = id OR 
    EXISTS (
      SELECT 1 FROM user_roles 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  ); -- Only the user or admins can update profiles

-- Policy for achievements
DROP POLICY IF EXISTS achievements_select_policy ON achievements;
CREATE POLICY achievements_select_policy ON achievements
  FOR SELECT USING (true); -- Anyone can view achievements

DROP POLICY IF EXISTS achievements_insert_update_delete_policy ON achievements;
CREATE POLICY achievements_insert_update_delete_policy ON achievements
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_roles 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  ); -- Only admins can modify achievements

-- Other policies...
  `);
  console.log('```');
  console.log('----------------------------------------------------------------');
}

// Run the setup
setupGamificationTables().catch(err => {
  console.error('Fatal error:', err);
  process.exit(1);
});
