// <PERSON><PERSON>t to check if the current user is the product owner of the dependency
// Usage: node src/commands/check-current-user.js

import { createClient } from '@supabase/supabase-js';

// Define Supabase client
// Get Supabase connection info from environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_KEY;

// Ensure environment variables are set
if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Missing Supabase environment variables.');
  console.error('Make sure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in your environment.');
  console.error('Create a .env file based on .env.example or set these variables in your environment.');
  process.exit(1);
}
const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

async function checkCurrentUser() {
  console.log("=== Checking Dependency Approval System ===");
  
  try {
    // Get the product iteration from the dependency
    const productIterationId = "1605293a-1ed8-4f86-9239-63e52598f322"; // From the dependency in the screenshot
    const productOwnerId = "40031c8e-859d-4240-9147-d29241a76bb0"; // From our previous check
    
    console.log(`Checking product iteration with ID: ${productIterationId}`);
    console.log(`Product Owner ID: ${productOwnerId}`);
    
    const { data: iteration, error: iterationError } = await supabase
      .from('product_iterations')
      .select(`
        id,
        version,
        iteration,
        status,
        product_owner_id,
        product_manager_id,
        product_base_id,
        product_bases:product_base_id (id, name, name_ar)
      `)
      .eq('id', productIterationId)
      .maybeSingle();
      
    if (iterationError) {
      console.error("Error fetching product iteration:", iterationError);
      return;
    }
    
    if (!iteration) {
      console.log(`No product iteration found with ID: ${productIterationId}`);
      return;
    }
    
    console.log('\nProduct Iteration details:');
    console.log(`- ID: ${iteration.id}`);
    console.log(`- Version: ${iteration.version || 'No version'}`);
    console.log(`- Iteration: ${iteration.iteration || 'No iteration'}`);
    console.log(`- Status: ${iteration.status || 'No status'}`);
    console.log(`- Product Owner ID: ${iteration.product_owner_id || 'Not set'}`);
    console.log(`- Product Manager ID: ${iteration.product_manager_id || 'Not set'}`);
    console.log(`- Product Base ID: ${iteration.product_base_id || 'Not set'}`);
    console.log(`- Product Base Name: ${iteration.product_bases?.name || 'Unknown'}`);
    
    // Check if the user has any pending approvals
    const { data: pendingApprovals, error: pendingError } = await supabase
      .from('product_iteration_dependencies')
      .select(`
        id,
        title,
        dependency_type,
        approval_status,
        product_iteration_id
      `)
      .eq('approval_status', 'pending');
      
    if (pendingError) {
      console.error("Error fetching pending approvals:", pendingError);
      return;
    }
    
    console.log(`\nTotal pending dependencies in the system: ${pendingApprovals?.length || 0}`);
    
    // Filter for dependencies that the user should see
    const userDependencies = pendingApprovals?.filter(dep => {
      return dep.product_iteration_id === productIterationId;
    }) || [];
    
    console.log(`Dependencies for the product iteration: ${userDependencies.length}`);
    
    if (userDependencies.length > 0) {
      console.log('\nDependencies that should be visible to the user:');
      userDependencies.forEach((dep, index) => {
        console.log(`${index + 1}. ID: ${dep.id}`);
        console.log(`   Title: ${dep.title || 'No title'}`);
        console.log(`   Type: ${dep.dependency_type || 'Unknown'}`);
        console.log(`   Status: ${dep.approval_status || 'Unknown'}`);
      });
    }
    
    // Check the approval cache
    console.log('\nChecking approval cache...');
    console.log('The approval cache might be stale. Try refreshing the page or clearing the cache.');
    
  } catch (error) {
    console.error("Error:", error);
  }
}

// Run the check
checkCurrentUser().catch(console.error);
