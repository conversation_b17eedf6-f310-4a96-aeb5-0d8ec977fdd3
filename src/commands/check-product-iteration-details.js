// <PERSON>ript to check the details of a product iteration
// Usage: node src/commands/check-product-iteration-details.js [product_iteration_id]

import { createClient } from '@supabase/supabase-js';

// Define Supabase client
// Get Supabase connection info from environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_KEY;

// Ensure environment variables are set
if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Missing Supabase environment variables.');
  console.error('Make sure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in your environment.');
  console.error('Create a .env file based on .env.example or set these variables in your environment.');
  process.exit(1);
}
const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

async function checkProductIteration(iterationId) {
  console.log("=== Checking Product Iteration ===");
  
  try {
    // If no iteration ID is provided, use the one from the dependency
    if (!iterationId) {
      iterationId = "1605293a-1ed8-4f86-9239-63e52598f322"; // From the dependency in the screenshot
    }
    
    console.log(`Checking product iteration with ID: ${iterationId}`);
    
    // Get the product iteration details
    const { data: iteration, error } = await supabase
      .from('product_iterations')
      .select(`
        id,
        version,
        iteration,
        status,
        created_at,
        updated_at,
        product_owner_id,
        product_manager_id,
        product_base_id,
        product_bases:product_base_id (id, name, name_ar)
      `)
      .eq('id', iterationId)
      .maybeSingle();
      
    if (error) {
      console.error("Error fetching product iteration:", error);
      return;
    }
    
    if (!iteration) {
      console.log(`No product iteration found with ID: ${iterationId}`);
      return;
    }
    
    console.log('\nProduct Iteration details:');
    console.log(`- ID: ${iteration.id}`);
    console.log(`- Version: ${iteration.version || 'No version'}`);
    console.log(`- Iteration: ${iteration.iteration || 'No iteration'}`);
    console.log(`- Status: ${iteration.status || 'No status'}`);
    console.log(`- Created: ${new Date(iteration.created_at).toLocaleString()}`);
    console.log(`- Updated: ${iteration.updated_at ? new Date(iteration.updated_at).toLocaleString() : 'Never'}`);
    console.log(`- Product Owner ID: ${iteration.product_owner_id || 'Not set'}`);
    console.log(`- Product Manager ID: ${iteration.product_manager_id || 'Not set'}`);
    console.log(`- Product Base ID: ${iteration.product_base_id || 'Not set'}`);
    console.log(`- Product Base Name: ${iteration.product_bases?.name || 'Unknown'}`);
    console.log(`- Product Base Name (Arabic): ${iteration.product_bases?.name_ar || 'Unknown'}`);
    
    // Check if the product owner is set
    if (!iteration.product_owner_id && !iteration.product_manager_id) {
      console.log('\nThis product iteration does not have a product owner or manager set.');
      console.log('This is why the dependency is not showing up in the dashboard.');
      console.log('The approval system filters out dependencies that don\'t have either a product_owner_id or product_manager_id set.');
    }
    
    // Check the dependent product
    const dependentProductId = "ac016c93-5d12-4cd6-9dcb-924283c1758d"; // From the dependency in the screenshot
    console.log(`\nChecking dependent product with ID: ${dependentProductId}`);
    
    const { data: dependentProduct, error: dependentError } = await supabase
      .from('product_iterations')
      .select(`
        id,
        version,
        iteration,
        product_base_id,
        product_bases:product_base_id (id, name, name_ar)
      `)
      .eq('id', dependentProductId)
      .maybeSingle();
      
    if (dependentError) {
      console.error("Error fetching dependent product:", dependentError);
    } else if (!dependentProduct) {
      console.log(`No product found with ID: ${dependentProductId}`);
    } else {
      console.log('\nDependent Product details:');
      console.log(`- ID: ${dependentProduct.id}`);
      console.log(`- Version: ${dependentProduct.version || 'No version'}`);
      console.log(`- Iteration: ${dependentProduct.iteration || 'No iteration'}`);
      console.log(`- Product Base ID: ${dependentProduct.product_base_id || 'Not set'}`);
      console.log(`- Product Base Name: ${dependentProduct.product_bases?.name || 'Unknown'}`);
      console.log(`- Product Base Name (Arabic): ${dependentProduct.product_bases?.name_ar || 'Unknown'}`);
    }
    
  } catch (error) {
    console.error("Error:", error);
  }
}

// Get product iteration ID from command line arguments
const iterationId = process.argv[2];

// Run the check
checkProductIteration(iterationId).catch(console.error);
