/**
 * Create Schema Version Table
 * 
 * This script creates the schema_versions table if it doesn't exist and initializes
 * it with the current expected version. This addresses the missing table error
 * that's preventing proper schema validation.
 */

import { createClient } from '@supabase/supabase-js';

// Get environment variables
const SUPABASE_URL = process.env.VITE_SUPABASE_URL || prompt('Enter your Supabase URL:');
const SUPABASE_ANON_KEY = process.env.VITE_SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_KEY || prompt('Enter your Supabase anon key:');

// Validation check
if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  console.error("Missing environment variables. Please check your .env file or enter them when prompted.");
  process.exit(1);
}

// Create a direct client to avoid circular dependencies
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Schema version information - must match what's in schemaVersioning.ts
const SCHEMA_VERSION_TABLE = 'schema_versions';
const CURRENT_SCHEMA_VERSION = '1.0.0';
const SCHEMA_COMPONENT_VERSIONS = {
  'core': '1.0.0',
  'stages': '1.0.0',
  'indicators': '1.0.0',
  'gamification': '1.0.0',
  'products': '1.0.0',
  'approvals': '1.0.0',
  'deliverables': '1.0.0'
};

async function createSchemaVersionTable() {
  console.log("Starting schema_versions table creation...");
  
  try {
    // First check if the uuid-ossp extension is enabled (needed for uuid_generate_v4())
    console.log("Checking for uuid-ossp extension...");
    
    let { error: extensionError } = await supabase.rpc('check_extension_exists', { 
      extension_name: 'uuid-ossp' 
    });
    
    if (extensionError) {
      console.log("Could not check extension with RPC, trying direct SQL...");
      
      // If the RPC isn't available, try to create it directly
      await supabase.rpc('exec_sql', { 
        query: 'CREATE EXTENSION IF NOT EXISTS "uuid-ossp";' 
      });
      
      console.log("Created uuid-ossp extension.");
    } else {
      console.log("uuid-ossp extension exists.");
    }
    
    // Check if the schema_versions table exists
    console.log("Checking if schema_versions table exists...");
    
    const { error: tableExistsError } = await supabase
      .from(SCHEMA_VERSION_TABLE)
      .select('id')
      .limit(1);
    
    if (tableExistsError && tableExistsError.message.includes(`relation "${SCHEMA_VERSION_TABLE}" does not exist`)) {
      console.log("Table does not exist, creating it...");
      
      // Create the table directly using SQL
      const createTableSQL = `
        CREATE TABLE IF NOT EXISTS public.${SCHEMA_VERSION_TABLE} (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          version TEXT NOT NULL,
          description TEXT,
          applied_at TIMESTAMPTZ DEFAULT now(),
          required BOOLEAN DEFAULT true,
          components JSONB
        );
        
        -- Create index on version for faster lookups
        CREATE INDEX IF NOT EXISTS schema_versions_version_idx ON public.${SCHEMA_VERSION_TABLE} (version);
      `;
      
      // Execute SQL directly if possible
      try {
        const { error: createError } = await supabase.rpc('exec_sql', { query: createTableSQL });
        
        if (createError) {
          console.error("Could not create table using RPC:", createError);
          console.error("You may need to run this SQL directly in the Supabase SQL editor:");
          console.log(createTableSQL);
          process.exit(1);
        }
        
        console.log("Successfully created schema_versions table.");
      } catch (execError) {
        console.error("Error executing SQL:", execError);
        console.error("You may need to run this SQL directly in the Supabase SQL editor:");
        console.log(createTableSQL);
        process.exit(1);
      }
      
      // Now insert the initial version record
      console.log("Inserting initial schema version record...");
      
      const { error: insertError } = await supabase
        .from(SCHEMA_VERSION_TABLE)
        .insert({
          version: CURRENT_SCHEMA_VERSION,
          description: 'Initial schema version',
          required: true,
          components: SCHEMA_COMPONENT_VERSIONS
        });
      
      if (insertError) {
        console.error("Error inserting initial schema version:", insertError);
        process.exit(1);
      }
      
      console.log(`Successfully inserted initial schema version ${CURRENT_SCHEMA_VERSION}`);
    } else {
      console.log("schema_versions table already exists.");
      
      // Check if we have the current version
      const { data: versionData, error: versionError } = await supabase
        .from(SCHEMA_VERSION_TABLE)
        .select('*')
        .eq('version', CURRENT_SCHEMA_VERSION)
        .maybeSingle();
      
      if (versionError) {
        console.error("Error checking for current version:", versionError);
        process.exit(1);
      }
      
      if (!versionData) {
        console.log(`Current version ${CURRENT_SCHEMA_VERSION} not found, inserting it...`);
        
        const { error: insertError } = await supabase
          .from(SCHEMA_VERSION_TABLE)
          .insert({
            version: CURRENT_SCHEMA_VERSION,
            description: 'Initial schema version',
            required: true,
            components: SCHEMA_COMPONENT_VERSIONS
          });
        
        if (insertError) {
          console.error("Error inserting current schema version:", insertError);
          process.exit(1);
        }
        
        console.log(`Successfully inserted current schema version ${CURRENT_SCHEMA_VERSION}`);
      } else {
        console.log(`Current version ${CURRENT_SCHEMA_VERSION} already exists.`);
      }
    }
    
    // Final verification
    const { data: finalVersions, error: finalError } = await supabase
      .from(SCHEMA_VERSION_TABLE)
      .select('*')
      .order('applied_at', { ascending: false });
    
    if (finalError) {
      console.error("Error verifying schema versions:", finalError);
      process.exit(1);
    }
    
    console.log("Current schema versions:");
    finalVersions.forEach(version => {
      console.log(`- ${version.version} (applied: ${new Date(version.applied_at).toLocaleString()})`);
    });
    
    console.log("\nSchema version table setup complete. The application should now validate successfully.");
    console.log("You may need to restart your application for changes to take effect.");
    
  } catch (error) {
    console.error("Unexpected error:", error);
    process.exit(1);
  }
}

// Run the function
createSchemaVersionTable().catch(err => {
  console.error("Failed to create schema version table:", err);
  process.exit(1);
});

/**
 * If RPC methods aren't available, you can use this SQL in the Supabase SQL Editor:
 * 
 * -- Create extension if it doesn't exist
 * CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
 * 
 * -- Create the schema_versions table
 * CREATE TABLE IF NOT EXISTS public.schema_versions (
 *   id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
 *   version TEXT NOT NULL,
 *   description TEXT,
 *   applied_at TIMESTAMPTZ DEFAULT now(),
 *   required BOOLEAN DEFAULT true,
 *   components JSONB
 * );
 * 
 * -- Create index on version for faster lookups
 * CREATE INDEX IF NOT EXISTS schema_versions_version_idx ON public.schema_versions (version);
 * 
 * -- Insert the initial version
 * INSERT INTO public.schema_versions (version, description, required, components)
 * VALUES (
 *   '1.0.0',
 *   'Initial schema version',
 *   true,
 *   '{"core":"1.0.0","stages":"1.0.0","indicators":"1.0.0","gamification":"1.0.0","products":"1.0.0","approvals":"1.0.0","deliverables":"1.0.0"}'
 * );
 */
