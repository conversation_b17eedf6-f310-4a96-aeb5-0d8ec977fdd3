// <PERSON>ript to fix dependencies by using the current browser user ID
// Usage: node src/commands/fix-browser-user.js

import { createClient } from '@supabase/supabase-js';

// Define Supabase client directly to avoid TypeScript import issues
// Get Supabase connection info from environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_KEY;

// Ensure environment variables are set
if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Missing Supabase environment variables.');
  console.error('Make sure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in your environment.');
  console.error('Create a .env file based on .env.example or set these variables in your environment.');
  process.exit(1);
}
const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

const fixDependenciesWithBrowserUser = async () => {
  console.log(`=== Dependency Fix Tool (Browser User Mode) ===`);
  
  try {
    // Step 1: Get the current user session
    console.log("\nStep 1: Getting current user session...");
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error('Error getting session:', sessionError.message);
      return;
    }
    
    if (!session) {
      console.log('No active session found. Please sign in first.');
      return;
    }
    
    const userId = session.user.id;
    const userEmail = session.user.email;
    
    console.log('Current user information:');
    console.log('------------------------');
    console.log(`User ID: ${userId}`);
    console.log(`Email: ${userEmail}`);
    console.log('------------------------');
    
    // Step 2: Find all pending dependencies
    console.log("\nStep 2: Finding all pending dependencies...");
    const { data: pendingDependencies, error: dependenciesError } = await supabase
      .from('product_iteration_dependencies')
      .select(`
        id, 
        title,
        details,
        dependency_type,
        approval_status,
        created_at,
        product_iteration_id,
        product_iterations:product_iteration_id (id, product_owner_id, product_manager_id, product_base_id)
      `)
      .eq('approval_status', 'pending');
      
    if (dependenciesError) {
      console.error('Error fetching pending dependencies:', dependenciesError);
      return;
    }
    
    console.log(`Found ${pendingDependencies.length} pending dependencies`);
    
    if (pendingDependencies.length === 0) {
      console.log('No pending dependencies found to update.');
      return;
    }
    
    // Log the pending dependencies for reference
    console.log('\nPending dependencies:');
    pendingDependencies.forEach(dep => {
      console.log(`- ID: ${dep.id}, Title: ${dep.title || dep.dependency_type}, Product Iteration: ${dep.product_iteration_id}`);
    });
    
    // Step 3: Get unique product iteration IDs
    const productIterationIds = [...new Set(pendingDependencies
      .filter(dep => dep.product_iteration_id) // Filter out any null product_iteration_id
      .map(dep => dep.product_iteration_id))];
    
    console.log(`\nFound ${productIterationIds.length} unique product iterations to update`);
    
    if (productIterationIds.length === 0) {
      console.log('No valid product iterations found to update.');
      return;
    }
    
    // Step 4: Check current state of product iterations
    console.log("\nStep 3: Checking current state of product iterations...");
    const { data: currentIterations, error: checkError } = await supabase
      .from('product_iterations')
      .select('id, product_owner_id, product_manager_id')
      .in('id', productIterationIds);
      
    if (checkError) {
      console.error('Error checking product iterations:', checkError);
      return;
    }
    
    console.log('\nCurrent state of product iterations:');
    currentIterations.forEach(iter => {
      console.log(`- ID: ${iter.id}, Owner: ${iter.product_owner_id || 'Not set'}, Manager: ${iter.product_manager_id || 'Not set'}`);
    });
    
    // Step 5: Update product iterations to set the user as owner
    console.log("\nStep 4: Updating product iterations with your user ID...");
    const { data: updateResult, error: updateError } = await supabase
      .from('product_iterations')
      .update({ product_owner_id: userId })
      .in('id', productIterationIds)
      .select('id, product_owner_id, product_manager_id');
      
    if (updateError) {
      console.error('Error updating product iterations:', updateError);
      return;
    }
    
    console.log(`\nSuccessfully updated ${updateResult.length} product iterations`);
    console.log('Updated product iterations:');
    updateResult.forEach(iter => {
      console.log(`- ID: ${iter.id}, Owner: ${iter.product_owner_id || 'Not set'}, Manager: ${iter.product_manager_id || 'Not set'}`);
    });
    
    console.log('\n=== Fix Complete ===');
    console.log('Your pending dependencies should now appear in the approvals dashboard.');
    console.log('Please refresh the approvals dashboard to see the changes.');
  } catch (error) {
    console.error('Error fixing dependencies:', error);
  }
};

fixDependenciesWithBrowserUser().catch(console.error);
