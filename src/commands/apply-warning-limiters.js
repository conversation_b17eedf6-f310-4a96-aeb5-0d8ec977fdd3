/**
 * <PERSON><PERSON><PERSON> to apply warning limiters to reduce console noise
 * 
 * This script:
 * 1. Copies the reducedWarnings.ts file to the appropriate directory
 * 2. Updates gateway.ts to import and apply the warning limiter
 * 
 * Usage: 
 * node src/commands/apply-warning-limiters.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get current file directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m',
};

console.log(`${colors.magenta}🛠  Applying warning limiters to reduce console noise...${colors.reset}\n`);

// Paths to relevant files
const rootDir = path.join(__dirname, '../..');
const sourceWarningsPath = path.join(rootDir, 'src/integrations/supabase/reducedWarnings.ts');
const sourceGatewayPath = path.join(rootDir, 'src/integrations/supabase/gateway.ts');
const docsPath = path.join(rootDir, 'docs/CONSOLE-WARNING-REDUCTION.md');

// Check if the required files exist
if (!fs.existsSync(sourceGatewayPath)) {
  console.error(`${colors.red}❌ Gateway file not found: ${sourceGatewayPath}${colors.reset}`);
  process.exit(1);
}

// Create the warning reducer file
function createWarningReducerFile() {
  const warningReducerContent = `/**
 * Warning reducer for gateway.ts console warnings
 * 
 * This module adds warning limiters to prevent repetitive console 
 * warnings from flooding the console. It works by keeping track of
 * which warnings have already been shown and suppressing duplicates.
 * 
 * Part of the performance optimization effort to reduce console noise.
 */

// Set to store already shown warnings
const shownWarningsSet = new Set<string>();

// Default warning limit - after this many, stop showing new ones of the same type
const DEFAULT_WARNING_LIMIT = 3;

// Counter for each warning type
const warningCounters: Record<string, number> = {};

/**
 * Limits the number of times a specific warning can be shown
 * @param key Unique identifier for this warning type
 * @param message The warning message to show
 * @param limit Max number of times to show this warning (default: 3)
 * @returns Boolean indicating if the warning was shown (true) or suppressed (false)
 */
export function limitWarning(key: string, message: string, limit: number = DEFAULT_WARNING_LIMIT): boolean {
  // If we've already shown this exact warning, don't show it again
  if (shownWarningsSet.has(key)) {
    return false;
  }
  
  // Increment counter for this warning type
  warningCounters[key] = (warningCounters[key] || 0) + 1;
  
  // If we're under the limit, show the warning
  if (warningCounters[key] <= limit) {
    console.warn(message);
    
    // If we've reached the limit, add a message saying we'll suppress future warnings
    if (warningCounters[key] === limit) {
      console.warn(\`⚠️ Suppressing further "\${key}" warnings to reduce console noise\`);
    }
    
    // Add to set of shown warnings
    shownWarningsSet.add(key);
    return true;
  }
  
  // Over the limit, don't show
  return false;
}

/**
 * Wrapper for iteration ID mismatch warnings specifically
 * @param iterationId The ID that doesn't match
 * @param availableIds Array of available IDs
 * @returns Boolean indicating if warning was shown
 */
export function limitIterationMismatchWarning(
  iterationId: string | undefined, 
  availableIds: string[]
): boolean {
  // Create a key for this specific warning
  const key = \`iteration-mismatch-\${iterationId}\`;
  
  // Create the warning message
  const message = \`No stages match the current iteration ID. Available iteration IDs in stages data: \${availableIds.join(', ')}\`;
  
  // Use the generic limiter with this specific key and message
  return limitWarning(key, message);
}

/**
 * Reset all warning counters and shown warnings
 * Primarily used for testing
 */
export function resetWarningLimiters(): void {
  shownWarningsSet.clear();
  Object.keys(warningCounters).forEach(key => {
    warningCounters[key] = 0;
  });
}

/**
 * Get current warning counts (for debugging)
 * @returns Object with counts for each warning type
 */
export function getWarningCounts(): Record<string, number> {
  return { ...warningCounters };
}

/**
 * Apply a warning limiter to the gateway.ts console warnings
 * This patches the console.warn method to check if warnings should be limited
 */
export function applyGatewayWarningLimiter(): void {
  // Only apply in browser context and if not already applied
  if (typeof window !== "undefined" && !(window as any).__gatewayWarningPatched) {
    const originalConsoleWarn = console.warn;
    
    console.warn = function(...args: any[]) {
      // Check if this is an iteration mismatch warning
      if (
        args[0] &&
        typeof args[0] === "string" &&
        args[0].includes("No stages match the current iteration ID")
      ) {
        // Extract the available IDs from the message
        const matches = args[0].match(/Available iteration IDs in stages data: (.*)/);
        if (matches && matches[1]) {
          const availableIds = matches[1].split(', ');
          
          // Use our limiter
          const shouldShow = limitIterationMismatchWarning("unknown", availableIds);
          
          // Only show if the limiter says we should
          if (!shouldShow) {
            return;
          }
        }
      }
      
      // For all other warnings, proceed normally
      originalConsoleWarn.apply(console, args);
    };
    
    (window as any).__gatewayWarningPatched = true;
  }
}`;

  try {
    if (!fs.existsSync(sourceWarningsPath)) {
      fs.writeFileSync(sourceWarningsPath, warningReducerContent, 'utf8');
      console.log(`${colors.green}✅ Created warning reducer file${colors.reset}`);
    } else {
      console.log(`${colors.yellow}⚠️ Warning reducer file already exists, skipping creation${colors.reset}`);
    }
  } catch (error) {
    console.error(`${colors.red}❌ Failed to create warning reducer file: ${error}${colors.reset}`);
  }
}

// Update gateway.ts to import and apply the warning limiter
function updateGatewayFile() {
  try {
    const gatewayContent = fs.readFileSync(sourceGatewayPath, 'utf8');
    
    // Check if the import already exists
    if (gatewayContent.includes('import { applyGatewayWarningLimiter }')) {
      console.log(`${colors.yellow}⚠️ Gateway file already has warning limiter import, skipping update${colors.reset}`);
      return;
    }
    
    // Find the right place to add the import and application - after AUTH_STORAGE_KEY declaration
    let updatedContent = gatewayContent.replace(
      /const AUTH_STORAGE_KEY = ".*";\s*/,
      `const AUTH_STORAGE_KEY = "stat-linker-custom-auth-token-v1";\n\n// Import warning reducer\nimport { applyGatewayWarningLimiter } from './reducedWarnings';\n\n// Apply warning limiter\napplyGatewayWarningLimiter();\n\n`
    );
    
    fs.writeFileSync(sourceGatewayPath, updatedContent, 'utf8');
    console.log(`${colors.green}✅ Updated gateway file to use warning limiter${colors.reset}`);
  } catch (error) {
    console.error(`${colors.red}❌ Failed to update gateway file: ${error}${colors.reset}`);
  }
}

// Create documentation file
function createDocumentationFile() {
  const documentationContent = `# Console Warning Reduction

## Overview

This document outlines the implementation of a console warning reduction system that prevents repetitive warnings from flooding the browser console. The system is particularly focused on iteration ID mismatch warnings that were causing performance bottlenecks.

## Problem Statement

Console warnings were being generated repeatedly for the same issues, particularly:

1. "No stages match the current iteration ID" warnings when viewing product stages
2. Multiple warnings for the same UUID mismatch issue
3. Repetitive telemetry-related console output

These repetitive warnings:
- Reduced application performance 
- Made debugging more difficult
- Cluttered the console with thousands of similar messages

## Implementation

### 1. Warning Limiter System

We implemented a robust warning limiter system in \`src/integrations/supabase/reducedWarnings.ts\` with the following features:

- **Warning Tracking**: Uses a Set to track which warnings have already been shown
- **Count Limiting**: Limits each warning type to a configurable number of appearances (default: 3)
- **Unique Warning Keys**: Creates unique keys for specific warning scenarios to prevent duplicates
- **Console Integration**: Patches the \`console.warn\` method to intercept and filter warnings

### 2. Special Case Handling for Iteration IDs

For iteration ID mismatch warnings specifically:
- Added smart detection of version format vs. UUID format mismatches
- Implemented auto-mapping from version numbers to UUIDs when possible
- Added intelligent warning suppression after hitting configured limits

### 3. Gateway Integration

The warning reducer is integrated into the Supabase gateway system:
\`\`\`typescript
// Import warning reducer
import { applyGatewayWarningLimiter } from './reducedWarnings';

// Apply warning limiter
applyGatewayWarningLimiter();
\`\`\`

## Benefits

1. **Reduced Console Noise**: Warnings now only appear a limited number of times
2. **Performance Improvement**: Significantly less JavaScript execution for warning handling
3. **Better Debugging**: Important warnings remain visible without being drowned out
4. **Auto-Recovery**: For some scenarios like version ID mapping, the system can now recover automatically

## Usage

The warning limiter system is automatically applied when the application starts. No manual activation is needed.

To access the warning limiter functions directly:

\`\`\`typescript
import { limitWarning, limitIterationMismatchWarning } from '@/integrations/supabase/reducedWarnings';

// Limit a general warning
limitWarning('unique-key', 'Your warning message', 3); // Shows max 3 times

// Limit an iteration mismatch warning specifically
limitIterationMismatchWarning(iterationId, availableIds);
\`\`\`

## Related Performance Improvements

This change works alongside other performance improvements:
- Disabled telemetry by default
- Stub implementation for gamification
- Migration from deprecated hooks
- RPC function client-side fallbacks

Together, these changes significantly improve console clarity and application performance.

## Future Considerations

For further improvements, consider:
1. Adding a user-configurable warning level setting
2. Implementing a UI indicator when warnings are being suppressed
3. Creating a debug panel to view all warnings, even suppressed ones`;

  try {
    if (!fs.existsSync(docsPath)) {
      fs.writeFileSync(docsPath, documentationContent, 'utf8');
      console.log(`${colors.green}✅ Created documentation file${colors.reset}`);
    } else {
      console.log(`${colors.yellow}⚠️ Documentation file already exists, skipping creation${colors.reset}`);
    }
  } catch (error) {
    console.error(`${colors.red}❌ Failed to create documentation file: ${error}${colors.reset}`);
  }
}

// Update performance fixes summary to mention console warning reduction
function updatePerformanceFixesSummary() {
  const summaryPath = path.join(rootDir, 'docs/PERFORMANCE-FIXES-SUMMARY.md');
  
  try {
    if (fs.existsSync(summaryPath)) {
      const summaryContent = fs.readFileSync(summaryPath, 'utf8');
      
      // Check if the section already exists
      if (summaryContent.includes('## 6. Console Warning Reduction')) {
        console.log(`${colors.yellow}⚠️ Performance fixes summary already has console warning reduction section, skipping update${colors.reset}`);
        return;
      }
      
      // Find the right place to add the section - before Future Work
      let updatedContent = summaryContent.replace(
        /## Future Work/,
        `## 6. Console Warning Reduction

A warning reduction system has been implemented to prevent console spamming:

- **Warning Limiter**: Tracks and limits repeated warnings to a configurable number
- **Iteration ID Mapping**: Smart handling for version format vs. UUID format mismatches
- **Auto-Recovery**: Automatic mapping from version numbers to UUIDs when possible
- **Gateway Integration**: Seamlessly integrated with the Supabase gateway

See [CONSOLE-WARNING-REDUCTION.md](./CONSOLE-WARNING-REDUCTION.md) for detailed information.

## Future Work`
      );
      
      // Update the Future Work section to mention console warning opportunities
      updatedContent = updatedContent.replace(
        /- Implementing more comprehensive error handling throughout the application\s*(\n|$)/,
        '- Implementing more comprehensive error handling throughout the application\n- Exploring additional warning reduction opportunities in other system components\n'
      );
      
      fs.writeFileSync(summaryPath, updatedContent, 'utf8');
      console.log(`${colors.green}✅ Updated performance fixes summary${colors.reset}`);
    } else {
      console.log(`${colors.yellow}⚠️ Performance fixes summary doesn't exist, skipping update${colors.reset}`);
    }
  } catch (error) {
    console.error(`${colors.red}❌ Failed to update performance fixes summary: ${error}${colors.reset}`);
  }
}

// Main execution
try {
  createWarningReducerFile();
  updateGatewayFile();
  createDocumentationFile();
  updatePerformanceFixesSummary();
  console.log(`\n${colors.green}✅ Successfully applied warning limiters${colors.reset}`);
} catch (error) {
  console.error(`\n${colors.red}❌ Error applying warning limiters: ${error}${colors.reset}`);
  process.exit(1);
}
