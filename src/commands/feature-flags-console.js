/**
 * Feature Flags Console <PERSON>
 * 
 * This script contains browser console commands to easily manage feature flags
 * Copy and paste these functions to the browser console to use them
 */

// Enable all feature flags immediately
function enableAllFeatureFlags() {
  const flags = {
    useCustomWorkflowStages: true,
    useSwimlanes: true,
    useWipLimits: true,
    useAgingIndicators: true
  };
  
  // Save to localStorage with both keys for compatibility
  localStorage.setItem('feature_flags_cache', JSON.stringify(flags));
  localStorage.setItem('feature_flags_override', JSON.stringify(flags));
  
  console.log('%c✓ All feature flags enabled!', 'color: green; font-weight: bold');
  console.table(flags);
  console.log('Refresh the page to see changes.');
  
  return flags;
}

// Disable all feature flags
function disableAllFeatureFlags() {
  const flags = {
    useCustomWorkflowStages: false,
    useSwimlanes: false,
    useWipLimits: false,
    useAgingIndicators: false
  };
  
  // Save to localStorage with both keys for compatibility
  localStorage.setItem('feature_flags_cache', JSON.stringify(flags));
  localStorage.setItem('feature_flags_override', JSON.stringify(flags));
  
  console.log('%c✓ All feature flags disabled!', 'color: red; font-weight: bold');
  console.table(flags);
  console.log('Refresh the page to see changes.');
  
  return flags;
}

// Set individual feature flags
function setFeatureFlags(flags) {
  // Get current flags
  const currentFlags = JSON.parse(localStorage.getItem('feature_flags_cache') || '{}');
  
  // Merge with provided flags
  const updatedFlags = {
    useCustomWorkflowStages: currentFlags.useCustomWorkflowStages || false,
    useSwimlanes: currentFlags.useSwimlanes || true,
    useWipLimits: currentFlags.useWipLimits || false,
    useAgingIndicators: currentFlags.useAgingIndicators || false,
    ...flags
  };
  
  // Save to localStorage with both keys for compatibility
  localStorage.setItem('feature_flags_cache', JSON.stringify(updatedFlags));
  localStorage.setItem('feature_flags_override', JSON.stringify(updatedFlags));
  
  console.log('%c✓ Feature flags updated!', 'color: blue; font-weight: bold');
  console.table(updatedFlags);
  console.log('Refresh the page to see changes.');
  
  return updatedFlags;
}

// Get current feature flags
function getFeatureFlags() {
  const storedFlags = localStorage.getItem('feature_flags_cache') || 
                     localStorage.getItem('feature_flags_override');
  
  if (!storedFlags) {
    console.log('No feature flags found in localStorage.');
    return null;
  }
  
  try {
    const flags = JSON.parse(storedFlags);
    console.log('Current feature flags:');
    console.table(flags);
    return flags;
  } catch (error) {
    console.error('Error parsing feature flags:', error);
    return null;
  }
}

// Clear all feature flags
function clearFeatureFlags() {
  localStorage.removeItem('feature_flags_cache');
  localStorage.removeItem('feature_flags_override');
  console.log('%c✓ All feature flags cleared!', 'color: orange; font-weight: bold');
  console.log('Refresh the page to see changes.');
}

// Usage examples
console.log(`
=============================================
Feature Flags Console Commands
=============================================

Copy and paste these commands to quickly manage feature flags:

// Enable all feature flags
enableAllFeatureFlags();

// Disable all feature flags
disableAllFeatureFlags();

// Enable specific flags
setFeatureFlags({
  useSwimlanes: true,
  useWipLimits: true
});

// Get current flags
getFeatureFlags();

// Clear all flags
clearFeatureFlags();
`);

// Register functions in window (if running in browser)
if (typeof window !== 'undefined') {
  window.enableAllFeatureFlags = enableAllFeatureFlags;
  window.disableAllFeatureFlags = disableAllFeatureFlags;
  window.setFeatureFlags = setFeatureFlags;
  window.getFeatureFlags = getFeatureFlags;
  window.clearFeatureFlags = clearFeatureFlags;
}
