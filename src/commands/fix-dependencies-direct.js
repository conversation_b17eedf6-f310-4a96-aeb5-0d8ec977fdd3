// <PERSON><PERSON>t to directly fix dependencies without requiring authentication
// Usage: node src/commands/fix-dependencies-direct.js YOUR_USER_ID

import { createClient } from '@supabase/supabase-js';

// Define Supabase client directly to avoid TypeScript import issues
const SUPABASE_URL = "https://wgsnpiskyczxhojlrwtr.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.qjE4Z2CwDpXW4C5VZ5tHrhLQV2uK6ut8Mnrgr0snhwc";

const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

const fixDependenciesDirect = async (userId) => {
  console.log(`=== Dependency Fix Tool ===`);
  console.log(`Using user ID: ${userId}`);
  
  try {
    // Step 1: Find all pending dependencies
    console.log("\nStep 1: Finding all pending dependencies...");
    const { data: pendingDependencies, error: dependenciesError } = await supabase
      .from('product_iteration_dependencies')
      .select('id, product_iteration_id, title, dependency_type, approval_status')
      .eq('approval_status', 'pending');
      
    if (dependenciesError) {
      console.error('Error fetching pending dependencies:', dependenciesError);
      return;
    }
    
    console.log(`Found ${pendingDependencies.length} pending dependencies`);
    
    if (pendingDependencies.length === 0) {
      console.log('No pending dependencies found to update.');
      return;
    }
    
    // Log the pending dependencies for reference
    console.log('\nPending dependencies:');
    pendingDependencies.forEach(dep => {
      console.log(`- ID: ${dep.id}, Title: ${dep.title || dep.dependency_type}, Product Iteration: ${dep.product_iteration_id}`);
    });
    
    // Step 2: Get unique product iteration IDs
    const productIterationIds = [...new Set(pendingDependencies
      .filter(dep => dep.product_iteration_id) // Filter out any null product_iteration_id
      .map(dep => dep.product_iteration_id))];
    
    console.log(`\nFound ${productIterationIds.length} unique product iterations to update`);
    
    if (productIterationIds.length === 0) {
      console.log('No valid product iterations found to update.');
      return;
    }
    
    // Step 3: Check current state of product iterations
    console.log("\nStep 2: Checking current state of product iterations...");
    const { data: currentIterations, error: checkError } = await supabase
      .from('product_iterations')
      .select('id, product_owner_id, product_manager_id')
      .in('id', productIterationIds);
      
    if (checkError) {
      console.error('Error checking product iterations:', checkError);
      return;
    }
    
    console.log('\nCurrent state of product iterations:');
    currentIterations.forEach(iter => {
      console.log(`- ID: ${iter.id}, Owner: ${iter.product_owner_id || 'Not set'}, Manager: ${iter.product_manager_id || 'Not set'}`);
    });
    
    // Step 4: Update product iterations to set the user as owner
    console.log("\nStep 3: Updating product iterations...");
    const { data: updateResult, error: updateError } = await supabase
      .from('product_iterations')
      .update({ product_owner_id: userId })
      .in('id', productIterationIds)
      .select('id, product_owner_id, product_manager_id');
      
    if (updateError) {
      console.error('Error updating product iterations:', updateError);
      return;
    }
    
    console.log(`\nSuccessfully updated ${updateResult.length} product iterations`);
    console.log('Updated product iterations:');
    updateResult.forEach(iter => {
      console.log(`- ID: ${iter.id}, Owner: ${iter.product_owner_id || 'Not set'}, Manager: ${iter.product_manager_id || 'Not set'}`);
    });
    
    console.log('\n=== Fix Complete ===');
    console.log('Your pending dependencies should now appear in the approvals dashboard.');
    console.log('Please refresh the approvals dashboard to see the changes.');
  } catch (error) {
    console.error('Error fixing dependencies:', error);
  }
};

// Get user ID from command line argument
const userId = process.argv[2];

if (!userId) {
  console.error('\nError: Please provide your user ID as a command line argument');
  console.error('Usage: node src/commands/fix-dependencies-direct.js YOUR_USER_ID');
  console.error('\nYou can find your user ID in the browser console by running:');
  console.error('console.log(JSON.parse(localStorage.getItem("supabase.auth.token")).currentSession.user.id)');
  process.exit(1);
}

fixDependenciesDirect(userId).catch(console.error);
