// <PERSON>ript to diagnose URL parsing issues
// Usage: node src/commands/diagnose-url-parsing.js

// Function to parse a URL and extract the product ID and iteration parameters
const parseUrl = (url) => {
  console.log(`\nParsing URL: ${url}`);
  
  try {
    // Create a URL object to parse the URL
    const parsedUrl = new URL(url, 'http://localhost');
    
    // Extract the path segments
    const pathSegments = parsedUrl.pathname.split('/').filter(segment => segment);
    
    // Check if the URL follows the expected pattern: /products/{productId}
    if (pathSegments.length >= 2 && pathSegments[0] === 'products') {
      const productId = pathSegments[1];
      console.log(`Product ID: ${productId}`);
      
      // Extract the iteration parameter from the search params
      const searchParams = new URLSearchParams(parsedUrl.search);
      const iteration = searchParams.get('iteration');
      console.log(`Iteration: ${iteration || 'None'}`);
      
      return { productId, iteration };
    } else {
      console.error('URL does not follow the expected pattern: /products/{productId}');
      console.log('Path segments:', pathSegments);
      return { productId: null, iteration: null };
    }
  } catch (error) {
    console.error('Error parsing URL:', error.message);
    return { productId: null, iteration: null };
  }
};

// Function to simulate how React Router would parse a URL
const simulateReactRouter = (url) => {
  console.log(`\nSimulating React Router parsing for URL: ${url}`);
  
  try {
    // Create a URL object to parse the URL
    const parsedUrl = new URL(url, 'http://localhost');
    
    // Extract the path segments
    const pathSegments = parsedUrl.pathname.split('/').filter(segment => segment);
    
    // React Router useParams would extract the productId from the path
    let productId = null;
    if (pathSegments.length >= 2 && pathSegments[0] === 'products') {
      productId = pathSegments[1];
    }
    
    // React Router useSearchParams would extract the iteration from the query string
    const searchParams = new URLSearchParams(parsedUrl.search);
    const iteration = searchParams.get('iteration');
    
    console.log('React Router parsing result:');
    console.log(`- useParams: { productId: ${productId ? `"${productId}"` : 'undefined'} }`);
    console.log(`- useSearchParams: iteration = ${iteration ? `"${iteration}"` : 'null'}`);
    
    return { productId, iteration };
  } catch (error) {
    console.error('Error simulating React Router parsing:', error.message);
    return { productId: null, iteration: null };
  }
};

// Function to check if a URL is malformed
const checkMalformedUrl = (url) => {
  console.log(`\nChecking if URL is malformed: ${url}`);
  
  try {
    // Create a URL object to parse the URL
    const parsedUrl = new URL(url, 'http://localhost');
    
    // Extract the path segments
    const pathSegments = parsedUrl.pathname.split('/').filter(segment => segment);
    
    // Check if the URL follows the expected pattern: /products/{productId}
    if (pathSegments.length < 2) {
      console.error('URL is malformed: Missing path segments');
      return true;
    }
    
    if (pathSegments[0] !== 'products') {
      console.error('URL is malformed: First path segment is not "products"');
      return true;
    }
    
    if (!pathSegments[1]) {
      console.error('URL is malformed: Missing product ID');
      return true;
    }
    
    // Check if the product ID is a valid UUID
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(pathSegments[1])) {
      console.warn('Warning: Product ID is not a valid UUID');
    }
    
    // Check if the iteration parameter is present
    const searchParams = new URLSearchParams(parsedUrl.search);
    const iteration = searchParams.get('iteration');
    if (iteration === '') {
      console.error('URL is malformed: Iteration parameter is empty');
      return true;
    }
    
    console.log('URL is well-formed');
    return false;
  } catch (error) {
    console.error('Error checking URL:', error.message);
    return true;
  }
};

// Function to suggest a fix for a malformed URL
const suggestUrlFix = (url) => {
  console.log(`\nSuggesting fix for URL: ${url}`);
  
  try {
    // Create a URL object to parse the URL
    const parsedUrl = new URL(url, 'http://localhost');
    
    // Extract the path segments
    const pathSegments = parsedUrl.pathname.split('/').filter(segment => segment);
    
    // Extract the search params
    const searchParams = new URLSearchParams(parsedUrl.search);
    const iteration = searchParams.get('iteration');
    
    // Check if the URL is missing the product ID
    if (pathSegments.length < 2 || pathSegments[0] !== 'products' || !pathSegments[1]) {
      console.error('URL is missing the product ID');
      
      // If we have an iteration parameter but no product ID, suggest a fix
      if (iteration) {
        const suggestedUrl = `/products/PRODUCT_ID?iteration=${iteration}`;
        console.log(`Suggested fix: ${suggestedUrl}`);
        console.log('Replace PRODUCT_ID with a valid product ID');
        return suggestedUrl;
      }
      
      // If we don't have an iteration parameter, suggest a fix
      const suggestedUrl = '/products/PRODUCT_ID';
      console.log(`Suggested fix: ${suggestedUrl}`);
      console.log('Replace PRODUCT_ID with a valid product ID');
      return suggestedUrl;
    }
    
    // If the URL is well-formed, return it as is
    console.log('URL is well-formed, no fix needed');
    return url;
  } catch (error) {
    console.error('Error suggesting URL fix:', error.message);
    return '/products';
  }
};

// Main function
const main = () => {
  console.log('URL Parsing Diagnostics Tool');
  console.log('===========================');
  
  // Test URLs
  const testUrls = [
    '/products/bbe88976-5283-4752-b8f0-b307fcaf4fb7',
    '/products/bbe88976-5283-4752-b8f0-b307fcaf4fb7?iteration=2.0',
    '/products/?iteration=2.0',
    '/products?iteration=2.0',
    '/products/undefined?iteration=2.0',
    '/products/null?iteration=2.0',
    '/products/bbe88976-5283-4752-b8f0-b307fcaf4fb7?iteration=',
    '/products/bbe88976-5283-4752-b8f0-b307fcaf4fb7?iteration',
    '/products/bbe88976-5283-4752-b8f0-b307fcaf4fb7?',
    '/products',
    '/products/',
  ];
  
  // Test each URL
  testUrls.forEach(url => {
    parseUrl(url);
    simulateReactRouter(url);
    const isMalformed = checkMalformedUrl(url);
    if (isMalformed) {
      suggestUrlFix(url);
    }
    console.log('---------------------------------------------------');
  });
  
  // Test the specific URL from the user's issue
  console.log('\nTesting the specific URL from the user\'s issue:');
  console.log('==============================================');
  const problemUrl = '/products/bbe88976-5283-4752-b8f0-b307fcaf4fb7?iteration=2.0';
  parseUrl(problemUrl);
  simulateReactRouter(problemUrl);
  const isMalformed = checkMalformedUrl(problemUrl);
  if (isMalformed) {
    suggestUrlFix(problemUrl);
  }
};

// Run the main function
main();
