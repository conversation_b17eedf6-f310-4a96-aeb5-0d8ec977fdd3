// Script to fix the approval system
// Usage: node src/commands/fix-approval-system.js

import { createClient } from '@supabase/supabase-js';

// Define Supabase client
// Get Supabase connection info from environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_KEY;

// Ensure environment variables are set
if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Missing Supabase environment variables.');
  console.error('Make sure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in your environment.');
  console.error('Create a .env file based on .env.example or set these variables in your environment.');
  process.exit(1);
}
const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

async function fixApprovalSystem() {
  console.log("=== Fixing Approval System ===");
  
  try {
    // 1. Check for pending dependencies
    console.log("Checking for pending dependencies...");
    const { data: pendingDependencies, error: pendingError } = await supabase
      .from('product_iteration_dependencies')
      .select(`
        id,
        title,
        details,
        dependency_type,
        approval_status,
        created_at,
        product_iteration_id,
        dependent_on_product_id,
        product_iterations:product_iteration_id (
          id, 
          product_owner_id, 
          product_manager_id, 
          product_base_id,
          version,
          iteration
        ),
        dependent_products:dependent_on_product_id (
          id,
          name
        )
      `)
      .eq('approval_status', 'pending');
      
    if (pendingError) {
      console.error("Error checking pending dependencies:", pendingError);
      return;
    }
    
    console.log(`Found ${pendingDependencies?.length || 0} pending dependencies`);
    
    // 2. Check for dependencies with missing product owner or manager
    const dependenciesWithMissingOwner = pendingDependencies?.filter(dep => {
      const productIterations = Array.isArray(dep.product_iterations) 
        ? dep.product_iterations[0] 
        : dep.product_iterations;
      
      return !productIterations || 
        (!productIterations.product_owner_id && !productIterations.product_manager_id);
    }) || [];
    
    console.log(`Found ${dependenciesWithMissingOwner.length} dependencies with missing product owner or manager`);
    
    // 3. Fix dependencies with missing product owner or manager
    if (dependenciesWithMissingOwner.length > 0) {
      console.log("Fixing dependencies with missing product owner or manager...");
      
      for (const dep of dependenciesWithMissingOwner) {
        const productIterations = Array.isArray(dep.product_iterations) 
          ? dep.product_iterations[0] 
          : dep.product_iterations;
        
        if (!productIterations) {
          console.log(`Dependency ${dep.id} has no product iteration, skipping...`);
          continue;
        }
        
        // Get the first user from the system to use as a fallback product owner
        const { data: users, error: usersError } = await supabase
          .from('profiles')
          .select('id')
          .limit(1);
          
        if (usersError || !users || users.length === 0) {
          console.error("Error getting fallback user:", usersError);
          continue;
        }
        
        const fallbackUserId = users[0].id;
        
        // Update the product iteration with the fallback user as product owner
        const { error: updateError } = await supabase
          .from('product_iterations')
          .update({
            product_owner_id: fallbackUserId
          })
          .eq('id', productIterations.id);
          
        if (updateError) {
          console.error(`Error updating product iteration ${productIterations.id}:`, updateError);
        } else {
          console.log(`Fixed product iteration ${productIterations.id} by setting product_owner_id to ${fallbackUserId}`);
        }
      }
    }
    
    // 4. Clear the approval cache
    console.log("Clearing approval cache...");
    console.log("Note: The approval cache is stored in the browser and will be automatically cleared when the user refreshes the page.");
    console.log("To force a cache refresh, you can add a button to the UI that calls approvalService.refreshData()");
    
    // 5. Create a fix for the approval cache in useApprovalCache.ts
    console.log("\nTo fix the approval cache permanently, you need to modify the useApprovalCache.ts file:");
    console.log("1. Remove the filtering for owner/manager IDs in the useApprovalCache hook");
    console.log("2. Add a force refresh mechanism to the PendingApprovals component");
    console.log("3. Ensure the cache is invalidated when the user navigates to the approvals page");
    
    console.log("\nFix completed. Please refresh the approvals page to see the changes.");
    
  } catch (error) {
    console.error("Error fixing approval system:", error);
  }
}

// Run the fix
fixApprovalSystem().catch(console.error);
