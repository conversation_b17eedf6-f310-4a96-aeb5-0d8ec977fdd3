import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

// Initialize dotenv
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Supabase URL or Key is missing in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Main function to set up gamification tables
 */
async function setupGamificationTables() {
  console.log('Setting up gamification system tables...');

  try {
    // Check connection
    await checkConnection();
    
    // Create tables using direct table operations
    await createUserProfilesTable();
    await createAchievementsTable();
    await createUserAchievementsTable();
    await createGamificationEventsTable();
    await createGamificationConfigTable();
    await createGamificationRulesTable();
    
    // Set up initial data
    await setupInitialAchievements();
    await setupInitialConfig();
    
    console.log('✅ Gamification tables setup complete!');
    return true;
  } catch (err) {
    console.error('Error setting up gamification tables:', err);
    return false;
  }
}

/**
 * Check database connection
 */
async function checkConnection() {
  try {
    const { data, error } = await supabase.from('user_gamification_profiles').select('id').limit(1);
    
    if (error && !error.message.includes('relation "user_gamification_profiles" does not exist')) {
      console.warn('Warning: Connection issue. Check your Supabase credentials.');
      console.warn(error.message);
    } else {
      console.log('Supabase connection successful');
    }
  } catch (err) {
    console.warn('Warning: Connection test failed. Check your Supabase credentials.');
    console.warn(err.message);
  }
}

/**
 * Create user profiles table
 */
async function createUserProfilesTable() {
  console.log('Creating user_gamification_profiles table...');
  
  // First check if the table already exists
  const { error: checkError } = await supabase.from('user_gamification_profiles').select('id').limit(1);
  
  if (!checkError || !checkError.message.includes('relation "user_gamification_profiles" does not exist')) {
    console.log('✓ user_gamification_profiles table already exists');
    return;
  }
  
  // Create table using the SQL operation
  const { error } = await supabase.from('user_gamification_profiles').insert({
    id: '00000000-0000-0000-0000-000000000000', // Dummy row to create table
    points: 0,
    level: 1
  });
  
  if (error && !error.message.includes('already exists')) {
    console.error(`Error creating user_gamification_profiles table: ${error.message}`);
    return;
  }
  
  console.log('✓ user_gamification_profiles table created successfully');
}

/**
 * Create achievements table
 */
async function createAchievementsTable() {
  console.log('Creating achievements table...');
  
  // First check if the table already exists
  const { error: checkError } = await supabase.from('achievements').select('id').limit(1);
  
  if (!checkError || !checkError.message.includes('relation "achievements" does not exist')) {
    console.log('✓ achievements table already exists');
    return;
  }
  
  // Create table using the SQL operation
  const { error } = await supabase.from('achievements').insert({
    name: 'Dummy Achievement',
    description: 'This is a dummy achievement to create the table',
    icon: 'trophy',
    points: 0,
    category: 'system'
  });
  
  if (error && !error.message.includes('already exists')) {
    console.error(`Error creating achievements table: ${error.message}`);
    return;
  }
  
  console.log('✓ achievements table created successfully');
}

/**
 * Create user achievements table (junction table)
 */
async function createUserAchievementsTable() {
  console.log('Creating user_achievements table...');
  
  // First check if the table already exists
  const { error: checkError } = await supabase.from('user_achievements').select('id').limit(1);
  
  if (!checkError || !checkError.message.includes('relation "user_achievements" does not exist')) {
    console.log('✓ user_achievements table already exists');
    return;
  }
  
  // Since this is a junction table, we need to make sure the referenced tables exist
  const { error: achievementsError } = await supabase.from('achievements').select('id').limit(1);
  if (achievementsError && achievementsError.message.includes('relation "achievements" does not exist')) {
    console.error('Error: achievements table must exist before creating user_achievements');
    return;
  }
  
  // Create the table through direct insert
  const { error } = await supabase.from('user_achievements').insert({
    id: '00000000-0000-0000-0000-000000000000', // Dummy row
    user_id: '00000000-0000-0000-0000-000000000000', // Dummy user ID
    achievement_id: '00000000-0000-0000-0000-000000000000' // Dummy achievement ID
  });
  
  if (error && !error.message.includes('already exists') && !error.message.includes('foreign key constraint')) {
    console.error(`Error creating user_achievements table: ${error.message}`);
    return;
  }
  
  console.log('✓ user_achievements table created successfully');
}

/**
 * Create gamification events table
 */
async function createGamificationEventsTable() {
  console.log('Creating gamification_events table...');
  
  // First check if the table already exists
  const { error: checkError } = await supabase.from('gamification_events').select('id').limit(1);
  
  if (!checkError || !checkError.message.includes('relation "gamification_events" does not exist')) {
    console.log('✓ gamification_events table already exists');
    return;
  }
  
  // Create the table through direct insert
  const { error } = await supabase.from('gamification_events').insert({
    id: '00000000-0000-0000-0000-000000000000', // Dummy row
    user_id: '00000000-0000-0000-0000-000000000000', // Dummy user ID
    event_type: 'dummy_event'
  });
  
  if (error && !error.message.includes('already exists') && !error.message.includes('foreign key constraint')) {
    console.error(`Error creating gamification_events table: ${error.message}`);
    return;
  }
  
  console.log('✓ gamification_events table created successfully');
}

/**
 * Create gamification config table
 */
async function createGamificationConfigTable() {
  console.log('Creating gamification_config table...');
  
  // First check if the table already exists
  const { error: checkError } = await supabase.from('gamification_config').select('id').limit(1);
  
  if (!checkError || !checkError.message.includes('relation "gamification_config" does not exist')) {
    console.log('✓ gamification_config table already exists');
    return;
  }
  
  // Create the table through direct insert
  const { error } = await supabase.from('gamification_config').insert({
    key: 'dummy_config',
    value: { enabled: true }
  });
  
  if (error && !error.message.includes('already exists')) {
    console.error(`Error creating gamification_config table: ${error.message}`);
    return;
  }
  
  console.log('✓ gamification_config table created successfully');
}

/**
 * Create gamification rules table
 */
async function createGamificationRulesTable() {
  console.log('Creating gamification_rules table...');
  
  // First check if the table already exists
  const { error: checkError } = await supabase.from('gamification_rules').select('id').limit(1);
  
  if (!checkError || !checkError.message.includes('relation "gamification_rules" does not exist')) {
    console.log('✓ gamification_rules table already exists');
    return;
  }
  
  // Create the table through direct insert
  const { error } = await supabase.from('gamification_rules').insert({
    name: 'Dummy Rule',
    conditions: { event_type: 'dummy_event', count: 1 },
    actions: [{ type: 'add_points', points: 0 }]
  });
  
  if (error && !error.message.includes('already exists')) {
    console.error(`Error creating gamification_rules table: ${error.message}`);
    return;
  }
  
  console.log('✓ gamification_rules table created successfully');
}

/**
 * Insert initial achievements
 */
async function setupInitialAchievements() {
  console.log('Setting up initial achievements...');
  
  // First, check if achievements already exist
  const { data: existingAchievements, error: checkError } = await supabase
    .from('achievements')
    .select('id')
    .limit(1);
  
  if (checkError) {
    console.error(`Failed to check existing achievements: ${checkError.message}`);
    return;
  }
  
  if (existingAchievements && existingAchievements.length > 0) {
    console.log('Achievements already exist, ensuring all required ones are present');
  }
  
  // Define initial achievements
  const initialAchievements = [
    {
      id: 'welcome_aboard',
      name: 'Welcome Aboard',
      name_ar: 'مرحبًا بك',
      description: 'Created your account and joined the system',
      description_ar: 'قمت بإنشاء حسابك والانضمام إلى النظام',
      icon: 'Award',
      points: 10,
      category: 'onboarding',
    },
    {
      id: 'first_product_view',
      name: 'First Product View',
      name_ar: 'أول مشاهدة للمنتج',
      description: 'Viewed a product for the first time',
      description_ar: 'قمت بمشاهدة منتج لأول مرة',
      icon: 'Eye',
      points: 5,
      category: 'products',
    },
    {
      id: 'explorer',
      name: 'Explorer',
      name_ar: 'مستكشف',
      description: 'Visited 5 different pages in the system',
      description_ar: 'قمت بزيارة 5 صفحات مختلفة في النظام',
      icon: 'Compass',
      points: 20,
      category: 'navigation',
    },
    {
      id: 'creator',
      name: 'Contributor',
      name_ar: 'مساهم',
      description: 'Created your first product',
      description_ar: 'قمت بإنشاء منتجك الأول',
      icon: 'Plus',
      points: 50,
      category: 'products',
    },
    {
      id: 'team_player',
      name: 'Team Player',
      name_ar: 'لاعب فريق',
      description: 'Collaborated on a product with others',
      description_ar: 'تعاونت على منتج مع الآخرين',
      icon: 'Users',
      points: 30,
      category: 'collaboration',
    },
    {
      id: 'profile_completer',
      name: 'Profile Completer',
      name_ar: 'مكمل الملف الشخصي',
      description: 'Completed your profile information',
      description_ar: 'أكملت معلومات ملفك الشخصي',
      icon: 'User',
      points: 15,
      category: 'profile',
    },
    {
      id: 'product_master',
      name: 'Product Master',
      name_ar: 'سيد المنتجات',
      description: 'Viewed 20 different products',
      description_ar: 'قمت بمشاهدة 20 منتجًا مختلفًا',
      icon: 'Star',
      points: 100,
      category: 'products',
    }
  ];
  
  // Insert the achievements
  for (const achievement of initialAchievements) {
    const { error: insertError } = await supabase
      .from('achievements')
      .upsert(achievement, {
        onConflict: 'id',
        ignoreDuplicates: false
      });
    
    if (insertError) {
      console.error(`Failed to insert achievement "${achievement.name}": ${insertError.message}`);
    } else {
      console.log(`✓ Achievement "${achievement.name}" inserted or updated successfully`);
    }
  }
}

/**
 * Setup initial config
 */
async function setupInitialConfig() {
  console.log('Setting up initial configuration...');
  
  // Define initial config values
  const initialConfig = [
    {
      key: 'gamification_enabled',
      category: 'system',
      value: { enabled: true },
      description: 'Master switch for the gamification system',
    },
    {
      key: 'level_formula',
      category: 'points',
      value: { formula: 'Math.floor(Math.sqrt(points / 100)) + 1' },
      description: 'Formula to calculate user level based on points',
    },
    {
      key: 'achievement_notification',
      category: 'notifications',
      value: { enabled: true },
      description: 'Show notifications when users earn achievements',
    },
    {
      key: 'level_up_notification',
      category: 'notifications',
      value: { enabled: true },
      description: 'Show notifications when users level up',
    },
    {
      key: 'points_notification',
      category: 'notifications',
      value: { enabled: true, threshold: 10 },
      description: 'Show notifications when users earn points (with minimum threshold)',
    },
    {
      key: 'leaderboard_enabled',
      category: 'social',
      value: { enabled: true },
      description: 'Enable or disable leaderboard functionality',
    },
  ];
  
  // Insert the config
  for (const config of initialConfig) {
    const { error: insertError } = await supabase
      .from('gamification_config')
      .upsert(config, {
        onConflict: 'key',
        ignoreDuplicates: false
      });
    
    if (insertError) {
      console.error(`Failed to insert config "${config.key}": ${insertError.message}`);
    } else {
      console.log(`✓ Config "${config.key}" inserted or updated successfully`);
    }
  }
}

// Run the setup if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  setupGamificationTables()
    .then(success => {
      if (success) {
        console.log('✅ Gamification tables setup completed successfully.');
        process.exit(0);
      } else {
        console.error('❌ Gamification tables setup failed.');
        process.exit(1);
      }
    })
    .catch(err => {
      console.error('Fatal error:', err);
      process.exit(1);
    });
}

export { setupGamificationTables };
