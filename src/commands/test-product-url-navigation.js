// Script to test product URL navigation
// Usage: node src/commands/test-product-url-navigation.js

import { createClient } from '@supabase/supabase-js';

// Define Supabase client directly to avoid TypeScript import issues
// Get Supabase connection info from environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_KEY;

// Ensure environment variables are set
if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Missing Supabase environment variables.');
  console.error('Make sure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in your environment.');
  console.error('Create a .env file based on .env.example or set these variables in your environment.');
  process.exit(1);
}
const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

// Function to get a list of products with their iterations
const getProductsWithIterations = async () => {
  console.log('Fetching products with iterations...');
  
  try {
    const { data: productBases, error: basesError } = await supabase
      .from('product_bases')
      .select(`
        id,
        name,
        iterations:product_iterations!product_iterations_product_base_id_fkey(
          id,
          version,
          iteration
        )
      `)
      .limit(5); // Limit to 5 products for testing
    
    if (basesError) {
      console.error('Error fetching products:', basesError);
      throw basesError;
    }
    
    return productBases;
  } catch (error) {
    console.error('Error in getProductsWithIterations:', error);
    throw error;
  }
};

// Function to simulate URL navigation
const simulateNavigation = (url) => {
  console.log(`\nSimulating navigation to: ${url}`);
  
  // Extract the product ID from the URL
  const productIdMatch = url.match(/\/products\/([^/?]+)/);
  const productId = productIdMatch ? productIdMatch[1] : null;
  
  // Extract the iteration parameter from the URL
  const iterationMatch = url.match(/[?&]iteration=([^&]+)/);
  const iteration = iterationMatch ? iterationMatch[1] : null;
  
  console.log(`Extracted productId: ${productId || 'null'}`);
  console.log(`Extracted iteration: ${iteration || 'null'}`);
  
  return { productId, iteration };
};

// Function to check if a product exists in the database
const checkProductExists = async (productId) => {
  if (!productId) {
    console.error('Cannot check if product exists: Product ID is missing');
    return false;
  }
  
  console.log(`Checking if product ${productId} exists...`);
  
  try {
    const { data, error } = await supabase
      .from('product_bases')
      .select('id, name')
      .eq('id', productId)
      .single();
      
    if (error) {
      console.error(`Error checking product existence: ${error.message}`);
      if (error.code === 'PGRST116') {
        console.error(`Product with ID ${productId} does not exist in the database`);
        return false;
      }
      throw error;
    }
    
    if (!data) {
      console.error(`Product with ID ${productId} not found`);
      return false;
    }
    
    console.log(`Product exists: ${data.name} (${data.id})`);
    return true;
  } catch (err) {
    console.error(`Error checking product: ${err.message}`);
    return false;
  }
};

// Function to check if an iteration exists for a product
const checkIterationExists = async (productId, iterationVersion) => {
  if (!productId) {
    console.error('Cannot check if iteration exists: Product ID is missing');
    return false;
  }
  
  if (!iterationVersion) {
    console.error('Cannot check if iteration exists: Iteration version is missing');
    return false;
  }
  
  console.log(`Checking if iteration ${iterationVersion} exists for product ${productId}...`);
  
  try {
    const { data, error } = await supabase
      .from('product_iterations')
      .select('id, version, iteration')
      .eq('product_base_id', productId)
      .or(`version.eq.${iterationVersion},iteration.eq.${iterationVersion}`);
      
    if (error) {
      console.error(`Error checking iteration existence: ${error.message}`);
      throw error;
    }
    
    if (!data || data.length === 0) {
      console.error(`Iteration ${iterationVersion} not found for product ${productId}`);
      return false;
    }
    
    console.log(`Iteration exists: ${data[0].version || data[0].iteration} (${data[0].id})`);
    return true;
  } catch (err) {
    console.error(`Error checking iteration: ${err.message}`);
    return false;
  }
};

// Function to simulate the product details page loading
const simulateProductDetailsPageLoading = async (url) => {
  console.log(`\nSimulating ProductDetailsPage loading for URL: ${url}`);
  
  // Extract the product ID and iteration from the URL
  const { productId, iteration } = simulateNavigation(url);
  
  // Check if the product ID is missing
  if (!productId) {
    console.error('Product ID is missing from the URL');
    console.error('Expected URL format: /products/{productId}?iteration={iterationValue}');
    return false;
  }
  
  // Check if the product exists in the database
  const productExists = await checkProductExists(productId);
  if (!productExists) {
    console.error('Product does not exist in the database');
    return false;
  }
  
  // If an iteration is specified, check if it exists for the product
  if (iteration) {
    const iterationExists = await checkIterationExists(productId, iteration);
    if (!iterationExists) {
      console.error('Iteration does not exist for the product');
      return false;
    }
  }
  
  console.log('Product details page loaded successfully');
  return true;
};

// Main function
const main = async () => {
  try {
    console.log('Product URL Navigation Test');
    console.log('=========================');
    
    // Test URLs
    const testUrls = [
      '/products/bbe88976-5283-4752-b8f0-b307fcaf4fb7',
      '/products/bbe88976-5283-4752-b8f0-b307fcaf4fb7?iteration=2.0',
      '/products/?iteration=2.0',
      '/products?iteration=2.0',
    ];
    
    // Test each URL
    for (const url of testUrls) {
      const success = await simulateProductDetailsPageLoading(url);
      console.log(`URL ${url} - ${success ? 'SUCCESS' : 'FAILED'}`);
      console.log('---------------------------------------------------');
    }
    
    // Get a list of products with their iterations
    const products = await getProductsWithIterations();
    console.log(`\nFound ${products.length} products`);
    
    // Test navigation to each product
    for (const product of products) {
      console.log(`\nTesting navigation to product: ${product.name} (${product.id})`);
      
      // Test navigation to the product without an iteration
      const url = `/products/${product.id}`;
      const success = await simulateProductDetailsPageLoading(url);
      console.log(`URL ${url} - ${success ? 'SUCCESS' : 'FAILED'}`);
      
      // Test navigation to the product with each iteration
      if (product.iterations && product.iterations.length > 0) {
        for (const iteration of product.iterations) {
          const iterationValue = iteration.iteration || iteration.version;
          if (iterationValue) {
            const url = `/products/${product.id}?iteration=${iterationValue}`;
            const success = await simulateProductDetailsPageLoading(url);
            console.log(`URL ${url} - ${success ? 'SUCCESS' : 'FAILED'}`);
          }
        }
      }
      
      console.log('---------------------------------------------------');
    }
    
    console.log('\nTest complete.');
  } catch (error) {
    console.error('Error in main:', error);
  }
};

// Run the main function
main();
