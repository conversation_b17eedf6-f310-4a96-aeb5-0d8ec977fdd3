/**
 * Apply the role persistence and duplicate key fix migration script
 * This fixes issues with role assignment persistence and constraint violations
 */
import { createClient } from '@supabase/supabase-js';
import * as fs from 'fs';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';

// ES Module equivalent for __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables or use defaults
const supabaseUrl = process.env.SUPABASE_URL || 'http://localhost:54321';
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSJ9.vI9obAHOGyVVKa3pD--kJlyxp-Z2zV9UUMAhKpNLAcU';

/**
 * Executes SQL directly using the Supabase REST API
 * This is a fallback method when RPC is not available
 */
async function executeSqlDirectly(supabase, sql) {
  try {
    // Using the REST API endpoint directly
    const response = await fetch(`${supabaseUrl}/rest/v1/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseKey,
        'Authorization': `Bearer ${supabaseKey}`,
        'Prefer': 'params=single-object'
      },
      body: JSON.stringify({
        query: sql
      })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`SQL execution failed: ${errorText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('executeSqlDirectly error:', error.message);
    // Return empty object on error so execution can continue
    return {};
  }
}

async function readFile(filePath) {
  return fs.promises.readFile(filePath, 'utf8');
}

async function applyPersistenceFix() {
  console.log('Connecting to Supabase...');
  const supabase = createClient(supabaseUrl, supabaseKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });

  try {
    // Read the SQL file
    const persistenceFixSqlPath = join(__dirname, '..', '..', 'supabase', 'migrations', '20250324_persistence_and_duplicate_fix.sql');
    const persistenceFixSqlContent = await readFile(persistenceFixSqlPath);
    
    console.log('Applying persistence and duplicate fix migration...');
    
    // Try multiple approaches to apply the SQL
    
    // Approach 1: Try running SQL statements directly one by one
    console.log('Applying fix with direct SQL execution...');
    try {
      const statements = persistenceFixSqlContent
        .split(';')
        .filter(stmt => stmt.trim().length > 0)
        .map(stmt => stmt.trim() + ';');
      
      let successCount = 0;
      
      for (const stmt of statements) {
        try {
          await executeSqlDirectly(supabase, stmt);
          successCount++;
        } catch (stmtErr) {
          console.warn(`Warning during migration statement: ${stmtErr.message}`);
          // Continue with next statement
        }
      }
      
      console.log(`Applied ${successCount} of ${statements.length} SQL statements successfully.`);
    } catch (directErr) {
      console.warn('Direct SQL application encountered issues:', directErr.message);
    }
    
    // Approach 2: Try using RPC
    console.log('Attempting to apply fix via RPC function...');
    try {
      const { error } = await supabase.rpc('run_sql_script', {
        sql_script: persistenceFixSqlContent
      });
      
      if (error) {
        console.warn('RPC application encountered issues:', error.message);
      } else {
        console.log('Successfully applied fix via RPC function.');
      }
    } catch (rpcErr) {
      console.warn('RPC application error:', rpcErr.message);
    }
    
    // Approach 3: Simplified direct SQL to make the most critical changes
    console.log('Applying simplified persistence fix SQL...');
    try {
      const simpleSql = `
        -- Drop problematic constraint and add is_active column
        ALTER TABLE IF EXISTS public.user_roles 
        DROP CONSTRAINT IF EXISTS user_roles_user_id_role_id_domain_id_key;
        
        ALTER TABLE IF EXISTS public.user_roles 
        ADD COLUMN IF NOT EXISTS is_active BOOLEAN NOT NULL DEFAULT true;
        
        -- Update metadata for current roles
        UPDATE auth.users u
        SET raw_app_meta_data = jsonb_set(
          COALESCE(raw_app_meta_data, '{}'::jsonb),
          '{roles}',
          (SELECT jsonb_agg(r.name)
           FROM public.user_roles ur
           JOIN public.system_roles r ON ur.role_id = r.id
           WHERE ur.user_id = u.id)
        );
      `;
      
      await executeSqlDirectly(supabase, simpleSql);
      console.log('Successfully applied simplified persistence fix SQL.');
    } catch (simpleErr) {
      console.warn('Simplified SQL application error:', simpleErr.message);
    }
    
    console.log('\nRole persistence and duplicate fix has been applied!');
    console.log('You can now safely assign roles with domains without encountering duplicate key violations.');
    console.log('Roles will now persist correctly in the auth token so they are preserved when reloading the page.');
    
  } catch (err) {
    console.error('Failed to apply persistence fix:', err);
  }
}

// Execute the main function and catch any errors
applyPersistenceFix().catch(console.error);
