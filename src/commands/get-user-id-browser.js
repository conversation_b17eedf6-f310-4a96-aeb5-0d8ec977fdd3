// This is a script to help you get your user ID from the browser
// Copy and paste this into your browser console while logged in to the application

console.log("=== User ID Finder ===");
console.log("Checking for Supabase auth data...");

try {
  // Try different storage locations for Supabase auth data
  const storageKeys = [
    "supabase.auth.token",
    "sb-wgsnpiskyczxhojlrwtr-auth-token",
    "supabase.auth.data"
  ];
  
  let authData = null;
  let foundKey = null;
  
  // Try each storage key
  for (const key of storageKeys) {
    const data = localStorage.getItem(key);
    if (data) {
      authData = data;
      foundKey = key;
      break;
    }
  }
  
  if (!authData) {
    console.error("No Supabase auth data found. Please make sure you're logged in.");
    console.error("Try these steps:");
    console.error("1. Refresh the page");
    console.error("2. Log out and log back in");
    console.error("3. Clear your browser cache and try again");
    
    // Try to get user ID from the window object if available
    if (window.supabase && window.supabase.auth && window.supabase.auth.user) {
      const user = window.supabase.auth.user();
      if (user && user.id) {
        console.log("\nFound user ID from Supabase client:");
        console.log(`User ID: ${user.id}`);
        console.log(`Email: ${user.email || 'Unknown'}`);
        
        console.log("\nTo fix your dependencies, run this command in the terminal:");
        console.log(`node src/commands/fix-dependencies-direct.js ${user.id}`);
        
        // Copy to clipboard for convenience
        navigator.clipboard.writeText(user.id)
          .then(() => console.log("\nUser ID copied to clipboard!"))
          .catch(err => console.error("Could not copy to clipboard:", err));
        
        // Skip the rest of the function
        authData = null; // Set to null to skip the next block
        foundKey = null;
      }
    }
    
    console.error("\nAlternatively, you can manually enter a user ID if you know it:");
    console.error("node src/commands/fix-dependencies-direct.js YOUR_USER_ID");
  } else {
    console.log(`Found auth data in localStorage key: ${foundKey}`);
    
    const parsedData = JSON.parse(authData);
    let userId = null;
    let userEmail = null;
    
    // Handle different Supabase auth data formats
    if (parsedData.currentSession && parsedData.currentSession.user) {
      // Format for supabase.auth.token
      userId = parsedData.currentSession.user.id;
      userEmail = parsedData.currentSession.user.email;
    } else if (parsedData.user) {
      // Format for sb-[ref]-auth-token
      userId = parsedData.user.id;
      userEmail = parsedData.user.email;
    } else if (parsedData.session && parsedData.session.user) {
      // Another possible format
      userId = parsedData.session.user.id;
      userEmail = parsedData.session.user.email;
    }
    
    if (userId) {
      console.log("=== User Information ===");
      console.log(`User ID: ${userId}`);
      console.log(`Email: ${userEmail || 'Unknown'}`);
      
      console.log("\nTo fix your dependencies, run this command in the terminal:");
      console.log(`node src/commands/fix-dependencies-direct.js ${userId}`);
      
      // Copy to clipboard for convenience
      navigator.clipboard.writeText(userId)
        .then(() => console.log("\nUser ID copied to clipboard!"))
        .catch(err => console.error("Could not copy to clipboard:", err));
    } else {
      console.error("Auth data found but user ID could not be extracted.");
      console.error("Try logging out and logging back in, then run this script again.");
    }
  }
} catch (error) {
  console.error("Error parsing auth data:", error);
  console.error(error);
}
