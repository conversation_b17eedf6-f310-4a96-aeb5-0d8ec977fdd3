// Script to check if a specific product and iteration exist in Supabase
// Usage: node src/commands/check-product-iteration.js PRODUCT_ID ITERATION_VERSION

import { createClient } from '@supabase/supabase-js';

// Define Supabase client directly to avoid TypeScript import issues
// Get Supabase connection info from environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_KEY;

// Ensure environment variables are set
if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Missing Supabase environment variables.');
  console.error('Make sure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in your environment.');
  console.error('Create a .env file based on .env.example or set these variables in your environment.');
  process.exit(1);
}
const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

const checkProductAndIteration = async (productId, iterationVersion) => {
  console.log(`Checking product ID: ${productId}`);
  
  // Step 1: Check if the product exists
  const { data: productBase, error: productError } = await supabase
    .from('product_bases')
    .select('id, name, name_ar, created_at')
    .eq('id', productId)
    .single();
    
  if (productError) {
    console.error('Error checking product:', productError);
    if (productError.code === 'PGRST116') {
      console.error(`Product with ID ${productId} does not exist in the database.`);
      return;
    }
    return;
  }
  
  if (!productBase) {
    console.log(`Product with ID ${productId} does not exist.`);
    return;
  }
  
  console.log(`Product found:`);
  console.log(`- ID: ${productBase.id}`);
  console.log(`- Name: ${productBase.name}`);
  console.log(`- Name (Arabic): ${productBase.name_ar || 'Not set'}`);
  console.log(`- Created at: ${productBase.created_at}`);
  
  // Step 2: Check iterations for this product
  const { data: iterations, error: iterationsError } = await supabase
    .from('product_iterations')
    .select('id, version, iteration, created_at, status')
    .eq('product_base_id', productId)
    .order('created_at', { ascending: false });
    
  if (iterationsError) {
    console.error('Error checking iterations:', iterationsError);
    return;
  }
  
  if (!iterations || iterations.length === 0) {
    console.log(`No iterations found for product ${productId}.`);
    return;
  }
  
  console.log(`\nFound ${iterations.length} iterations for this product:`);
  iterations.forEach((iter, index) => {
    console.log(`${index + 1}. Version: ${iter.version || iter.iteration || 'Unknown'}, ID: ${iter.id}, Status: ${iter.status}, Created: ${iter.created_at}`);
  });
  
  // Step 3: Check for the specific iteration if provided
  if (iterationVersion) {
    console.log(`\nChecking for specific iteration: ${iterationVersion}`);
    
    const matchingIteration = iterations.find(
      iter => (iter.iteration === iterationVersion || iter.version === iterationVersion)
    );
    
    if (matchingIteration) {
      console.log(`Iteration ${iterationVersion} found:`);
      console.log(`- ID: ${matchingIteration.id}`);
      console.log(`- Version: ${matchingIteration.version || 'Not set'}`);
      console.log(`- Iteration: ${matchingIteration.iteration || 'Not set'}`);
      console.log(`- Status: ${matchingIteration.status}`);
      console.log(`- Created at: ${matchingIteration.created_at}`);
    } else {
      console.log(`Iteration ${iterationVersion} does not exist for this product.`);
      console.log(`Available iterations: ${iterations.map(i => i.iteration || i.version).join(', ')}`);
    }
  }
};

// Get product ID and iteration version from command line arguments
const productId = process.argv[2] || 'bbe88976-5283-4752-b8f0-b307fcaf4fb7'; // Default to the problematic ID
const iterationVersion = process.argv[3] || '2.0'; // Default to the problematic version

checkProductAndIteration(productId, iterationVersion)
  .catch(console.error)
  .finally(() => {
    console.log('\nCheck complete.');
  });
