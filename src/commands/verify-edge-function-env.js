// <PERSON>ript to verify environment variable setting in Edge Functions
import dotenv from 'dotenv';
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import chalk from 'chalk';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Required environment variables
const EDGE_FUNCTION_SECRET = process.env.VITE_EDGE_FUNCTION_SECRET;

if (!EDGE_FUNCTION_SECRET) {
  console.error(chalk.red('❌ Missing VITE_EDGE_FUNCTION_SECRET in .env.local'));
  process.exit(1);
}

console.log(chalk.blue('=== Edge Function Environment Verifier ==='));
console.log(chalk.gray(`Current EDGE_FUNCTION_SECRET: ${EDGE_FUNCTION_SECRET.substring(0, 5)}...${EDGE_FUNCTION_SECRET.substring(EDGE_FUNCTION_SECRET.length - 5)}`));

// Verify and update both Edge Functions
const edgeFunctions = ['debug', 'sdg-etl'];

for (const func of edgeFunctions) {
  console.log(chalk.blue(`\nChecking ${func} Edge Function...`));
  
  const envFilePath = path.join(process.cwd(), 'supabase', 'functions', func, '.env');
  
  // Verify if the file exists
  if (!fs.existsSync(envFilePath)) {
    console.log(chalk.yellow(`Creating new .env file for ${func}...`));
    createEnvFile(func, envFilePath);
    continue;
  }
  
  // Read current env file
  const currentEnv = fs.readFileSync(envFilePath, 'utf8');
  console.log(chalk.gray('Current .env file:'));
  console.log(chalk.gray('-'.repeat(40)));
  console.log(chalk.gray(currentEnv));
  console.log(chalk.gray('-'.repeat(40)));
  
  // Check if the secret key matches
  const envLines = currentEnv.split('\n');
  let secretLineIndex = -1;
  let currentSecret = '';
  
  for (let i = 0; i < envLines.length; i++) {
    const line = envLines[i].trim();
    if (line.startsWith('EDGE_FUNCTION_SECRET=')) {
      secretLineIndex = i;
      currentSecret = line.substring('EDGE_FUNCTION_SECRET='.length);
      break;
    }
  }
  
  if (secretLineIndex === -1) {
    console.log(chalk.red(`No EDGE_FUNCTION_SECRET found in ${func}'s .env file. Adding it...`));
    envLines.push(`EDGE_FUNCTION_SECRET=${EDGE_FUNCTION_SECRET}`);
    fs.writeFileSync(envFilePath, envLines.join('\n'));
    console.log(chalk.green(`✅ Added EDGE_FUNCTION_SECRET to ${func}'s .env file`));
  } else if (currentSecret !== EDGE_FUNCTION_SECRET) {
    console.log(chalk.yellow(`Secret key mismatch in ${func}'s .env file:`));
    console.log(chalk.yellow(`- Current: ${currentSecret.substring(0, 5)}...${currentSecret.substring(currentSecret.length - 5)}`));
    console.log(chalk.yellow(`- Expected: ${EDGE_FUNCTION_SECRET.substring(0, 5)}...${EDGE_FUNCTION_SECRET.substring(EDGE_FUNCTION_SECRET.length - 5)}`));
    
    envLines[secretLineIndex] = `EDGE_FUNCTION_SECRET=${EDGE_FUNCTION_SECRET}`;
    fs.writeFileSync(envFilePath, envLines.join('\n'));
    console.log(chalk.green(`✅ Updated EDGE_FUNCTION_SECRET in ${func}'s .env file`));
  } else {
    console.log(chalk.green(`✅ ${func}'s .env file has correct EDGE_FUNCTION_SECRET`));
  }
}

// Function to create a new .env file for an Edge Function
function createEnvFile(funcName, filePath) {
  const content = `SUPABASE_URL=https://wgsnpiskyczxhojlrwtr.supabase.co
SUPABASE_SERVICE_ROLE_KEY=${process.env.SUPABASE_SERVICE_ROLE_KEY || ''}
EDGE_FUNCTION_SECRET=${EDGE_FUNCTION_SECRET}`;

  fs.writeFileSync(filePath, content);
  console.log(chalk.green(`✅ Created new .env file for ${funcName}`));
}

// Prompt to redeploy the edge functions
console.log(chalk.blue('\n=== Next Steps ==='));
console.log(chalk.yellow('The environment files have been updated. You should redeploy both Edge Functions:'));
console.log(chalk.gray('1. node scripts/deploy-debug-function.js'));
console.log(chalk.gray('2. node scripts/deploy-sdg-edge-function.js'));
