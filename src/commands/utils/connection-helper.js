/**
 * Supabase database connection helper functions
 */

import readline from 'readline';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Get current file directory (equivalent to __dirname in CommonJS)
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Make sure environment variables are loaded
dotenv.config();

/**
 * Gets the Supabase connection string from various sources:
 * 1. Environment variable SUPABASE_CONNECTION_STRING
 * 2. .env file
 * 3. Supabase config.toml file
 * 4. Construct from Supabase URL and keys if possible
 * 5. Prompt user for inputs if interactive
 * 
 * @param {boolean} interactive Whether to prompt for inputs if connection string can't be determined
 * @returns {string|null} PostgreSQL connection string or null if it can't be determined
 */
function getSupabaseConnectionString(interactive = true) {
  // Check environment variable
  if (process.env.SUPABASE_CONNECTION_STRING) {
    return process.env.SUPABASE_CONNECTION_STRING;
  }
  
  // Check .env file again (redundant with dotenv.config() but kept for clarity)
  try {
    const envPath = path.join(process.cwd(), '.env');
    if (fs.existsSync(envPath)) {
      const envContent = fs.readFileSync(envPath, 'utf8');
      const match = envContent.match(/SUPABASE_CONNECTION_STRING=["']?(.*?)["']?$/m);
      if (match && match[1]) {
        return match[1];
      }
    }
  } catch (error) {
    console.warn('Could not read .env file:', error.message);
  }
  
  // Try to read from Supabase config.toml
  try {
    const configPath = path.join(process.cwd(), 'supabase', 'config.toml');
    if (fs.existsSync(configPath)) {
      const configContent = fs.readFileSync(configPath, 'utf8');
      
      // Extract database_url from config.toml
      const dbUrlMatch = configContent.match(/database_url\s*=\s*["'](.+?)["']/);
      if (dbUrlMatch && dbUrlMatch[1]) {
        return dbUrlMatch[1];
      }
      
      // If no database_url, try to construct from components
      const hostMatch = configContent.match(/db_host\s*=\s*["'](.+?)["']/);
      const portMatch = configContent.match(/db_port\s*=\s*(\d+)/);
      const nameMatch = configContent.match(/db_name\s*=\s*["'](.+?)["']/);
      const userMatch = configContent.match(/db_user\s*=\s*["'](.+?)["']/);
      const passMatch = configContent.match(/db_password\s*=\s*["'](.+?)["']/);
      
      if (hostMatch && portMatch && nameMatch && userMatch && passMatch) {
        const host = hostMatch[1];
        const port = portMatch[1];
        const name = nameMatch[1];
        const user = userMatch[1];
        const pass = passMatch[1];
        
        return `postgresql://${user}:${pass}@${host}:${port}/${name}`;
      }
    }
  } catch (error) {
    console.warn('Could not read Supabase config:', error.message);
  }
  
  // Try to construct from URL and project reference if available
  if (process.env.SUPABASE_URL) {
    // Extract project reference from URL
    const projectRefMatch = process.env.SUPABASE_URL.match(/https:\/\/([^.]+)\.supabase\.co/);
    if (projectRefMatch && projectRefMatch[1]) {
      const projectRef = projectRefMatch[1];
      console.log(`\nDetected Supabase project reference: ${projectRef}`);
      console.log('Note: To connect directly to the database, you need the database password.');
      console.log('This is different from your API keys and must be obtained from the Supabase dashboard.');
      console.log('');
      
      // If we're in non-interactive mode, we can't ask for the password
      if (!interactive) {
        return null;
      }
      
      // In interactive mode, we could prompt for the password, but we'll return null for now
      console.log('Please get your database password from the Supabase dashboard:');
      console.log('1. Go to https://app.supabase.io/project/' + projectRef);
      console.log('2. Navigate to Project Settings > Database');
      console.log('3. Find the "Connection string" section');
      console.log('');
      
      // Could prompt for password here using readline, but implementing this
      // would require async/await which would change the function signature
    }
  }

  // For non-interactive mode, return null if we can't determine the connection string
  if (!interactive) {
    return null;
  }
  
  // If we've reached here and in interactive mode, prompt for connection details
  console.log('\nCould not determine PostgreSQL connection string automatically.');
  console.log('Please provide connection details in your .env file:\n');
  console.log('SUPABASE_CONNECTION_STRING=postgresql://postgres:<EMAIL>:5432/postgres');
  
  return null;
}

export {
  getSupabaseConnectionString
};
