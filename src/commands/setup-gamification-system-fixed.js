import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

// Initialize dotenv
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Supabase URL or Key is missing in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Main function to set up the entire gamification system
 */
async function setupGamificationSystem() {
  console.log('🎮 Setting up gamification system...');

  try {
    // Step 1: Create database tables
    console.log('\n📊 Setting up database tables...');
    await createUserProfilesTable();
    await createAchievementsTable();
    await createUserAchievementsTable();
    await createGamificationEventsTable();
    await createGamificationConfigTable();
    await createGamificationRulesTable();
    
    // Step 2: Create default configuration
    console.log('\n⚙️ Setting up default configuration...');
    await setupDefaultConfig();
    
    // Step 3: Create default achievements
    console.log('\n🏆 Setting up default achievements...');
    await setupDefaultAchievements();
    
    // Step 4: Create default rules
    console.log('\n📝 Setting up default rules...');
    await setupDefaultRules();
    
    console.log('\n✅ Gamification system setup complete!');
    
    console.log('\n🔍 Next steps:');
    console.log('  1. Restart your application to activate the gamification system');
    console.log('  2. Visit the Admin panel and select the Gamification section');
    console.log('  3. Configure additional rules and achievements if needed');
    
    return true;
  } catch (err) {
    console.error('❌ Error setting up gamification system:', err);
    return false;
  }
}

/**
 * Create user profiles table
 */
async function createUserProfilesTable() {
  console.log('Creating user_gamification_profiles table...');
  
  // First check if the table already exists
  const { error: checkError } = await supabase.from('user_gamification_profiles').select('id').limit(1);
  
  if (!checkError || !checkError.message.includes('relation "user_gamification_profiles" does not exist')) {
    console.log('✓ user_gamification_profiles table already exists');
    return;
  }
  
  // Create table using insert operation
  const { error } = await supabase.from('user_gamification_profiles').insert({
    id: '00000000-0000-0000-0000-000000000000', // Dummy row to create table
    points: 0,
    level: 1,
    streak_days: 0,
    settings: {},
    last_active: new Date().toISOString(),
    streak_last_date: new Date().toISOString().split('T')[0],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  });
  
  if (error && !error.message.includes('already exists')) {
    console.error(`Error creating user_gamification_profiles table: ${error.message}`);
    return;
  }
  
  console.log('✓ user_gamification_profiles table created successfully');
}

/**
 * Create achievements table
 */
async function createAchievementsTable() {
  console.log('Creating achievements table...');
  
  // First check if the table already exists
  const { error: checkError } = await supabase.from('achievements').select('id').limit(1);
  
  if (!checkError || !checkError.message.includes('relation "achievements" does not exist')) {
    console.log('✓ achievements table already exists');
    return;
  }
  
  // Create table using insert operation
  const { error } = await supabase.from('achievements').insert({
    name: 'Dummy Achievement',
    description: 'This is a dummy achievement to create the table',
    category: 'system',
    icon: 'Trophy',
    points: 0,
    difficulty: 'medium',
    is_hidden: true,
    requirements: { dummy: true },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  });
  
  if (error && !error.message.includes('already exists')) {
    console.error(`Error creating achievements table: ${error.message}`);
    return;
  }
  
  console.log('✓ achievements table created successfully');
}

/**
 * Create user achievements table
 */
async function createUserAchievementsTable() {
  console.log('Creating user_achievements table...');
  
  // First check if the table already exists
  const { error: checkError } = await supabase.from('user_achievements').select('id').limit(1);
  
  if (!checkError || !checkError.message.includes('relation "user_achievements" does not exist')) {
    console.log('✓ user_achievements table already exists');
    return;
  }
  
  // Create the table through direct insert
  const { error } = await supabase.from('user_achievements').insert({
    id: '00000000-0000-0000-0000-000000000000', // Dummy row
    user_id: '00000000-0000-0000-0000-000000000000', // Dummy user ID
    achievement_id: '00000000-0000-0000-0000-000000000000', // Dummy achievement ID
    earned_at: new Date().toISOString()
  });
  
  if (error && !error.message.includes('already exists') && !error.message.includes('foreign key constraint')) {
    console.error(`Error creating user_achievements table: ${error.message}`);
    return;
  }
  
  console.log('✓ user_achievements table created successfully');
}

/**
 * Create gamification events table
 */
async function createGamificationEventsTable() {
  console.log('Creating gamification_events table...');
  
  // First check if the table already exists
  const { error: checkError } = await supabase.from('gamification_events').select('id').limit(1);
  
  if (!checkError || !checkError.message.includes('relation "gamification_events" does not exist')) {
    console.log('✓ gamification_events table already exists');
    return;
  }
  
  // Create the table through direct insert
  const { error } = await supabase.from('gamification_events').insert({
    id: '00000000-0000-0000-0000-000000000000', // Dummy row
    user_id: '00000000-0000-0000-0000-000000000000', // Dummy user ID
    event_type: 'dummy_event',
    event_data: { dummy: true },
    occurred_at: new Date().toISOString()
  });
  
  if (error && !error.message.includes('already exists') && !error.message.includes('foreign key constraint')) {
    console.error(`Error creating gamification_events table: ${error.message}`);
    return;
  }
  
  console.log('✓ gamification_events table created successfully');
}

/**
 * Create gamification config table
 */
async function createGamificationConfigTable() {
  console.log('Creating gamification_config table...');
  
  // First check if the table already exists
  const { error: checkError } = await supabase.from('gamification_config').select('key').limit(1);
  
  if (!checkError || !checkError.message.includes('relation "gamification_config" does not exist')) {
    console.log('✓ gamification_config table already exists');
    return;
  }
  
  // Create the table through direct insert
  const { error } = await supabase.from('gamification_config').insert({
    key: 'dummy_config',
    value: { enabled: true },
    description: 'Dummy config for table creation',
    category: 'system',
    is_system: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  });
  
  if (error && !error.message.includes('already exists')) {
    console.error(`Error creating gamification_config table: ${error.message}`);
    return;
  }
  
  console.log('✓ gamification_config table created successfully');
}

/**
 * Create gamification rules table
 */
async function createGamificationRulesTable() {
  console.log('Creating gamification_rules table...');
  
  // First check if the table already exists
  const { error: checkError } = await supabase.from('gamification_rules').select('id').limit(1);
  
  if (!checkError || !checkError.message.includes('relation "gamification_rules" does not exist')) {
    console.log('✓ gamification_rules table already exists');
    return;
  }
  
  // Create the table through direct insert
  const { error } = await supabase.from('gamification_rules').insert({
    name: 'Dummy Rule',
    description: 'Dummy rule for table creation',
    event_type: 'dummy_event',
    conditions: [{ field: 'dummy', operator: '==', value: true }],
    actions: [{ type: 'add_points', points: 0 }],
    is_active: false,
    priority: 999,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  });
  
  if (error && !error.message.includes('already exists')) {
    console.error(`Error creating gamification_rules table: ${error.message}`);
    return;
  }
  
  console.log('✓ gamification_rules table created successfully');
}

/**
 * Setup default configuration
 */
async function setupDefaultConfig() {
  // Define default config values
  const defaultConfig = [
    {
      key: 'point_values',
      value: {
        product_created: 50,
        product_updated: 10,
        comment_added: 5,
        achievement_earned: 25,
        profile_completed: 100,
        daily_login: 10
      },
      description: 'Point values for various actions',
      category: 'points',
      is_system: true
    },
    {
      key: 'level_formula',
      value: {
        formula: 'FLOOR(POWER(points / 100, 0.5))',
        description: 'Level is calculated as the square root of points divided by 100, rounded down'
      },
      description: 'Formula for calculating user level based on points',
      category: 'levels',
      is_system: true
    },
    {
      key: 'gamification_enabled',
      value: { enabled: true },
      description: 'Master switch for the gamification system',
      category: 'system',
      is_system: true
    },
    {
      key: 'achievements_visible',
      value: { visible: true },
      description: 'Whether achievements are visible to users',
      category: 'achievements',
      is_system: false
    },
    {
      key: 'leaderboard_enabled',
      value: { enabled: true },
      description: 'Whether the leaderboard feature is enabled',
      category: 'social',
      is_system: false
    }
  ];
  
  // Insert the config items
  for (const config of defaultConfig) {
    const { error } = await supabase
      .from('gamification_config')
      .upsert(config, { onConflict: 'key' });
    
    if (error) {
      console.error(`Error inserting config "${config.key}": ${error.message}`);
    } else {
      console.log(`✓ Config "${config.key}" inserted or updated successfully`);
    }
  }
}

/**
 * Setup default achievements
 */
async function setupDefaultAchievements() {
  // Define default achievements
  const defaultAchievements = [
    {
      name: 'Welcome Aboard',
      description: 'Complete your profile and join the community',
      category: 'onboarding',
      icon: 'UserCheck',
      points: 100,
      difficulty: 'easy',
      is_hidden: false,
      requirements: { profile_completion: 100 }
    },
    {
      name: 'First Product',
      description: 'Create your first statistical product',
      category: 'products',
      icon: 'BarChart',
      points: 150,
      difficulty: 'easy',
      is_hidden: false,
      requirements: { products_created: 1 }
    },
    {
      name: 'Collaboration Star',
      description: 'Contribute to 5 different products',
      category: 'collaboration',
      icon: 'Users',
      points: 300,
      difficulty: 'medium',
      is_hidden: false,
      requirements: { unique_products_contributed: 5 }
    },
    {
      name: 'Data Champion',
      description: 'Have 3 products published and approved',
      category: 'products',
      icon: 'Award',
      points: 500,
      difficulty: 'hard',
      is_hidden: false,
      requirements: { products_published: 3 }
    },
    {
      name: 'Consistent Contributor',
      description: 'Log in for 7 consecutive days',
      category: 'engagement',
      icon: 'Calendar',
      points: 200,
      difficulty: 'medium',
      is_hidden: false,
      requirements: { login_streak: 7 }
    },
    {
      name: 'Survey Expert',
      description: 'Create or contribute to 3 surveys',
      category: 'surveys',
      icon: 'ClipboardList',
      points: 250,
      difficulty: 'medium',
      is_hidden: false,
      requirements: { surveys_contributed: 3 }
    },
    {
      name: 'Knowledge Sharer',
      description: 'Add comments on 10 different products',
      category: 'collaboration',
      icon: 'MessageSquare',
      points: 200,
      difficulty: 'medium',
      is_hidden: false,
      requirements: { comments_added: 10 }
    },
    {
      name: 'Approval Ace',
      description: 'Get 5 approvals without revision requests',
      category: 'workflow',
      icon: 'CheckCircle',
      points: 400,
      difficulty: 'hard',
      is_hidden: false,
      requirements: { clean_approvals: 5 }
    }
  ];
  
  // Insert the achievements
  for (const achievement of defaultAchievements) {
    const { error } = await supabase
      .from('achievements')
      .upsert(achievement, { onConflict: 'name' });
    
    if (error) {
      console.error(`Error inserting achievement "${achievement.name}": ${error.message}`);
    } else {
      console.log(`✓ Achievement "${achievement.name}" inserted or updated successfully`);
    }
  }
}

/**
 * Setup default rules
 */
async function setupDefaultRules() {
  // First get achievement IDs for rules that grant achievements
  const { data: achievements, error: achievementsError } = await supabase
    .from('achievements')
    .select('id, name');
  
  if (achievementsError) {
    console.error(`Error fetching achievements: ${achievementsError.message}`);
    return;
  }
  
  // Map achievement names to IDs
  const achievementMap = achievements.reduce((map, achievement) => {
    map[achievement.name] = achievement.id;
    return map;
  }, {});
  
  // Define default rules
  const defaultRules = [
    {
      name: 'Profile Completed',
      description: 'Award points and achievement when user completes their profile',
      event_type: 'profile_updated',
      conditions: [
        {
          field: 'completion_percentage',
          operator: '>=',
          value: 100
        }
      ],
      actions: [
        {
          type: 'add_points',
          points: 100,
          reason: 'Profile completed'
        },
        {
          type: 'grant_achievement',
          achievement_id: achievementMap['Welcome Aboard']
        }
      ],
      is_active: true,
      priority: 10
    },
    {
      name: 'First Product Created',
      description: 'Award achievement when user creates their first product',
      event_type: 'product_created',
      conditions: [
        {
          field: 'user_products_count',
          operator: '==',
          value: 1
        }
      ],
      actions: [
        {
          type: 'add_points',
          points: 150,
          reason: 'First product created'
        },
        {
          type: 'grant_achievement',
          achievement_id: achievementMap['First Product']
        }
      ],
      is_active: true,
      priority: 20
    },
    {
      name: 'Daily Login Streak',
      description: 'Track and reward daily login streaks',
      event_type: 'user_login',
      conditions: [
        {
          field: 'login_streak',
          operator: '>=',
          value: 7
        },
        {
          field: 'has_achievement',
          operator: '==',
          value: false,
          achievement_id: achievementMap['Consistent Contributor']
        }
      ],
      actions: [
        {
          type: 'add_points',
          points: 200,
          reason: '7-day login streak achieved'
        },
        {
          type: 'grant_achievement',
          achievement_id: achievementMap['Consistent Contributor']
        }
      ],
      is_active: true,
      priority: 30
    },
    {
      name: 'Comment Added',
      description: 'Award points when a user adds a comment',
      event_type: 'comment_added',
      conditions: [],
      actions: [
        {
          type: 'add_points',
          points: 5,
          reason: 'Comment added'
        }
      ],
      is_active: true,
      priority: 100
    },
    {
      name: 'Product Updated',
      description: 'Award points when a user updates a product',
      event_type: 'product_updated',
      conditions: [],
      actions: [
        {
          type: 'add_points',
          points: 10,
          reason: 'Product updated'
        }
      ],
      is_active: true,
      priority: 100
    }
  ];
  
  // Insert the rules
  for (const rule of defaultRules) {
    const { error } = await supabase
      .from('gamification_rules')
      .upsert(rule, { onConflict: 'name' });
    
    if (error) {
      console.error(`Error inserting rule "${rule.name}": ${error.message}`);
    } else {
      console.log(`✓ Rule "${rule.name}" inserted or updated successfully`);
    }
  }
}

// Run the setup if this script is executed directly
console.log('Script starting with argv[1]:', process.argv[1]);
console.log('import.meta.url:', import.meta.url);
if (true) {
  setupGamificationSystem()
    .then(success => {
      if (success) {
        console.log('\n✅ Gamification system setup completed successfully.');
        process.exit(0);
      } else {
        console.error('\n❌ Gamification system setup failed.');
        process.exit(1);
      }
    })
    .catch(err => {
      console.error('Fatal error:', err);
      process.exit(1);
    });
}

export { setupGamificationSystem };
