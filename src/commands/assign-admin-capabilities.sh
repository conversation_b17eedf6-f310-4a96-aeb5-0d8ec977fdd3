#!/bin/bash

# Check if node is installed
if ! command -v node &> /dev/null; then
    echo "Node.js is not installed. Please install Node.js to run this script."
    exit 1
fi

# Get the directory where the script is located
SCRIPT_DIR=$(dirname "$0")

# Run the JavaScript file with Node.js
echo "Running admin capabilities assignment script..."
node "$SCRIPT_DIR/assign-admin-capabilities.js"

# Check if the command was successful
if [ $? -eq 0 ]; then
    echo "✅ Admin capabilities successfully assigned!"
else
    echo "❌ Failed to assign admin capabilities. See error messages above."
    exit 1
fi
