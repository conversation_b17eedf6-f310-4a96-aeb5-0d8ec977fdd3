@echo off
REM Script to fix approval system issues by updating product iterations
REM This script will:
REM 1. Get the current user's ID
REM 2. Update product iterations to include the user ID as product owner
REM 3. Log the results

echo === Approval System Fix ===
echo This script will fix issues with dependencies not showing up in the approvals dashboard.
echo.

REM Get the current user ID
echo Step 1: Getting current user ID...
for /f "tokens=3" %%i in ('node src/commands/get-current-user.js ^| findstr "User ID:"') do set USER_ID=%%i

if "%USER_ID%"=="" (
  echo Error: Could not get user ID. Please make sure you're logged in.
  echo Try running 'node src/commands/get-current-user.js' manually to see the error.
  exit /b 1
)

echo Found user ID: %USER_ID%
echo.

REM Update product iterations
echo Step 2: Updating product iterations...
node src/commands/update-product-iterations.js "%USER_ID%"

echo.
echo === Fix Complete ===
echo Please refresh the approvals dashboard to see the changes.
echo If you still don't see your dependencies, check the README-approval-fix.md file for troubleshooting steps.
