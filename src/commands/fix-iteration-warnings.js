/**
 * <PERSON><PERSON><PERSON> to fix iteration ID mapping and reduce console warnings
 * 
 * This script adds:
 * 1. Warning limiter to prevent repeated console warnings
 * 2. Version number to UUID mapping for iteration IDs
 * 3. Enhanced error handling for invalid iteration IDs
 * 
 * Usage: 
 * node src/commands/fix-iteration-warnings.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get current file directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m',
};

console.log(`${colors.magenta}🛠  Preparing to fix iteration ID warnings...${colors.reset}\n`);

// Paths to relevant files
const useProductStagesPath = path.join(process.cwd(), 'src/components/products/stage/hooks/useProductStages.ts');
const docsPath = path.join(process.cwd(), 'docs/ITERATION-ID-MAPPING-FIX.md');

// Check if the hook file exists
if (!fs.existsSync(useProductStagesPath)) {
  console.error(`${colors.red}❌ useProductStages hook file not found: ${useProductStagesPath}${colors.reset}`);
  process.exit(1);
}

try {
  // Read the original file
  console.log(`${colors.blue}📖 Reading useProductStages.ts file...${colors.reset}`);
  const originalContent = fs.readFileSync(useProductStagesPath, 'utf8');
  
  // Update the content with our changes
  console.log(`${colors.blue}✏️  Adding warning limiter and ID mapping...${colors.reset}`);

  // 1. Add warning limiter reference
  let modifiedContent = originalContent.replace(
    'const toastShownRef = useRef<Set<string>>(new Set());',
    'const toastShownRef = useRef<Set<string>>(new Set());\n  const warningShownRef = useRef<Set<string>>(new Set());\n  // Add version to UUID mapping\n  const versionToUuidMapRef = useRef<Record<string, string>>({});'
  );

  // 2. Find and update the useEffect that logs warnings
  const warningPattern = /useEffect\(\s*\(\)\s*=>\s*\{[\s\S]*?currentIterationStages\.length\s*===\s*0\s*&&\s*stagesData\.length\s*>\s*0[\s\S]*?console\.warn[\s\S]*?\}\s*\)\s*,\s*\[currentIterationStages,\s*productIterationId,\s*stagesData\]\s*\);/;

  modifiedContent = modifiedContent.replace(warningPattern, `useEffect(() => {
    console.log("Current iteration stages after filtering:", {
      count: currentIterationStages?.length || 0,
      iterationId: productIterationId,
      stages: currentIterationStages?.map(s => ({
        id: s.id,
        type: s.stage_type,
        startDate: s.start_date,
        endDate: s.end_date,
        iterationId: s.product_iteration_id
      }))
    });
    
    if (currentIterationStages.length === 0 && stagesData.length > 0) {
      // Generate a unique warning key for this specific scenario
      const availableIterationIds = [...new Set(stagesData.map(s => s.product_iteration_id))];
      const warningKey = \`iteration-mismatch-\${productIterationId}-\${availableIterationIds.join('-')}\`;
      
      // Only show the warning once per unique combination
      if (!warningShownRef.current.has(warningKey)) {
        console.warn("No stages match the current iteration ID. Available iteration IDs in stages data:", 
          availableIterationIds.join(", ")
        );
        warningShownRef.current.add(warningKey);
        
        // Try to map numeric version to UUID if we have data
        if (availableIterationIds.length === 1 && 
            typeof productIterationId === 'string' && 
            /^\\d+(\\.\\d+)?$/.test(productIterationId)) {
          console.log(\`Detected potential version number (\${productIterationId}) instead of UUID. Mapping to available ID \${availableIterationIds[0]}\`);
          versionToUuidMapRef.current[productIterationId] = availableIterationIds[0];
          // Update the valid iteration ID to the mapped UUID
          validIterationIdRef.current = availableIterationIds[0];
          setProductIterationId(availableIterationIds[0]);
          queryEnabledRef.current = true;
        }
      }
    }
  }, [currentIterationStages, productIterationId, stagesData]);`);

  // 3. Update the validateIterationID function to add version mapping logic
  const validatePattern = /const validateIterationID = useCallback\(async \(id: string\) => \{[\s\S]*?if \(!id\) return false;/;
  modifiedContent = modifiedContent.replace(validatePattern, `const validateIterationID = useCallback(async (id: string) => {
    if (!id) return false;
    
    // Check if this is a version number and we have a mapping for it
    if (/^\\d+(\\.\\d+)?$/.test(id) && versionToUuidMapRef.current[id]) {
      console.log(\`Using mapped UUID for version \${id}: \${versionToUuidMapRef.current[id]}\`);
      return validateIterationID(versionToUuidMapRef.current[id]);
    }`);

  // Write the updated file
  fs.writeFileSync(useProductStagesPath, modifiedContent);
  console.log(`${colors.green}✅ Successfully updated useProduct
