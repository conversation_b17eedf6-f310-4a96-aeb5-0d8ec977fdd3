// Check authentication secrets to diagnose issues
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config({ path: '.env.local' });

const EDGE_FUNCTION_SECRET = process.env.VITE_EDGE_FUNCTION_SECRET || 'default-dev-secret';

// Function to read .env file from the Edge Function
function readEdgeFunctionEnv() {
  try {
    const envPath = path.join(process.cwd(), 'supabase', 'functions', 'sdg-etl', '.env');
    if (!fs.existsSync(envPath)) {
      return null;
    }
    
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');
    const envVars = {};
    
    lines.forEach(line => {
      if (line.trim() && !line.startsWith('#')) {
        const [key, ...valueParts] = line.split('=');
        const value = valueParts.join('='); // Rejoin in case value contains '='
        if (key && value) {
          envVars[key.trim()] = value.trim();
        }
      }
    });
    
    return envVars;
  } catch (error) {
    console.error('Error reading Edge Function .env:', error.message);
    return null;
  }
}

// Compare secrets
async function compareSecrets() {
  console.log('Checking authentication secrets');
  console.log('==============================');
  
  // Local secret
  console.log('Local Secret (.env.local):');
  console.log(`  Length: ${EDGE_FUNCTION_SECRET.length}`);
  console.log(`  Value: ${EDGE_FUNCTION_SECRET}`);
  console.log(`  First 6 chars: ${EDGE_FUNCTION_SECRET.substring(0, 6)}`);
  console.log(`  Last 6 chars: ${EDGE_FUNCTION_SECRET.substring(EDGE_FUNCTION_SECRET.length - 6)}`);
  console.log(`  Character codes:`, EDGE_FUNCTION_SECRET.split('').map(c => c.charCodeAt(0).toString(16)).join(' '));
  
  // Edge Function secret
  const edgeFunctionEnv = readEdgeFunctionEnv();
  
  if (edgeFunctionEnv && edgeFunctionEnv.EDGE_FUNCTION_SECRET) {
    const funcSecret = edgeFunctionEnv.EDGE_FUNCTION_SECRET;
    console.log('\nEdge Function Secret (.env):');
    console.log(`  Length: ${funcSecret.length}`);
    console.log(`  Value: ${funcSecret}`);
    console.log(`  First 6 chars: ${funcSecret.substring(0, 6)}`);
    console.log(`  Last 6 chars: ${funcSecret.substring(funcSecret.length - 6)}`);
    console.log(`  Character codes:`, funcSecret.split('').map(c => c.charCodeAt(0).toString(16)).join(' '));
    
    // Check for mismatch
    if (funcSecret === EDGE_FUNCTION_SECRET) {
      console.log('\n✅ Secrets match in the files');
    } else {
      console.log('\n❌ Secrets do NOT match in the files');
      console.log('\nDifferences:');
      
      // Find first diff
      let firstDiffIndex = 0;
      while (firstDiffIndex < Math.min(funcSecret.length, EDGE_FUNCTION_SECRET.length)) {
        if (funcSecret[firstDiffIndex] !== EDGE_FUNCTION_SECRET[firstDiffIndex]) {
          break;
        }
        firstDiffIndex++;
      }
      
      if (firstDiffIndex < Math.min(funcSecret.length, EDGE_FUNCTION_SECRET.length)) {
        console.log(`  First difference at index ${firstDiffIndex}:`);
        console.log(`  Local:   "${EDGE_FUNCTION_SECRET.substring(Math.max(0, firstDiffIndex-5), firstDiffIndex+6)}" (char: "${EDGE_FUNCTION_SECRET[firstDiffIndex]}" - ${EDGE_FUNCTION_SECRET.charCodeAt(firstDiffIndex).toString(16)})`);
        console.log(`  Edge Fn: "${funcSecret.substring(Math.max(0, firstDiffIndex-5), firstDiffIndex+6)}" (char: "${funcSecret[firstDiffIndex]}" - ${funcSecret.charCodeAt(firstDiffIndex).toString(16)})`);
      } else if (funcSecret.length !== EDGE_FUNCTION_SECRET.length) {
        console.log(`  Lengths differ - Local: ${EDGE_FUNCTION_SECRET.length}, Edge Function: ${funcSecret.length}`);
      }
    }
  } else {
    console.log('\n❌ Edge Function .env not found or does not contain EDGE_FUNCTION_SECRET');
  }
  
  console.log('\n==============================');
  console.log('Next steps:');
  console.log('1. If secrets don\'t match, run: node scripts/deploy-sdg-edge-function.js');
  console.log('2. This will update the .env file in the Edge Function and redeploy it');
  console.log('3. After deploying, test with: node src/commands/fetch-sdg-data.js test');
}

// Run the check
compareSecrets();
