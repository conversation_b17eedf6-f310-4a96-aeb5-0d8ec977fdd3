// <PERSON><PERSON><PERSON> to get the current user's ID from the Supabase session
// Usage: node src/commands/get-current-user.js

import { createClient } from '@supabase/supabase-js';

// Define Supabase client directly to avoid TypeScript import issues
// Get Supabase connection info from environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_KEY;

// Ensure environment variables are set
if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Missing Supabase environment variables.');
  console.error('Make sure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in your environment.');
  console.error('Create a .env file based on .env.example or set these variables in your environment.');
  process.exit(1);
}
const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

const getCurrentUser = async () => {
  try {
    // Get the current session
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('Error getting session:', error.message);
      return;
    }
    
    if (!session) {
      console.log('No active session found. Please sign in first.');
      return;
    }
    
    const userId = session.user.id;
    const userEmail = session.user.email;
    
    console.log('Current user information:');
    console.log('------------------------');
    console.log(`User ID: ${userId}`);
    console.log(`Email: ${userEmail}`);
    console.log('------------------------');
    console.log('Use this User ID with the update-product-iterations.js script:');
    console.log(`node src/commands/update-product-iterations.js ${userId}`);
    
    return userId;
  } catch (error) {
    console.error('Error getting current user:', error.message);
  }
};

getCurrentUser().catch(console.error);
