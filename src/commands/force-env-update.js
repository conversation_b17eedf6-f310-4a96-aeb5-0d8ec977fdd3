// Script to force environment variable updates in Edge Functions
import dotenv from 'dotenv';
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import chalk from 'chalk';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Required environment variables
const EDGE_FUNCTION_SECRET = process.env.VITE_EDGE_FUNCTION_SECRET;

if (!EDGE_FUNCTION_SECRET) {
  console.error(chalk.red('❌ Missing VITE_EDGE_FUNCTION_SECRET in .env.local'));
  process.exit(1);
}

console.log(chalk.blue('=== Edge Function Environment Force Updater ==='));

// Generate a new timestamped secret to ensure environment change is detected
const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
const randomString = Math.random().toString(36).substring(2, 10);
const newSecret = `TEMP_KEY_${timestamp}_${randomString}`;

console.log(chalk.yellow(`Generating temporary secret for redeployment: ${newSecret.substring(0, 5)}...${newSecret.substring(newSecret.length - 5)}`));

// Force update both Edge Functions
const edgeFunctions = ['debug', 'sdg-etl'];

for (const func of edgeFunctions) {
  console.log(chalk.blue(`\nUpdating ${func} Edge Function...`));
  
  const envFilePath = path.join(process.cwd(), 'supabase', 'functions', func, '.env');
  
  // Create a temporary environment file with the new secret
  const tempEnvContent = `SUPABASE_URL=https://wgsnpiskyczxhojlrwtr.supabase.co
SUPABASE_SERVICE_ROLE_KEY=${process.env.SUPABASE_SERVICE_ROLE_KEY || ''}
EDGE_FUNCTION_SECRET=${newSecret}`;
  
  fs.writeFileSync(envFilePath, tempEnvContent);
  console.log(chalk.yellow(`1. Temporary secret written to ${func}/.env`));
  
  // Log the change
  console.log(chalk.gray('Temporary content:'));
  console.log(chalk.gray('-'.repeat(40)));
  console.log(chalk.gray(tempEnvContent));
  console.log(chalk.gray('-'.repeat(40)));
  
  // Deploy with temporary secret
  try {
    console.log(chalk.yellow(`2. Deploying ${func} with temporary secret...`));
    
    const deployScript = func === 'debug' 
      ? 'node scripts/deploy-debug-function.js'
      : 'node scripts/deploy-sdg-edge-function.js';
      
    execSync(deployScript, { stdio: 'inherit' });
    
    console.log(chalk.green(`✓ ${func} deployed with temporary secret`));
  } catch (error) {
    console.error(chalk.red(`Error deploying ${func} with temporary secret:`), error);
    // Continue with next step anyway
  }
  
  // Now update to the real secret
  const realEnvContent = `SUPABASE_URL=https://wgsnpiskyczxhojlrwtr.supabase.co
SUPABASE_SERVICE_ROLE_KEY=${process.env.SUPABASE_SERVICE_ROLE_KEY || ''}
EDGE_FUNCTION_SECRET=${EDGE_FUNCTION_SECRET}`;
  
  fs.writeFileSync(envFilePath, realEnvContent);
  console.log(chalk.yellow(`3. Real secret written to ${func}/.env`));
  
  // Log the change
  console.log(chalk.gray('Final content:'));
  console.log(chalk.gray('-'.repeat(40)));
  console.log(chalk.gray(realEnvContent));
  console.log(chalk.gray('-'.repeat(40)));
  
  // Deploy with real secret
  try {
    console.log(chalk.yellow(`4. Deploying ${func} with real secret...`));
    
    const deployScript = func === 'debug' 
      ? 'node scripts/deploy-debug-function.js'
      : 'node scripts/deploy-sdg-edge-function.js';
      
    execSync(deployScript, { stdio: 'inherit' });
    
    console.log(chalk.green(`✓ ${func} deployed with real secret`));
  } catch (error) {
    console.error(chalk.red(`Error deploying ${func} with real secret:`), error);
  }
}

console.log(chalk.green('\n✅ Environment update complete!'));
console.log(chalk.blue('\nEdge Functions should now recognize the correct secret:'));
console.log(chalk.gray(`EDGE_FUNCTION_SECRET=${EDGE_FUNCTION_SECRET.substring(0, 5)}...${EDGE_FUNCTION_SECRET.substring(EDGE_FUNCTION_SECRET.length - 5)}`));

// Prompt to test the functions
console.log(chalk.blue('\n=== Next Steps ==='));
console.log(chalk.yellow('You should now test the Edge Functions:'));
console.log(chalk.gray('1. node src/commands/test-debug-verbose.js'));
console.log(chalk.gray('2. node src/commands/test-sdg-auth.js'));
