// <PERSON>rip<PERSON> to apply the fix for user_kanban_preferences
// Run with: node src/commands/apply-kanban-preferences-fix.js

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('Applying user_kanban_preferences fix...');

// Get the project root directory
const projectRoot = path.resolve(__dirname, '../..');

// Path to the migration file
const migrationFilePath = path.join(projectRoot, 'supabase/migrations/20250318_fix_user_kanban_preferences.sql');

// Check if the migration file exists
if (!fs.existsSync(migrationFilePath)) {
  console.error(`Migration file not found: ${migrationFilePath}`);
  process.exit(1);
}

try {
  // Run the migration with Supabase CLI
  console.log('Running migration with Supabase CLI...');
  const output = execSync('npx supabase db push --db-url $SUPABASE_DB_URL', {
    cwd: projectRoot,
    env: {
      ...process.env,
      SUPABASE_DB_URL: process.env.SUPABASE_DB_URL || 'postgresql://postgres:postgres@localhost:54322/postgres'
    },
    stdio: 'inherit'
  });

  console.log('Migration successfully applied.');
  console.log('');
  console.log('If you were experiencing errors with the task board related to:');
  console.log('  - "column user_kanban_preferences.column_widths does not exist"');
  console.log('  - "duplicate key value violates unique constraint"');
  console.log('These issues should now be resolved.');
} catch (error) {
  console.error('Error applying migration:', error.message);
  console.log('');
  console.log('You can try to apply the migration manually:');
  console.log('1. Connect to your Supabase database');
  console.log(`2. Run the SQL commands in: ${migrationFilePath}`);
  process.exit(1);
}
