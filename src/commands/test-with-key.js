// Test program with the unique test key
import fetch from 'node-fetch';

const SUPABASE_URL = 'https://wgsnpiskyczxhojlrwtr.supabase.co';
const TEST_KEY = 'TEST_KEY_2025-04-26T16-27-04-282Z_epcmo5ly';

async function testDebug() {
  console.log('Testing debug function with test key');
  
  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/debug`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${TEST_KEY}`
      }
    });
    
    console.log(`Response status: ${response.status}`);
    const data = await response.json();
    console.log('Response data:', JSON.stringify(data, null, 2));
    
    return data;
  } catch (error) {
    console.error('Error:', error);
  }
}

testDebug();
