#!/bin/bash
echo "Running user_kanban_preferences database fix..."
echo ""

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# Run the Node.js script
node "$SCRIPT_DIR/apply-kanban-preferences-fix.js"

# Check if the script ran successfully
if [ $? -ne 0 ]; then
  echo ""
  echo "Error occurred while applying fix."
  echo "Please check the error message above for details."
  echo ""
  exit 1
fi

echo ""
echo "The user_kanban_preferences fix has been applied successfully."
echo "The task board errors should now be resolved."
echo ""
