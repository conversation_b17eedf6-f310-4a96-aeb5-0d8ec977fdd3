/**
 * <PERSON><PERSON><PERSON> to suppress capability RPC function warnings
 * 
 * This script applies enhanced warning suppression for the
 * get_dynamic_user_capabilities 404 errors to clean up the console output.
 * 
 * Usage:
 * node src/commands/suppress-capability-warnings.cjs
 */

// Use CJS format since it works in both ESM and CJS environments
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

/**
 * Apply warning suppression for capability RPC functions
 */
function suppressCapabilityWarnings() {
  console.log(`${colors.blue}Applying enhanced warning suppression for RPC functions...${colors.reset}`);
  
  try {
    // Create or update the gateway.js file with warning suppression
    const gatewayPath = path.join(__dirname, '../integrations/supabase/gateway.js');
    const reducedWarningsContent = `
// Override console.warn for specific RPC functions to reduce noise
const originalWarn = console.warn;

console.warn = function(...args) {
  // Skip warnings that match specific patterns
  if (args[0] && typeof args[0] === 'string') {
    const patterns = [
      'RPC function get_dynamic_user_capabilities returned 404',
      'POST https://wgsnpiskyczxhojlrwtr.supabase.co/rest/v1/rpc/get_dynamic_user_capabilities 404',
      'RPC function get_user_caps returned 404',
      'POST https://wgsnpiskyczxhojlrwtr.supabase.co/rest/v1/rpc/get_user_caps 404'
    ];
    
    for (const pattern of patterns) {
      if (args[0].includes(pattern)) {
        // Instead of showing the warning, log at debug level
        if (process.env.NODE_ENV === 'development' && process.env.LOG_LEVEL === 'verbose') {
          console.debug('Suppressed warning:', args[0]);
        }
        return; // Skip this warning
      }
    }
  }
  
  // Show other warnings
  originalWarn.apply(console, args);
};

console.log('RPC function warnings have been suppressed for capabilities functions');
`;
    
    // Write to a script file that will be loaded in the browser
    const scriptPath = path.join(__dirname, '../../public/js/suppress-capability-warnings.js');
    fs.writeFileSync(scriptPath, reducedWarningsContent);
    console.log(`${colors.green}Created warning suppression script at ${scriptPath}${colors.reset}`);
    
    // Create HTML to demonstrate the suppression
    const htmlPath = path.join(__dirname, '../../public/suppress-capability-warnings.html');
    const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Suppress Capability Warnings</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            line-height: 1.6;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
        code {
            background-color: #f5f5f5;
            padding: 2px 6px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>RPC Function Warning Suppression</h1>
    
    <div class="card">
        <h2>Warning Status</h2>
        <p>This page has loaded the warning suppression script. The following warnings are now suppressed:</p>
        <ul>
            <li><code>RPC function get_dynamic_user_capabilities returned 404</code></li>
            <li><code>POST https://...supabase.co/rest/v1/rpc/get_dynamic_user_capabilities 404</code></li>
        </ul>
        <p>The fallback mechanisms continue to work silently in the background.</p>
    </div>
    
    <div class="card">
        <h2>How to Use</h2>
        <p>To activate this warning suppression in your application:</p>
        <ol>
            <li>Include the script in your HTML:</li>
            <code>&lt;script src="/js/suppress-capability-warnings.js"&gt;&lt;/script&gt;</code>
            <li>Add it before any scripts that make Supabase calls</li>
        </ol>
    </div>

    <script src="/js/suppress-capability-warnings.js"></script>
</body>
</html>
`;
    
    fs.writeFileSync(htmlPath, htmlContent);
    console.log(`${colors.green}Created HTML demonstration page at ${htmlPath}${colors.reset}`);
    
    // Success message
    console.log(`\n${colors.cyan}Warning suppression has been configured.${colors.reset}`);
    console.log(`${colors.cyan}The fallback mechanisms will continue to work silently.${colors.reset}`);
    
    // Add info about how to verify
    console.log(`\n${colors.yellow}To verify suppression:${colors.reset}`);
    console.log(`1. Open the demonstration page: /suppress-capability-warnings.html`);
    console.log(`2. Open browser console and check that 404 errors are no longer displayed`);
    console.log(`3. To include in your app, add the script tag to your HTML pages`);
    
  } catch (error) {
    console.error(`${colors.red}Error applying warning suppression:${colors.reset}`, error.message);
    process.exit(1);
  }
}

// Execute the script
suppressCapabilityWarnings();

module.exports = { suppressCapabilityWarnings };
