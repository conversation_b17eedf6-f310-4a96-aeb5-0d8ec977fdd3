#!/bin/bash

# Apply the safe permissions system script
# This script fixes the infinite recursion issue in RLS policies

# Change to the project root directory
cd "$(dirname "$0")/../.." || exit 1

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
  echo "Error: Node.js is not installed or not in PATH"
  exit 1
fi

# Run the apply script directly
# Our JavaScript script now handles both initialization and migration
echo "Applying safe permissions system..."
node src/commands/apply-safe-permissions-system.js

# Check if the script ran successfully
if [ $? -eq 0 ]; then
  echo "✅ Permission system successfully applied!"
  echo "Now you can use the role testing tools in the Admin Panel"
else
  echo "❌ Failed to apply the permission system"
  echo "Please check the error messages above"
  exit 1
fi
