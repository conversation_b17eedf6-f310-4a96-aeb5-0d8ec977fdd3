#!/usr/bin/env node

/**
 * Command to apply tag system migration
 * 
 * This script will:
 * 1. Set up the tag system tables
 * 2. Add sample tags
 * 3. Set up RLS policies
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read the migration SQL from the file
const migrationPath = path.join(__dirname, '..', '..', 'supabase', 'migrations', '20250407_tag_system.sql');
const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

// Function to apply the migration
async function applyTagSystemMigration(supabaseUrl, supabaseKey) {
  console.log('Creating Supabase client...');
  const supabase = createClient(supabaseUrl, supabaseKey);
  
  console.log('Starting tag system migration...');
  
  try {
    // Execute the migration SQL
    const { error } = await supabase.rpc('pgexecute', { query: migrationSQL });
    
    if (error) {
      console.error('Error applying tag system migration:', error);
      return false;
    }
    
    console.log('Tag system migration applied successfully!');
    console.log('Tag system tables have been created with:');
    console.log('- Support for iteration-specific tags');
    console.log('- Sample tags (Bug, Feature, Documentation, etc.)');
    console.log('- Row Level Security policies');
    console.log('- Proper indexing for better performance');
    
    return true;
  } catch (err) {
    console.error('Error executing migration:', err);
    return false;
  }
}

// Main function
async function main() {
  // Get Supabase URL and key from environment variables or command line arguments
  const supabaseUrl = process.env.SUPABASE_URL || process.argv[2];
  const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.argv[3];
  
  if (!supabaseUrl || !supabaseKey) {
    console.error('Error: Supabase URL and service key are required.');
    console.error('Usage: apply-tag-system <supabase-url> <supabase-service-key>');
    console.error('Or set SUPABASE_URL and SUPABASE_SERVICE_KEY environment variables.');
    process.exit(1);
  }
  
  try {
    const success = await applyTagSystemMigration(supabaseUrl, supabaseKey);
    
    if (success) {
      console.log('Tag system migration completed successfully.');
      process.exit(0);
    } else {
      console.error('Tag system migration failed.');
      process.exit(1);
    }
  } catch (error) {
    console.error('Unexpected error:', error);
    process.exit(1);
  }
}

// Run the main function
main();
