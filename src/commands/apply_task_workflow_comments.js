// apply_task_workflow_comments.js
// <PERSON>ript to apply the task workflow and comments system migration

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read the .env file to get the Supabase URL and key
const envPath = path.resolve(__dirname, '../../.env');
let supabaseUrl;
let supabaseKey;

try {
  const envFile = fs.readFileSync(envPath, 'utf8');
  const lines = envFile.split('\n');
  
  for (const line of lines) {
    const [key, value] = line.split('=');
    if (key === 'VITE_SUPABASE_URL') {
      supabaseUrl = value;
    } else if (key === 'VITE_SUPABASE_ANON_KEY') {
      supabaseKey = value;
    }
  }
} catch (error) {
  console.error('Error reading .env file:', error);
  process.exit(1);
}

if (!supabaseUrl || !supabaseKey) {
  console.error('Supabase URL or key not found in .env file');
  process.exit(1);
}

// Create a Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Read the migration SQL file
const migrationPath = path.resolve(__dirname, '../../supabase/migrations/20250325_task_workflow_and_comments.sql');
const sql = fs.readFileSync(migrationPath, 'utf8');

// Function to apply the migration
async function applyMigration() {
  console.log('Applying task workflow and comments migration...');
  
  try {
    // Execute the SQL migration
    const { error } = await supabase.rpc('exec_sql', { sql });
    
    if (error) {
      console.error('Error applying migration:', error);
      return;
    }
    
    console.log('Migration applied successfully!');
    console.log('Task workflow roles and task comments system is now enabled.');
    console.log('Task status transitions are now role-based:');
    console.log('- Department Director, Product Owner, and Product Manager can create tasks (TO DO)');
    console.log('- Assignee can move task to IN PROGRESS');
    console.log('- Assignee can move task to UNDER REVIEW');
    console.log('- Department Director, Product Owner, and Product Manager can finalize tasks (COMPLETED)');
    console.log('Comment system is also available on all tasks.');
  } catch (error) {
    console.error('Error applying migration:', error);
  }
}

// Execute the migration
applyMigration();
