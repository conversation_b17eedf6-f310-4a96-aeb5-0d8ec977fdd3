// Direct synchronization of authorization tokens
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

console.log('🔑 SDG Authorization Token Synchronizer');

// Simple fixed token for both client and server
const FIXED_TOKEN = 'SIMPLE_SDG_SECRET_KEY_2025';

// Update tokens
console.log('\n1️⃣ Updating local environment variables...');
const envLocalPath = '.env.local';
let envLocalContent = fs.readFileSync(envLocalPath, 'utf8');

// Update the VITE_EDGE_FUNCTION_SECRET
if (envLocalContent.includes('VITE_EDGE_FUNCTION_SECRET=')) {
  const regex = /VITE_EDGE_FUNCTION_SECRET=([^\n]*)/;
  const match = envLocalContent.match(regex);
  const oldValue = match ? match[1] : 'not found';
  
  console.log(`   Current value: ${oldValue}`);
  envLocalContent = envLocalContent.replace(
    /VITE_EDGE_FUNCTION_SECRET=.*/,
    `VITE_EDGE_FUNCTION_SECRET=${FIXED_TOKEN}`
  );
  console.log(`   New value: ${FIXED_TOKEN}`);
} else {
  envLocalContent += `\n# Fixed Edge Function Token\nVITE_EDGE_FUNCTION_SECRET=${FIXED_TOKEN}\n`;
  console.log(`   Added new variable with value: ${FIXED_TOKEN}`);
}

fs.writeFileSync(envLocalPath, envLocalContent);
console.log('   ✅ Updated .env.local');

// Update Edge Function .env files
console.log('\n2️⃣ Updating Edge Function environment variables...');
const edgeFunctions = ['debug', 'sdg-etl'];

for (const func of edgeFunctions) {
  const envPath = path.join('supabase', 'functions', func, '.env');
  
  console.log(`\n   Working on ${func} Edge Function...`);
  let envContent = fs.readFileSync(envPath, 'utf8');
  
  // Update the EDGE_FUNCTION_SECRET
  if (envContent.includes('EDGE_FUNCTION_SECRET=')) {
    const regex = /EDGE_FUNCTION_SECRET=([^\n]*)/;
    const match = envContent.match(regex);
    const oldValue = match ? match[1] : 'not found';
    
    console.log(`   Current value: ${oldValue}`);
    envContent = envContent.replace(
      /EDGE_FUNCTION_SECRET=.*/,
      `EDGE_FUNCTION_SECRET=${FIXED_TOKEN}`
    );
    console.log(`   New value: ${FIXED_TOKEN}`);
  } else {
    envContent += `\nEDGE_FUNCTION_SECRET=${FIXED_TOKEN}\n`;
    console.log(`   Added new variable with value: ${FIXED_TOKEN}`);
  }
  
  fs.writeFileSync(envPath, envContent);
  console.log(`   ✅ Updated ${envPath}`);
}

// Deploy Edge Functions
console.log('\n3️⃣ Deploying Edge Functions...');

try {
  // Deploy debug function
  console.log('\n   Deploying debug function...');
  execSync('node scripts/deploy-debug-function.js', { stdio: 'inherit' });
  console.log('   ✅ Debug function deployed successfully');
  
  // Deploy sdg-etl function
  console.log('\n   Deploying sdg-etl function...');
  execSync('node scripts/deploy-sdg-edge-function.js', { stdio: 'inherit' });
  console.log('   ✅ SDG-ETL function deployed successfully');
  
  // Update the authentication test to use the new token
  console.log('\n4️⃣ Updating test scripts to use the new token...');
  
  const testAuthPath = 'src/commands/test-sdg-auth.js';
  let testAuthContent = fs.readFileSync(testAuthPath, 'utf8');
  
  // Make sure the test uses the environment variable directly
  const authContentUpdated = testAuthContent.replace(
    /const EDGE_FUNCTION_SECRET = process\.env\.VITE_EDGE_FUNCTION_SECRET;/,
    `const EDGE_FUNCTION_SECRET = '${FIXED_TOKEN}'; // Fixed value for testing`
  );
  
  fs.writeFileSync(testAuthPath, authContentUpdated);
  console.log('   ✅ Updated test-sdg-auth.js');
  
  // Run tests
  console.log('\n5️⃣ Testing the Edge Functions...');
  
  console.log('\n   Testing debug function...');
  execSync('node src/commands/test-debug-verbose.js', { stdio: 'inherit' });
  
  console.log('\n   Testing sdg-etl function...');
  execSync('node src/commands/test-sdg-auth.js', { stdio: 'inherit' });
  
  console.log('\n🎉 Token synchronization completed!');
} catch (error) {
  console.error('\n❌ Error during the process:', error.message);
  process.exit(1);
}
