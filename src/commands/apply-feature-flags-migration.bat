@echo off
rem Script to apply the feature flags migration for Windows

rem Move to the project root directory
cd /d "%~dp0\..\..\"

rem Check if Node.js is installed
where node >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Error: Node.js is not installed. Please install Node.js to run this script.
    exit /b 1
)

rem Run the migration script
echo Applying feature flags migration...
node src/commands/apply-feature-flags-migration.js

rem Display completion message
if %ERRORLEVEL% equ 0 (
    echo.
    echo ✅ Feature flags migration completed.
    echo You can now access the Feature Flags Manager in the Admin Dashboard.
    echo.
    echo To learn more, read the documentation at:
    echo src/commands/README-feature-flags.md
) else (
    echo.
    echo ❌ Failed to apply feature flags migration.
    echo Please check the error message above and try again.
)
