// Script to test product details page navigation
// Usage: node src/commands/test-product-navigation.js

import { createClient } from '@supabase/supabase-js';

// Define Supabase client directly to avoid TypeScript import issues
// Get Supabase connection info from environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_KEY;

// Ensure environment variables are set
if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Missing Supabase environment variables.');
  console.error('Make sure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in your environment.');
  console.error('Create a .env file based on .env.example or set these variables in your environment.');
  process.exit(1);
}
const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

// Function to get a list of products with their iterations
const getProductsWithIterations = async () => {
  console.log('Fetching products with iterations...');
  
  try {
    const { data: productBases, error: basesError } = await supabase
      .from('product_bases')
      .select(`
        id,
        name,
        iterations:product_iterations!product_iterations_product_base_id_fkey(
          id,
          version,
          iteration
        )
      `)
      .limit(5); // Limit to 5 products for testing
    
    if (basesError) {
      console.error('Error fetching products:', basesError);
      throw basesError;
    }
    
    return productBases;
  } catch (error) {
    console.error('Error in getProductsWithIterations:', error);
    throw error;
  }
};

// Function to generate test URLs for product details page
const generateTestUrls = (products) => {
  console.log('\nGenerating test URLs for product details page...');
  
  const testUrls = [];
  
  products.forEach(product => {
    // Base URL without iteration
    testUrls.push({
      description: `Product ${product.name} without iteration`,
      url: `/products/${product.id}`
    });
    
    // URLs with iterations
    if (product.iterations && product.iterations.length > 0) {
      product.iterations.forEach(iteration => {
        const iterationValue = iteration.iteration || iteration.version;
        if (iterationValue) {
          testUrls.push({
            description: `Product ${product.name} with iteration ${iterationValue}`,
            url: `/products/${product.id}?iteration=${iterationValue}`
          });
        }
      });
    }
    
    // URL with non-existent iteration
    testUrls.push({
      description: `Product ${product.name} with non-existent iteration`,
      url: `/products/${product.id}?iteration=999.999`
    });
  });
  
  // Add a URL with non-existent product ID
  testUrls.push({
    description: 'Non-existent product ID',
    url: '/products/non-existent-id'
  });
  
  return testUrls;
};

// Function to simulate URL parsing
const parseUrl = (url) => {
  console.log(`\nParsing URL: ${url}`);
  
  // Extract the product ID from the URL
  const productIdMatch = url.match(/\/products\/([^/?]+)/);
  const productId = productIdMatch ? productIdMatch[1] : null;
  
  // Extract the iteration parameter from the URL
  const iterationMatch = url.match(/[?&]iteration=([^&]+)/);
  const iteration = iterationMatch ? iterationMatch[1] : null;
  
  return { productId, iteration };
};

// Main function
const main = async () => {
  try {
    // Get products with iterations
    const products = await getProductsWithIterations();
    console.log(`Found ${products.length} products`);
    
    // Generate test URLs
    const testUrls = generateTestUrls(products);
    console.log(`Generated ${testUrls.length} test URLs`);
    
    // Test URL parsing
    console.log('\nTesting URL parsing...');
    testUrls.forEach(test => {
      console.log(`\nTest: ${test.description}`);
      console.log(`URL: ${test.url}`);
      
      const { productId, iteration } = parseUrl(test.url);
      console.log(`  Product ID: ${productId || 'null'}`);
      console.log(`  Iteration: ${iteration || 'null'}`);
      
      // Simulate navigation
      console.log('  Simulating navigation...');
      if (!productId) {
        console.log('  Error: Product ID is required');
      } else {
        console.log(`  Navigating to product ${productId}${iteration ? ` with iteration ${iteration}` : ''}`);
      }
    });
    
    console.log('\nTest complete.');
  } catch (error) {
    console.error('Error in main:', error);
  }
};

// Run the main function
main();
