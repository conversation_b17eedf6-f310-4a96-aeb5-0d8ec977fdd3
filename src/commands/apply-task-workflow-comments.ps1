# apply-task-workflow-comments.ps1
# Run the task workflow and comments migration on Windows

# Get the directory where this script is located
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path

# Navigate to the project root directory (2 levels up)
$ProjectRoot = (Get-Item $ScriptDir).Parent.Parent.FullName
Set-Location -Path $ProjectRoot

# Run the migration script
Write-Host "Running task workflow and comments migration..."
node --experimental-modules src/commands/apply_task_workflow_comments.js

# Check if the script executed successfully
if ($LASTEXITCODE -eq 0) {
    Write-Host "Migration script completed successfully!" -ForegroundColor Green
} else {
    Write-Host "Migration script failed. Check the error messages above." -ForegroundColor Red
    exit 1
}
