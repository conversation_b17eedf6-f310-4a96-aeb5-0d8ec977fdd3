#!/bin/bash
# Script to apply the permission system changes
# Usage: ./src/commands/apply-permissions-system.sh

# Color definitions
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Domain-Specific Permission System Setup ===${NC}"
echo 
echo -e "This script will apply the permission system database changes."
echo -e "It creates all necessary tables, functions, and initial data for"
echo -e "the domain-specific permission system."
echo

# Check if node is installed
if ! command -v node &> /dev/null; then
    echo -e "${RED}Error: Node.js is not installed.${NC}"
    echo -e "Please install Node.js before running this script."
    exit 1
fi

echo -e "${YELLOW}Step 1:${NC} Checking for required files..."
if [ ! -f "src/commands/apply-permissions-system.js" ]; then
    echo -e "${RED}Error: src/commands/apply-permissions-system.js not found.${NC}"
    exit 1
fi

if [ ! -f "supabase/migrations/20250324_create_permission_system.sql" ]; then
    echo -e "${RED}Error: Migration file not found.${NC}"
    echo -e "Please ensure that supabase/migrations/20250324_create_permission_system.sql exists."
    exit 1
fi

# Create the exec_sql function if needed
echo -e "${YELLOW}Step 2:${NC} Checking for exec_sql function..."
echo -e "Note: You may need to create this function in your Supabase project if it doesn't exist."
echo -e "SQL to create the function:"
echo -e "${BLUE}"
echo "CREATE OR REPLACE FUNCTION exec_sql(sql TEXT) RETURNS VOID AS \$\$"
echo "BEGIN"
echo "  EXECUTE sql;"
echo "END;"
echo "\$\$ LANGUAGE plpgsql SECURITY DEFINER;"
echo -e "${NC}"

# Run the permission system application script
echo -e "${YELLOW}Step 3:${NC} Applying permission system changes..."
node src/commands/apply-permissions-system.js

# Check if the script was successful
if [ $? -eq 0 ]; then
  echo -e "${GREEN}✓ Permission system changes applied successfully!${NC}"
  echo
  echo -e "Next steps:"
  echo -e "1. Update UI components to check permissions before showing action buttons"
  echo -e "2. Test with different user roles (use the testing tools in Admin panel)"
  echo -e "3. See src/docs/DOMAIN-SPECIFIC-PERMISSIONS.md for more information"
else
  echo -e "${RED}✗ Error applying permission system changes.${NC}"
  echo -e "Please check the error messages above."
fi

echo
echo -e "${BLUE}=== Script Completed ===${NC}"
