import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

// Initialize dotenv
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Supabase URL or Key is missing in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Main function to set up the entire gamification system
 */
async function setupGamificationSystem() {
  console.log('🎮 Setting up complete gamification system...');

  try {
    // Run all the necessary setup scripts
    console.log('\n📊 Running tables setup scripts...');
    
    // Import and run each setup script
    try {
      const { setupGamificationTables } = await import('./setup-gamification-tables-fixed.js');
      await setupGamificationTables();
      console.log('✅ Core gamification tables created successfully');
    } catch (err) {
      console.error('❌ Error creating core tables:', err.message);
    }
    
    try {
      const { setupGamificationAdminTables } = await import('./setup-gamification-admin-tables-fixed.js');
      await setupGamificationAdminTables();
      console.log('✅ Admin tables created successfully');
    } catch (err) {
      console.error('❌ Error creating admin tables:', err.message);
    }
    
    try {
      const { setupGamificationRules } = await import('./setup-gamification-rules-fixed.js');
      await setupGamificationRules();
      console.log('✅ Default rules created successfully');
    } catch (err) {
      console.error('❌ Error creating default rules:', err.message);
    }
    
    // Create necessary SQL functions
    console.log('\n📊 Setting up SQL functions for analytics...');
    await setupSQLFunctions();
    
    console.log('\n✅ Gamification system setup complete!');
    
    console.log('\n🔍 Next steps:');
    console.log('  1. Restart your application to activate the gamification system');
    console.log('  2. Visit the Admin panel and select the Gamification section');
    console.log('  3. Configure additional rules and achievements if needed');
    
    return true;
  } catch (err) {
    console.error('❌ Error setting up gamification system:', err);
    return false;
  }
}

/**
 * Create SQL functions required for analytics
 */
async function setupSQLFunctions() {
  console.log('Setting up SQL functions for analytics...');
  
  // Define SQL functions
  const sqlFunctions = [
    {
      name: 'get_achievement_stats',
      sql: `
        CREATE OR REPLACE FUNCTION get_achievement_stats()
        RETURNS TABLE (
          achievement_id UUID,
          name VARCHAR,
          name_ar VARCHAR,
          earned_count BIGINT,
          category VARCHAR,
          points INTEGER
        )
        LANGUAGE SQL
        AS $$
          SELECT 
            a.id as achievement_id,
            a.name,
            a.name_ar,
            COUNT(ua.id) as earned_count,
            a.category,
            a.points
          FROM 
            achievements a
          LEFT JOIN 
            user_achievements ua ON a.id = ua.achievement_id
          GROUP BY 
            a.id, a.name, a.name_ar, a.category, a.points
          ORDER BY 
            earned_count DESC;
        $$;
      `
    },
    {
      name: 'get_leaderboard',
      sql: `
        CREATE OR REPLACE FUNCTION get_leaderboard(limit_val INTEGER, offset_val INTEGER)
        RETURNS TABLE (
          user_id UUID,
          display_name TEXT,
          total_points INTEGER,
          level INTEGER,
          achievements_count BIGINT,
          rank BIGINT
        )
        LANGUAGE SQL
        AS $$
          WITH ranked_users AS (
            SELECT 
              up.user_id,
              p.raw_user_meta_data->>'name' as display_name,
              up.total_points,
              up.level,
              COUNT(ua.id) as achievements_count,
              ROW_NUMBER() OVER (ORDER BY up.total_points DESC) as rank
            FROM 
              user_points up
            LEFT JOIN 
              auth.users p ON up.user_id = p.id
            LEFT JOIN 
              user_achievements ua ON up.user_id = ua.user_id
            GROUP BY 
              up.user_id, p.raw_user_meta_data, up.total_points, up.level
          )
          SELECT * FROM ranked_users
          LIMIT limit_val
          OFFSET offset_val;
        $$;
      `
    },
    {
      name: 'get_event_type_analytics',
      sql: `
        CREATE OR REPLACE FUNCTION get_event_type_analytics()
        RETURNS TABLE (
          event_type VARCHAR,
          count BIGINT,
          users_count BIGINT,
          first_occurred_at TIMESTAMPTZ,
          last_occurred_at TIMESTAMPTZ
        )
        LANGUAGE SQL
        AS $$
          SELECT 
            ge.event_type,
            COUNT(ge.id) as count,
            COUNT(DISTINCT ge.user_id) as users_count,
            MIN(ge.occurred_at) as first_occurred_at,
            MAX(ge.occurred_at) as last_occurred_at
          FROM 
            gamification_events ge
          GROUP BY 
            ge.event_type
          ORDER BY 
            count DESC;
        $$;
      `
    },
    {
      name: 'get_time_series_data',
      sql: `
        CREATE OR REPLACE FUNCTION get_time_series_data(
          period_val VARCHAR,
          start_date TIMESTAMPTZ,
          end_date TIMESTAMPTZ,
          event_type_val VARCHAR DEFAULT NULL
        )
        RETURNS TABLE (
          period VARCHAR,
          count BIGINT,
          event_type VARCHAR
        )
        LANGUAGE SQL
        AS $$
          WITH time_periods AS (
            SELECT 
              CASE 
                WHEN period_val = 'day' THEN TO_CHAR(ge.occurred_at, 'YYYY-MM-DD')
                WHEN period_val = 'week' THEN TO_CHAR(DATE_TRUNC('week', ge.occurred_at), 'YYYY-MM-DD')
                WHEN period_val = 'month' THEN TO_CHAR(DATE_TRUNC('month', ge.occurred_at), 'YYYY-MM')
                WHEN period_val = 'year' THEN TO_CHAR(DATE_TRUNC('year', ge.occurred_at), 'YYYY')
                ELSE TO_CHAR(ge.occurred_at, 'YYYY-MM-DD')
              END as period,
              ge.event_type,
              ge.id
            FROM 
              gamification_events ge
            WHERE 
              ge.occurred_at BETWEEN start_date AND end_date
              AND (event_type_val IS NULL OR ge.event_type = event_type_val)
          )
          SELECT 
            period,
            COUNT(id) as count,
            event_type
          FROM 
            time_periods
          GROUP BY 
            period, event_type
          ORDER BY 
            period ASC;
        $$;
      `
    },
    {
      name: 'get_user_engagement_stats',
      sql: `
        CREATE OR REPLACE FUNCTION get_user_engagement_stats()
        RETURNS TABLE (
          total_users BIGINT,
          active_users BIGINT,
          average_points NUMERIC,
          average_achievements NUMERIC
        )
        LANGUAGE SQL
        AS $$
          WITH stats AS (
            SELECT 
              COUNT(DISTINCT up.user_id) as total_users,
              COUNT(DISTINCT CASE WHEN ge.occurred_at > NOW() - INTERVAL '30 days' THEN ge.user_id END) as active_users,
              AVG(up.total_points) as average_points,
              (SELECT COUNT(ua.id) FROM user_achievements ua) / 
                NULLIF(COUNT(DISTINCT up.user_id), 0) as average_achievements
            FROM 
              user_points up
            LEFT JOIN 
              gamification_events ge ON up.user_id = ge.user_id
          )
          SELECT 
            total_users,
            active_users,
            average_points,
            average_achievements
          FROM 
            stats;
        $$;
      `
    }
  ];
  
  // Create each SQL function
  for (const func of sqlFunctions) {
    try {
      const { error } = await supabase.rpc('execute_sql', {
        sql: func.sql
      });
      
      if (error) {
        console.error(`Error creating function ${func.name}: ${error.message}`);
      } else {
        console.log(`✓ Function ${func.name} created successfully`);
      }
    } catch (err) {
      console.error(`Error creating function ${func.name}:`, err);
    }
  }
}

// Run the setup if this script is executed directly
if (import.meta.url.endsWith('setup-gamification-complete.js')) {
  setupGamificationSystem()
    .then(success => {
      if (success) {
        console.log('\n✅ Gamification system setup completed successfully.');
        process.exit(0);
      } else {
        console.error('\n❌ Gamification system setup failed.');
        process.exit(1);
      }
    })
    .catch(err => {
      console.error('Fatal error:', err);
      process.exit(1);
    });
}

export { setupGamificationSystem, setupSQLFunctions };
