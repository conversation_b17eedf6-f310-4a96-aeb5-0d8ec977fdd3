// Script to prepare the SQL migration for the permission system
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// ANSI color codes for terminal output
const GREEN = '\x1b[32m';
const YELLOW = '\x1b[33m';
const BLUE = '\x1b[34m';
const RESET = '\x1b[0m';
const RED = '\x1b[31m';

console.log(`${BLUE}=== Permission System Migration Preparation ===${RESET}\n`);

// Read the SQL file
function readMigrationFile() {
  try {
    const filePath = path.join(__dirname, '../../supabase/migrations/20250324_create_permission_system.sql');
    const sql = fs.readFileSync(filePath, 'utf8');
    console.log(`${GREEN}✓ Migration file read successfully${RESET}`);
    return sql;
  } catch (error) {
    console.error(`${RED}✗ Error reading migration file:${RESET}`, error.message);
    return null;
  }
}

// Main function
function main() {
  try {
    console.log(`${BLUE}Reading SQL migration file...${RESET}`);
    const sql = readMigrationFile();
    
    if (!sql) {
      console.error(`${RED}Unable to read migration file. Aborting.${RESET}`);
      process.exit(1);
    }
    
    console.log('\n--------------------------------------------------------------\n');
    console.log(`${YELLOW}MIGRATION INSTRUCTIONS:${RESET}\n`);
    console.log('1. Open your Supabase dashboard');
    console.log('2. Navigate to the SQL Editor');
    console.log('3. Create a new query');
    console.log('4. Paste the SQL below into the editor');
    console.log('5. Run the query to apply all permission system changes');
    console.log('\n--------------------------------------------------------------\n');
    
    // Print summary of what will be created
    console.log(`${BLUE}This migration will create:${RESET}\n`);
    console.log('- system_roles: Roles in the system (admin, viewer, etc.)');
    console.log('- statistical_domains: Statistical categories (social, economic, etc.)');
    console.log('- system_modules: Functional areas of the application');
    console.log('- permission_levels: Hierarchical permission levels');
    console.log('- user_roles: User role assignments with domain context');
    console.log('- role_module_permissions: Role-module permission mapping');
    console.log('- product_permissions: Product-specific permission overrides');
    
    console.log(`\n${BLUE}Functions:${RESET}\n`);
    console.log('- check_user_permission: Check if a user has a specific permission');
    console.log('- get_user_permissions: Get all permissions for a user');
    console.log('- assign_user_role: Assign a role to a user');
    console.log('- remove_user_role: Remove a role from a user');
    
    console.log('\n--------------------------------------------------------------\n');
    console.log(`${YELLOW}SQL MIGRATION:${RESET}\n`);
    
    // Print the SQL
    console.log(sql);
    
    console.log('\n--------------------------------------------------------------\n');
    console.log(`${GREEN}✓ SQL migration prepared successfully!${RESET}\n`);
    console.log('Copy the SQL above and run it in your Supabase SQL Editor.');
    
  } catch (error) {
    console.error(`${RED}Error preparing migration:${RESET}`, error.message);
    process.exit(1);
  }
}

// Run the script
main();
