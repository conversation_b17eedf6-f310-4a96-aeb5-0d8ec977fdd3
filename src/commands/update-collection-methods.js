
// This file is for documentation purposes only and represents the SQL commands
// that should be executed to update the collection methods in the database.

// Updates for survey_collection_methods table
// 1. Face-to-Face -> CAPI
// UPDATE survey_collection_methods 
// SET name = 'CAPI', display_name = 'CAPI (Computer-Assisted Personal Interviewing)'
// WHERE name = 'Face-to-Face';

// 2. Phone Interview -> CATI
// UPDATE survey_collection_methods
// SET name = 'CATI', display_name = 'CATI (Computer-Assisted Telephone Interviewing)'
// WHERE name = 'Phone Interview';

// 3. Online Survey -> CAWI
// UPDATE survey_collection_methods
// SET name = 'CAWI', display_name = 'CAWI (Computer-Assisted Web Interviewing)'
// WHERE name = 'Online Survey';

// 4. Paper Forms -> PAPI
// UPDATE survey_collection_methods
// SET name = 'PAPI', display_name = 'PAPI (Paper-and-Pencil Interviewing)'
// WHERE name = 'Paper Forms';

// 5. SMS Survey -> TAPI
// UPDATE survey_collection_methods
// SET name = 'TAPI', display_name = 'TAPI (Tablet-Assisted Personal Interviewing)'
// WHERE name = 'SMS Survey';
