#!/bin/bash
# Script to apply the feature flags migration

# Move to the project root directory
cd "$(dirname "$0")/../.."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "Error: Node.js is not installed. Please install Node.js to run this script."
    exit 1
fi

# Run the migration script
echo "Applying feature flags migration..."
node src/commands/apply-feature-flags-migration.js

# Display completion message
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Feature flags migration completed."
    echo "You can now access the Feature Flags Manager in the Admin Dashboard."
    echo ""
    echo "To learn more, read the documentation at:"
    echo "src/commands/README-feature-flags.md"
else
    echo ""
    echo "❌ Failed to apply feature flags migration."
    echo "Please check the error message above and try again."
fi
