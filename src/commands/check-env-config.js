/**
 * Environment Variable Configuration Checker
 * 
 * This script verifies that all required Supabase environment variables
 * are properly set in the .env file.
 * 
 * Usage:
 * node src/commands/check-env-config.js
 */

import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Load environment variables from .env file
dotenv.config();

// Get current file directory (equivalent to __dirname in CommonJS)
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

console.log(`\n${colors.blue}🔍 Checking Environment Variables Configuration${colors.reset}\n`);

// Define required variables
const requiredBackendVars = [
  'SUPABASE_URL',
  'SUPABASE_ANON_KEY',
  'SUPABASE_SERVICE_ROLE_KEY',
];

const requiredFrontendVars = [
  'VITE_SUPABASE_URL',
  'VITE_SUPABASE_ANON_KEY',
];

const optionalVars = [
  'SUPABASE_CONNECTION_STRING'
];

// Check if .env file exists
const envPath = path.join(process.cwd(), '.env');
if (!fs.existsSync(envPath)) {
  console.log(`${colors.red}✗ .env file not found in project root!${colors.reset}`);
  console.log(`${colors.yellow}  Create a .env file with required Supabase configuration.${colors.reset}`);
  process.exit(1);
}

// Check backend variables
console.log(`${colors.cyan}1. Checking Backend Variables${colors.reset}`);
let backendOk = true;

requiredBackendVars.forEach(varName => {
  if (process.env[varName]) {
    console.log(`   ${varName}: ${colors.green}✓ Set${colors.reset}`);
  } else {
    console.log(`   ${varName}: ${colors.red}✗ Missing${colors.reset}`);
    backendOk = false;
  }
});

// Check frontend variables
console.log(`\n${colors.cyan}2. Checking Frontend (Vite) Variables${colors.reset}`);
let frontendOk = true;

requiredFrontendVars.forEach(varName => {
  if (process.env[varName]) {
    console.log(`   ${varName}: ${colors.green}✓ Set${colors.reset}`);
  } else {
    console.log(`   ${varName}: ${colors.red}✗ Missing${colors.reset}`);
    frontendOk = false;
  }
});

// Check optional variables
console.log(`\n${colors.cyan}3. Checking Optional Variables${colors.reset}`);

optionalVars.forEach(varName => {
  if (process.env[varName]) {
    console.log(`   ${varName}: ${colors.green}✓ Set${colors.reset}`);
  } else {
    console.log(`   ${varName}: ${colors.yellow}⚠️ Not set (optional)${colors.reset}`);
  }
});

// Check Vite URL and ANON_KEY consistency
console.log(`\n${colors.cyan}4. Checking Consistency${colors.reset}`);

if (process.env.SUPABASE_URL && process.env.VITE_SUPABASE_URL) {
  if (process.env.SUPABASE_URL === process.env.VITE_SUPABASE_URL) {
    console.log(`   URL consistency: ${colors.green}✓ SUPABASE_URL and VITE_SUPABASE_URL match${colors.reset}`);
  } else {
    console.log(`   URL consistency: ${colors.red}✗ SUPABASE_URL and VITE_SUPABASE_URL do not match!${colors.reset}`);
    console.log(`     - SUPABASE_URL: ${process.env.SUPABASE_URL}`);
    console.log(`     - VITE_SUPABASE_URL: ${process.env.VITE_SUPABASE_URL}`);
  }
}

if (process.env.SUPABASE_ANON_KEY && process.env.VITE_SUPABASE_ANON_KEY) {
  if (process.env.SUPABASE_ANON_KEY === process.env.VITE_SUPABASE_ANON_KEY) {
    console.log(`   ANON_KEY consistency: ${colors.green}✓ SUPABASE_ANON_KEY and VITE_SUPABASE_ANON_KEY match${colors.reset}`);
  } else {
    console.log(`   ANON_KEY consistency: ${colors.red}✗ SUPABASE_ANON_KEY and VITE_SUPABASE_ANON_KEY do not match!${colors.reset}`);
  }
}

// Overall summary
console.log(`\n${colors.cyan}5. Summary${colors.reset}`);
if (backendOk && frontendOk) {
  console.log(`${colors.green}✓ All required environment variables are properly configured!${colors.reset}`);
  console.log(`  Your application should connect to Supabase correctly.`);
} else {
  console.log(`${colors.red}✗ Some required environment variables are missing!${colors.reset}`);
  console.log(`  Please update your .env file with the missing variables.`);
  
  if (!backendOk) {
    console.log(`  Backend scripts may not function correctly without required variables.`);
  }
  
  if (!frontendOk) {
    console.log(`  Frontend application may show a blank screen or connection errors.`);
  }
  
  console.log(`\n${colors.yellow}⚠️ See docs/SUPABASE-ENV-SETUP.md for detailed instructions.${colors.reset}`);
}

console.log(`\n${colors.blue}ℹ️  After updating environment variables, restart your development server${colors.reset}\n`);
