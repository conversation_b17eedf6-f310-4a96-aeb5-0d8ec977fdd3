/**
 * Apply the permission groups migration script
 * This adds support for predefined role+domain combinations and user assignments
 */
import { createClient } from '@supabase/supabase-js';
import * as fs from 'fs';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';

// ES Module equivalent for __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables or use defaults
const supabaseUrl = process.env.SUPABASE_URL || 'http://localhost:54321';
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSJ9.vI9obAHOGyVVKa3pD--kJlyxp-Z2zV9UUMAhKpNLAcU';

/**
 * Executes SQL directly using the Supabase REST API
 * This is a fallback method when RPC is not available
 */
async function executeSqlDirectly(supabase, sql) {
  // Using the REST API endpoint directly
  const response = await fetch(`${supabaseUrl}/rest/v1/`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'apikey': supabaseKey,
      'Authorization': `Bearer ${supabaseKey}`,
      'Prefer': 'params=single-object'
    },
    body: JSON.stringify({
      query: sql
    })
  });
  
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`SQL execution failed: ${errorText}`);
  }
  
  return await response.json();
}

async function readFile(filePath) {
  return fs.promises.readFile(filePath, 'utf8');
}

async function applyPermissionGroups() {
  console.log('Connecting to Supabase...');
  const supabase = createClient(supabaseUrl, supabaseKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });

  try {
    // Read the SQL files
    const permissionGroupsSqlPath = join(__dirname, '..', '..', 'supabase', 'migrations', '20250324_permission_groups.sql');
    const permissionGroupsSqlContent = await readFile(permissionGroupsSqlPath);
    
    console.log('Applying permission groups migration...');
    
    // First, disable RLS for easier migration
    try {
      // Simple SQL to disable RLS
      const disableRlsSql = `
        -- Temporarily disable RLS for migration
        ALTER TABLE IF EXISTS public.system_roles DISABLE ROW LEVEL SECURITY;
        ALTER TABLE IF EXISTS public.statistical_domains DISABLE ROW LEVEL SECURITY;
        ALTER TABLE IF EXISTS public.user_roles DISABLE ROW LEVEL SECURITY;
      `;
      await executeSqlDirectly(supabase, disableRlsSql);
      console.log('Successfully disabled RLS for migration.');
    } catch (disableErr) {
      console.warn('Warning: Could not disable RLS, continuing anyway...', disableErr.message);
    }
    
    // Apply permission groups migration
    try {
      // First attempt to use RPC
      const { error } = await supabase.rpc('run_sql_script', {
        sql_script: permissionGroupsSqlContent
      });
      
      if (error) {
        console.log('RPC failed, applying migration in smaller chunks...');
        // Break down the SQL into smaller chunks for direct execution
        const statements = permissionGroupsSqlContent.split(';').filter(stmt => stmt.trim().length > 0);
        
        for (const stmt of statements) {
          try {
            await executeSqlDirectly(supabase, stmt + ';');
          } catch (stmtErr) {
            console.warn(`Warning during migration: ${stmtErr.message}`);
            // Continue with next statement
          }
        }
      }
      
      // Re-enable RLS after operation
      try {
        const enableRlsSql = `
          -- Re-enable RLS
          ALTER TABLE IF EXISTS public.system_roles ENABLE ROW LEVEL SECURITY;
          ALTER TABLE IF EXISTS public.statistical_domains ENABLE ROW LEVEL SECURITY;
          ALTER TABLE IF EXISTS public.user_roles ENABLE ROW LEVEL SECURITY;
          ALTER TABLE IF EXISTS public.permission_groups ENABLE ROW LEVEL SECURITY;
          ALTER TABLE IF EXISTS public.user_permission_groups ENABLE ROW LEVEL SECURITY;
        `;
        await executeSqlDirectly(supabase, enableRlsSql);
      } catch (enableErr) {
        console.warn('Warning: Could not re-enable RLS:', enableErr.message);
      }
      
      console.log('Permission groups migration applied successfully!');
      
      // Get all permission groups to verify the migration worked
      try {
        const { data, error: groupsError } = await supabase.rpc('get_permission_groups');
        
        if (groupsError) {
          console.warn('Warning: Could not verify permission groups:', groupsError.message);
        } else {
          console.log(`\nSuccessfully created ${data.length} permission groups:`);
          data.forEach(group => {
            console.log(`- ${group.name} (${group.role_name}${group.domain_name ? ` in ${group.domain_name}` : ' globally'})`);
          });
        }
      } catch (verifyErr) {
        console.warn('Warning: Could not verify permission groups:', verifyErr.message);
      }
      
    } catch (err) {
      console.error('Error applying permission groups migration:', err);
      throw err;
    }
    
    console.log('\nPermission groups have been applied!');
    console.log('You can now use the Role and Domain Selector in the Admin panel.');
    
  } catch (err) {
    console.error('Failed to apply permission groups:', err);
  }
}

// Execute the main function and catch any errors
applyPermissionGroups().catch(console.error);
