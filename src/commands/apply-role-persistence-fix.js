/**
 * Apply the role persistence fix to improve role assignment stability
 * This creates a separate table and functions for role persistence that aren't affected by schema cache issues
 */
import { createClient } from '@supabase/supabase-js';
import * as fs from 'fs';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';

// ES Module equivalent for __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables or use defaults
const supabaseUrl = process.env.SUPABASE_URL || 'http://localhost:54321';
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSJ9.vI9obAHOGyVVKa3pD--kJlyxp-Z2zV9UUMAhKpNLAcU';

async function readFile(filePath) {
  return fs.promises.readFile(filePath, 'utf8');
}

async function applyRolePersistenceFix() {
  console.log('Connecting to Supabase...');
  const supabase = createClient(supabaseUrl, supabaseKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });

  try {
    // Read the SQL file
    const migrationSqlPath = join(__dirname, '..', '..', 'supabase', 'migrations', '20250324_role_persistence_fix.sql');
    console.log(`Reading migration file from: ${migrationSqlPath}`);
    const migrationSqlContent = await readFile(migrationSqlPath);
    
    console.log('Applying role persistence fix...');
    
    // Execute the SQL statements
    const { error } = await supabase.rpc('run_sql_script', {
      sql_script: migrationSqlContent
    });
    
    if (error) {
      console.warn('Warning: RPC execution had issues:', error.message);
      console.log('Trying direct SQL approach instead...');
      
      // Split into individual statements for better error handling
      const statements = migrationSqlContent
        .split(';')
        .filter(stmt => stmt.trim().length > 0)
        .map(stmt => stmt.trim() + ';');
      
      for (const stmt of statements) {
        try {
          await supabase.rpc('run_sql', { sql: stmt });
        } catch (stmtErr) {
          console.warn(`Warning during statement: ${stmtErr.message}`);
          // Continue with next statement
        }
      }
    }
    
    // Verify the table and functions were created
    try {
      const { data: tableExists, error: tableError } = await supabase.rpc('check_table_exists', {
        table_name: 'user_role_backup'
      });
      
      if (tableError) {
        console.warn('Could not verify table existence:', tableError.message);
      } else if (tableExists) {
        console.log('✅ user_role_backup table created successfully');
      } else {
        console.warn('⚠️ user_role_backup table may not have been created');
      }
      
      // Test the persist_role_with_backup function with a test user
      try {
        console.log('Testing the new persist_role_with_backup function...');
        const { data: currentUserId } = await supabase.auth.getUser();
        
        if (currentUserId) {
          const { error: testError } = await supabase.rpc('persist_role_with_backup', {
            p_user_id: currentUserId.user.id,
            p_role_name: 'viewer'
          });
          
          if (testError) {
            console.warn('Test function call failed:', testError.message);
          } else {
            console.log('✅ persist_role_with_backup function works correctly');
          }
        }
      } catch (testErr) {
        console.warn('Test function error:', testErr.message);
      }
    } catch (verifyErr) {
      console.warn('Verification error:', verifyErr.message);
    }
    
    console.log('\nRole persistence fix applied!');
    console.log('This fix provides a more reliable way to assign and persist roles.');
    console.log('You can now use the following new functions:');
    console.log('- persist_role_with_backup: Assigns a role with better persistence');
    console.log('- get_user_role_from_backup: Gets a user\'s role from the backup table');
    console.log('- refresh_jwt_with_role: Force refreshes JWT claims with the current role');
    
  } catch (err) {
    console.error('Failed to apply role persistence fix:', err);
  }
}

// Execute the main function and catch any errors
applyRolePersistenceFix().catch(console.error);
