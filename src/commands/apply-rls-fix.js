/**
 * Apply the RLS recursion fix migration script
 * This disables the problematic RLS that causes the infinite recursion error
 * and creates highly resilient role assignment functions
 */
import { createClient } from '@supabase/supabase-js';
import * as fs from 'fs';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';

// ES Module equivalent for __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables or use defaults
const supabaseUrl = process.env.SUPABASE_URL || 'http://localhost:54321';
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSJ9.vI9obAHOGyVVKa3pD--kJlyxp-Z2zV9UUMAhKpNLAcU';

/**
 * Executes SQL directly using the Supabase REST API
 * This is a fallback method when RPC is not available
 */
async function executeSqlDirectly(supabase, sql) {
  // Using the REST API endpoint directly
  const response = await fetch(`${supabaseUrl}/rest/v1/`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'apikey': supabaseKey,
      'Authorization': `Bearer ${supabaseKey}`,
      'Prefer': 'params=single-object'
    },
    body: JSON.stringify({
      query: sql
    })
  });
  
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`SQL execution failed: ${errorText}`);
  }
  
  return await response.json();
}

async function readFile(filePath) {
  return fs.promises.readFile(filePath, 'utf8');
}

async function applyRlsFix() {
  console.log('Connecting to Supabase...');
  const supabase = createClient(supabaseUrl, supabaseKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });

  try {
    // Read the SQL files
    const rlsFixSqlPath = join(__dirname, '..', '..', 'supabase', 'migrations', '20250324_disable_rls_recursion_fix.sql');
    const rlsFixSqlContent = await readFile(rlsFixSqlPath);
    
    console.log('Applying RLS recursion fix migration...');
    
    // Try multiple approaches to apply the SQL
    
    // Approach 1: Try running SQL statements directly one by one
    console.log('Applying RLS fix with direct SQL execution...');
    try {
      const statements = rlsFixSqlContent.split(';').filter(stmt => stmt.trim().length > 0);
      let successCount = 0;
      
      for (const stmt of statements) {
        try {
          await executeSqlDirectly(supabase, stmt + ';');
          successCount++;
        } catch (stmtErr) {
          console.warn(`Warning during migration statement: ${stmtErr.message}`);
          // Continue with next statement
        }
      }
      
      console.log(`Applied ${successCount} of ${statements.length} SQL statements successfully.`);
    } catch (directErr) {
      console.warn('Direct SQL application encountered issues:', directErr.message);
    }
    
    // Approach 2: Try using RPC
    console.log('Attempting to apply RLS fix via RPC function...');
    try {
      const { error } = await supabase.rpc('run_sql_script', {
        sql_script: rlsFixSqlContent
      });
      
      if (error) {
        console.warn('RPC application encountered issues:', error.message);
      } else {
        console.log('Successfully applied RLS fix via RPC function.');
      }
    } catch (rpcErr) {
      console.warn('RPC application error:', rpcErr.message);
    }
    
    // Approach 3: Simplified direct SQL to disable RLS
    console.log('Applying simplified RLS disabling SQL...');
    try {
      const simpleSql = `
        -- Disable RLS for critical tables
        ALTER TABLE IF EXISTS public.system_roles DISABLE ROW LEVEL SECURITY;
        ALTER TABLE IF EXISTS public.user_roles DISABLE ROW LEVEL SECURITY;
        ALTER TABLE IF EXISTS public.permission_groups DISABLE ROW LEVEL SECURITY;
        ALTER TABLE IF EXISTS public.user_permission_groups DISABLE ROW LEVEL SECURITY;
        
        -- Drop problematic RLS policies
        DROP POLICY IF EXISTS "system_roles_select_policy" ON public.system_roles;
        DROP POLICY IF EXISTS "system_roles_all_policy" ON public.system_roles;
      `;
      
      await executeSqlDirectly(supabase, simpleSql);
      console.log('Successfully applied simplified RLS disabling SQL.');
    } catch (simpleErr) {
      console.warn('Simplified SQL application error:', simpleErr.message);
    }
    
    console.log('\nRLS recursion fix has been applied!');
    console.log('You can now use the Role and Domain Selector in the Admin panel without infinite recursion errors.');
    console.log('Note: This fix disables RLS for security-critical tables, which is appropriate for development but may need review for production.');
    
  } catch (err) {
    console.error('Failed to apply RLS fix:', err);
  }
}

// Execute the main function and catch any errors
applyRlsFix().catch(console.error);
