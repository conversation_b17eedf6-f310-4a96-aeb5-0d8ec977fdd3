@echo off
echo Applying workflow stage migrations using Supabase CLI...

REM Change to the supabase directory
cd /d "%~dp0\..\..\supabase" || (
    echo Could not find supabase directory
    exit /b 1
)

REM Check if supabase CLI is installed
where supabase >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo X Error: supabase CLI is not installed or not in PATH
    echo Please install the Supabase CLI: https://supabase.com/docs/guides/cli
    exit /b 1
)

echo Running Supabase database migrations...
supabase db reset
if %ERRORLEVEL% neq 0 (
    echo X Error applying migrations with 'supabase db reset'
    exit /b 1
)

echo.
echo ✓ Migrations applied successfully!
echo You should now be able to create and manage workflow stages.
echo Please restart your application if it's currently running.
