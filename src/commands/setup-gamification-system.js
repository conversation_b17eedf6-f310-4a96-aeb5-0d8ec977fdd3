// @ts-check
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

/**
 * Comprehensive script to set up the entire gamification system in one go.
 * This script creates:
 * 1. Core gamification tables (achievements, user_achievements, etc.)
 * 2. Admin configuration tables
 * 3. Default rules and achievements
 */

// Supabase client setup
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('ERROR: Supabase credentials not found in environment variables.');
  console.error('Please ensure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function createTables() {
  console.log('\n🔧 Setting up core gamification tables...');

  try {
    // Create achievements table
    const { data: achievementsExists, error: achievementsCheckError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'achievements')
      .maybeSingle();
      
    if (!achievementsExists) {
      // Table doesn't exist, create it
      const { error: createError } = await supabase.rpc('execute_sql', {
        sql: `
          CREATE TABLE IF NOT EXISTS achievements (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            name VARCHAR(255) NOT NULL,
            description TEXT,
            category VARCHAR(100),
            icon VARCHAR(255),
            points INTEGER DEFAULT 0,
            difficulty VARCHAR(50) DEFAULT 'medium',
            is_hidden BOOLEAN DEFAULT false,
            requirements JSONB,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `
      });
      
      if (createError) {
        throw new Error(`Failed to create achievements table: ${createError.message}`);
      }
    }

    // Create user_achievements table
    const { data: userAchievementsExists, error: userAchievementsCheckError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'user_achievements')
      .maybeSingle();
      
    if (!userAchievementsExists) {
      // Table doesn't exist, create it
      const { error: createError } = await supabase.rpc('execute_sql', {
        sql: `
          CREATE TABLE IF NOT EXISTS user_achievements (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
            achievement_id UUID NOT NULL REFERENCES achievements(id) ON DELETE CASCADE,
            earned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            UNIQUE(user_id, achievement_id)
          );
        `
      });
      
      if (createError) {
        throw new Error(`Failed to create user_achievements table: ${createError.message}`);
      }
    }

    // Create user_gamification_profiles table
    const { data: profilesExists, error: profilesCheckError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'user_gamification_profiles')
      .maybeSingle();
      
    if (!profilesExists) {
      // Table doesn't exist, create it
      const { error: createError } = await supabase.rpc('execute_sql', {
        sql: `
          CREATE TABLE IF NOT EXISTS user_gamification_profiles (
            id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
            points INTEGER DEFAULT 0,
            level INTEGER DEFAULT 1,
            last_active TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            streak_days INTEGER DEFAULT 0,
            streak_last_date DATE DEFAULT CURRENT_DATE,
            settings JSONB DEFAULT '{}',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `
      });
      
      if (createError) {
        throw new Error(`Failed to create user_gamification_profiles table: ${createError.message}`);
      }
    }

    // Create gamification_events table
    const { data: eventsExists, error: eventsCheckError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'gamification_events')
      .maybeSingle();
      
    if (!eventsExists) {
      // Table doesn't exist, create it
      const { error: createError } = await supabase.rpc('execute_sql', {
        sql: `
          CREATE TABLE IF NOT EXISTS gamification_events (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
            event_type VARCHAR(100) NOT NULL,
            event_data JSONB,
            occurred_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `
      });
      
      if (createError) {
        throw new Error(`Failed to create gamification_events table: ${createError.message}`);
      }
    }

    // Create gamification_rules table
    const { data: rulesExists, error: rulesCheckError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'gamification_rules')
      .maybeSingle();
      
    if (!rulesExists) {
      // Table doesn't exist, create it
      const { error: createError } = await supabase.rpc('execute_sql', {
        sql: `
          CREATE TABLE IF NOT EXISTS gamification_rules (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            name VARCHAR(255) NOT NULL,
            description TEXT,
            event_type VARCHAR(100) NOT NULL,
            conditions JSONB NOT NULL,
            actions JSONB NOT NULL,
            is_active BOOLEAN DEFAULT true,
            priority INTEGER DEFAULT 100,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `
      });
      
      if (createError) {
        throw new Error(`Failed to create gamification_rules table: ${createError.message}`);
      }
    }

    // Create gamification_config table
    const { data: configExists, error: configCheckError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'gamification_config')
      .maybeSingle();
      
    if (!configExists) {
      // Table doesn't exist, create it
      const { error: createError } = await supabase.rpc('execute_sql', {
        sql: `
          CREATE TABLE IF NOT EXISTS gamification_config (
            key VARCHAR(255) PRIMARY KEY,
            value JSONB NOT NULL,
            description TEXT,
            category VARCHAR(100),
            is_system BOOLEAN DEFAULT false,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `
      });
      
      if (createError) {
        throw new Error(`Failed to create gamification_config table: ${createError.message}`);
      }
    }

    console.log('✅ Core gamification tables created or validated successfully');
    return true;
  } catch (error) {
    console.error('❌ Error creating gamification tables:', error.message);
    return false;
  }
}

async function createDefaultConfig() {
  console.log('\n🔧 Setting up default gamification configuration...');

  try {
    const configs = [
      {
        key: 'point_values',
        value: {
          product_created: 50,
          product_updated: 10,
          comment_added: 5,
          achievement_earned: 25,
          profile_completed: 100,
          daily_login: 10
        },
        description: 'Point values for various actions',
        category: 'points',
        is_system: true
      },
      {
        key: 'level_formula',
        value: {
          formula: 'FLOOR(POWER(points / 100, 0.5))',
          description: 'Level is calculated as the square root of points divided by 100, rounded down'
        },
        description: 'Formula for calculating user level based on points',
        category: 'levels',
        is_system: true
      },
      {
        key: 'gamification_enabled',
        value: { enabled: true },
        description: 'Master switch for the gamification system',
        category: 'system',
        is_system: true
      },
      {
        key: 'achievements_visible',
        value: { visible: true },
        description: 'Whether achievements are visible to users',
        category: 'achievements',
        is_system: false
      },
      {
        key: 'leaderboard_enabled',
        value: { enabled: true },
        description: 'Whether the leaderboard feature is enabled',
        category: 'social',
        is_system: false
      }
    ];

    for (const config of configs) {
      const { error } = await supabase
        .from('gamification_config')
        .upsert(config, { onConflict: 'key' });

      if (error) {
        throw new Error(`Failed to insert config ${config.key}: ${error.message}`);
      }
    }

    console.log('✅ Default configurations created successfully');
    return true;
  } catch (error) {
    console.error('❌ Error creating default configurations:', error.message);
    return false;
  }
}

async function createDefaultAchievements() {
  console.log('\n🔧 Setting up default achievements...');

  try {
    const achievements = [
      {
        name: 'Welcome Aboard',
        description: 'Complete your profile and join the community',
        category: 'onboarding',
        icon: 'UserCheck',
        points: 100,
        difficulty: 'easy',
        is_hidden: false,
        requirements: { profile_completion: 100 }
      },
      {
        name: 'First Product',
        description: 'Create your first statistical product',
        category: 'products',
        icon: 'BarChart',
        points: 150,
        difficulty: 'easy',
        is_hidden: false,
        requirements: { products_created: 1 }
      },
      {
        name: 'Collaboration Star',
        description: 'Contribute to 5 different products',
        category: 'collaboration',
        icon: 'Users',
        points: 300,
        difficulty: 'medium',
        is_hidden: false,
        requirements: { unique_products_contributed: 5 }
      },
      {
        name: 'Data Champion',
        description: 'Have 3 products published and approved',
        category: 'products',
        icon: 'Award',
        points: 500,
        difficulty: 'hard',
        is_hidden: false,
        requirements: { products_published: 3 }
      },
      {
        name: 'Consistent Contributor',
        description: 'Log in for 7 consecutive days',
        category: 'engagement',
        icon: 'Calendar',
        points: 200,
        difficulty: 'medium',
        is_hidden: false,
        requirements: { login_streak: 7 }
      },
      {
        name: 'Survey Expert',
        description: 'Create or contribute to 3 surveys',
        category: 'surveys',
        icon: 'ClipboardList',
        points: 250,
        difficulty: 'medium',
        is_hidden: false,
        requirements: { surveys_contributed: 3 }
      },
      {
        name: 'Knowledge Sharer',
        description: 'Add comments on 10 different products',
        category: 'collaboration',
        icon: 'MessageSquare',
        points: 200,
        difficulty: 'medium',
        is_hidden: false,
        requirements: { comments_added: 10 }
      },
      {
        name: 'Approval Ace',
        description: 'Get 5 approvals without revision requests',
        category: 'workflow',
        icon: 'CheckCircle',
        points: 400,
        difficulty: 'hard',
        is_hidden: false,
        requirements: { clean_approvals: 5 }
      }
    ];

    for (const achievement of achievements) {
      const { error } = await supabase
        .from('achievements')
        .upsert(achievement, { onConflict: 'name' });

      if (error) {
        throw new Error(`Failed to insert achievement ${achievement.name}: ${error.message}`);
      }
    }

    console.log('✅ Default achievements created successfully');
    return true;
  } catch (error) {
    console.error('❌ Error creating default achievements:', error.message);
    return false;
  }
}

async function createDefaultRules() {
  console.log('\n🔧 Setting up default gamification rules...');

  try {
    // Get achievement IDs for rules that grant achievements
    const { data: achievements } = await supabase
      .from('achievements')
      .select('id, name');

    if (!achievements) {
      throw new Error('Failed to retrieve achievements for rule creation');
    }

    const achievementMap = achievements.reduce((map, achievement) => {
      map[achievement.name] = achievement.id;
      return map;
    }, {});

    const rules = [
      {
        name: 'Profile Completed',
        description: 'Award points and achievement when user completes their profile',
        event_type: 'profile_updated',
        conditions: [
          {
            field: 'completion_percentage',
            operator: '>=',
            value: 100
          }
        ],
        actions: [
          {
            type: 'add_points',
            points: 100,
            reason: 'Profile completed'
          },
          {
            type: 'grant_achievement',
            achievement_id: achievementMap['Welcome Aboard']
          }
        ],
        is_active: true,
        priority: 10
      },
      {
        name: 'First Product Created',
        description: 'Award achievement when user creates their first product',
        event_type: 'product_created',
        conditions: [
          {
            field: 'user_products_count',
            operator: '==',
            value: 1
          }
        ],
        actions: [
          {
            type: 'add_points',
            points: 150,
            reason: 'First product created'
          },
          {
            type: 'grant_achievement',
            achievement_id: achievementMap['First Product']
          }
        ],
        is_active: true,
        priority: 20
      },
      {
        name: 'Daily Login Streak',
        description: 'Track and reward daily login streaks',
        event_type: 'user_login',
        conditions: [
          {
            field: 'login_streak',
            operator: '>=',
            value: 7
          },
          {
            field: 'has_achievement',
            operator: '==',
            value: false,
            achievement_id: achievementMap['Consistent Contributor']
          }
        ],
        actions: [
          {
            type: 'add_points',
            points: 200,
            reason: '7-day login streak achieved'
          },
          {
            type: 'grant_achievement',
            achievement_id: achievementMap['Consistent Contributor']
          }
        ],
        is_active: true,
        priority: 30
      },
      {
        name: 'Comment Added',
        description: 'Award points when a user adds a comment',
        event_type: 'comment_added',
        conditions: [],
        actions: [
          {
            type: 'add_points',
            points: 5,
            reason: 'Comment added'
          }
        ],
        is_active: true,
        priority: 100
      },
      {
        name: 'Product Updated',
        description: 'Award points when a user updates a product',
        event_type: 'product_updated',
        conditions: [],
        actions: [
          {
            type: 'add_points',
            points: 10,
            reason: 'Product updated'
          }
        ],
        is_active: true,
        priority: 100
      }
    ];

    for (const rule of rules) {
      const { error } = await supabase
        .from('gamification_rules')
        .upsert(rule, { onConflict: 'name' });

      if (error) {
        throw new Error(`Failed to insert rule ${rule.name}: ${error.message}`);
      }
    }

    console.log('✅ Default rules created successfully');
    return true;
  } catch (error) {
    console.error('❌ Error creating default rules:', error.message);
    return false;
  }
}

async function setupGamificationSystem() {
  console.log('🎮 Setting up gamification system...');

  const tablesCreated = await createTables();
  if (!tablesCreated) {
    console.error('Failed to create necessary tables. Stopping setup.');
    return;
  }

  const configCreated = await createDefaultConfig();
  if (!configCreated) {
    console.warn('⚠️ Failed to create default configuration. Continuing with setup...');
  }

  const achievementsCreated = await createDefaultAchievements();
  if (!achievementsCreated) {
    console.warn('⚠️ Failed to create default achievements. Continuing with setup...');
  }

  const rulesCreated = await createDefaultRules();
  if (!rulesCreated) {
    console.warn('⚠️ Failed to create default rules. Continuing with setup...');
  }

  if (tablesCreated && configCreated && achievementsCreated && rulesCreated) {
    console.log('\n✅ Gamification system setup completed successfully!');
  } else {
    console.log('\n⚠️ Gamification system setup completed with some warnings.');
  }

  console.log('\n🔍 Next steps:');
  console.log('  1. Restart your application to activate the gamification system');
  console.log('  2. Visit the Admin panel and select the Gamification section');
  console.log('  3. Configure additional rules and achievements if needed');
}

// Execute the setup
setupGamificationSystem().catch(error => {
  console.error('❌ Fatal error during gamification system setup:', error);
  process.exit(1);
});
