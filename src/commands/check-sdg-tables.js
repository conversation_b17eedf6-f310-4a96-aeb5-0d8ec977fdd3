// Script to check SDG database tables and their structure
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Required environment variables
const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_KEY = process.env.VITE_SUPABASE_ANON_KEY;

if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('❌ Missing required environment variables');
  console.error('Please ensure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in .env.local');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

async function checkTableColumns(tableName) {
  try {
    console.log(`\n📊 Checking structure for table: ${tableName}`);
    
    // Query to get column information
    const { data, error } = await supabase.rpc('exec', { 
      query: `
        SELECT 
          column_name, 
          data_type,
          is_nullable
        FROM 
          information_schema.columns 
        WHERE 
          table_name = '${tableName}'
        ORDER BY ordinal_position
      `
    });
    
    if (error) {
      console.error(`❌ Error fetching columns for ${tableName}:`, error.message);
      return null;
    }

    // Display column information
    console.log('Column Name'.padEnd(25) + ' | ' + 'Data Type'.padEnd(15) + ' | ' + 'Nullable');
    console.log('─'.repeat(60));
    
    data.forEach(column => {
      console.log(
        column.column_name.padEnd(25) + ' | ' + 
        column.data_type.padEnd(15) + ' | ' + 
        (column.is_nullable === 'YES' ? 'Yes' : 'No')
      );
    });
    
    return data;
  } catch (error) {
    console.error(`❌ Unexpected error checking ${tableName}:`, error.message);
    return null;
  }
}

async function checkTableData(tableName, limit = 5) {
  try {
    console.log(`\n📋 Checking data for table: ${tableName} (up to ${limit} rows)`);
    
    // Count rows first
    const countQuery = await supabase
      .from(tableName)
      .select('*', { count: 'exact', head: true });
    
    if (countQuery.error) {
      console.error(`❌ Error counting rows for ${tableName}:`, countQuery.error.message);
      return null;
    }
    
    console.log(`Total rows: ${countQuery.count}`);
    
    if (countQuery.count === 0) {
      console.log('⚠️ No rows found in this table.');
      return [];
    }
    
    // Get sample data
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .limit(limit);
    
    if (error) {
      console.error(`❌ Error fetching data for ${tableName}:`, error.message);
      return null;
    }
    
    // Display the data
    console.log('\nSample data:');
    console.log(JSON.stringify(data, null, 2));
    
    return data;
  } catch (error) {
    console.error(`❌ Unexpected error fetching data from ${tableName}:`, error.message);
    return null;
  }
}

async function checkSDGTables() {
  console.log('🔍 Checking SDG Database Tables');

  // List of SDG tables to check
  const tables = [
    'sdg_goals',
    'sdg_targets',
    'sdg_indicators',
    'sdg_observations',
    'sdg_rankings'
  ];
  
  // Check each table
  for (const tableName of tables) {
    // Check table structure
    await checkTableColumns(tableName);
    
    // Check table data
    await checkTableData(tableName);
  }
}

checkSDGTables().catch(err => {
  console.error('❌ Error running the script:', err);
  process.exit(1);
});
