// <PERSON>ript to check the details of a dependency
// Usage: node src/commands/check-dependency-details.js [dependency_id]

import { createClient } from '@supabase/supabase-js';

// Define Supabase client
// Get Supabase connection info from environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_KEY;

// Ensure environment variables are set
if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Missing Supabase environment variables.');
  console.error('Make sure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in your environment.');
  console.error('Create a .env file based on .env.example or set these variables in your environment.');
  process.exit(1);
}
const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

async function checkDependencyDetails(dependencyId) {
  console.log("=== Checking Dependency Details ===");
  
  try {
    // If no dependency ID is provided, use the one from the screenshot
    if (!dependencyId) {
      dependencyId = "246d537f-e9c5-4c81-a10a-011e41d45d52"; // From the dependency in the screenshot
    }
    
    console.log(`Checking dependency with ID: ${dependencyId}`);
    
    // Get the dependency details
    const { data: dependency, error } = await supabase
      .from('product_iteration_dependencies')
      .select(`
        id,
        title,
        details,
        dependency_type,
        approval_status,
        created_at,
        product_iteration_id,
        dependent_on_product_id
      `)
      .eq('id', dependencyId)
      .maybeSingle();
      
    if (error) {
      console.error("Error fetching dependency:", error);
      return;
    }
    
    if (!dependency) {
      console.log(`No dependency found with ID: ${dependencyId}`);
      return;
    }
    
    console.log('\nDependency details:');
    console.log(`- ID: ${dependency.id}`);
    console.log(`- Title: ${dependency.title || 'No title'}`);
    console.log(`- Details: ${dependency.details || 'No details'}`);
    console.log(`- Type: ${dependency.dependency_type || 'No type'}`);
    console.log(`- Status: ${dependency.approval_status || 'No status'}`);
    console.log(`- Created: ${new Date(dependency.created_at).toLocaleString()}`);
    console.log(`- Product Iteration ID: ${dependency.product_iteration_id || 'Not set'}`);
    console.log(`- Dependent On Product ID: ${dependency.dependent_on_product_id || 'Not set'}`);
    
    // Get the product iteration details
    if (dependency.product_iteration_id) {
      console.log(`\nChecking product iteration with ID: ${dependency.product_iteration_id}`);
      
      const { data: iteration, error: iterationError } = await supabase
        .from('product_iterations')
        .select(`
          id,
          version,
          iteration,
          status,
          product_owner_id,
          product_manager_id,
          product_base_id,
          product_bases:product_base_id (id, name, name_ar)
        `)
        .eq('id', dependency.product_iteration_id)
        .maybeSingle();
        
      if (iterationError) {
        console.error("Error fetching product iteration:", iterationError);
      } else if (!iteration) {
        console.log(`No product iteration found with ID: ${dependency.product_iteration_id}`);
      } else {
        console.log('\nProduct Iteration details:');
        console.log(`- ID: ${iteration.id}`);
        console.log(`- Version: ${iteration.version || 'No version'}`);
        console.log(`- Iteration: ${iteration.iteration || 'No iteration'}`);
        console.log(`- Status: ${iteration.status || 'No status'}`);
        console.log(`- Product Owner ID: ${iteration.product_owner_id || 'Not set'}`);
        console.log(`- Product Manager ID: ${iteration.product_manager_id || 'Not set'}`);
        console.log(`- Product Base ID: ${iteration.product_base_id || 'Not set'}`);
        console.log(`- Product Base Name: ${iteration.product_bases?.name || 'Unknown'}`);
        
        // Check if the product owner is set
        if (!iteration.product_owner_id && !iteration.product_manager_id) {
          console.log('\nThis product iteration does not have a product owner or manager set.');
          console.log('This is why the dependency is not showing up in the dashboard.');
          console.log('The approval system filters out dependencies that don\'t have either a product_owner_id or product_manager_id set.');
        }
      }
    }
    
    // Get the dependent product details
    if (dependency.dependent_on_product_id) {
      console.log(`\nChecking dependent product base with ID: ${dependency.dependent_on_product_id}`);
      
      const { data: productBase, error: productBaseError } = await supabase
        .from('product_bases')
        .select(`
          id,
          name,
          name_ar,
          department,
          department_ar,
          category,
          frequency
        `)
        .eq('id', dependency.dependent_on_product_id)
        .maybeSingle();
        
      if (productBaseError) {
        console.error("Error fetching product base:", productBaseError);
      } else if (!productBase) {
        console.log(`No product base found with ID: ${dependency.dependent_on_product_id}`);
      } else {
        console.log('\nDependent Product Base details:');
        console.log(`- ID: ${productBase.id}`);
        console.log(`- Name: ${productBase.name || 'No name'}`);
        console.log(`- Name (Arabic): ${productBase.name_ar || 'No name'}`);
        console.log(`- Department: ${productBase.department || 'No department'}`);
        console.log(`- Department (Arabic): ${productBase.department_ar || 'No department'}`);
        console.log(`- Category: ${productBase.category || 'No category'}`);
        console.log(`- Frequency: ${productBase.frequency || 'No frequency'}`);
      }
    }
    
  } catch (error) {
    console.error("Error:", error);
  }
}

// Get dependency ID from command line arguments
const dependencyId = process.argv[2];

// Run the check
checkDependencyDetails(dependencyId).catch(console.error);
