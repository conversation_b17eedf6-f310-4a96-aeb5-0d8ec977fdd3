// Add Arabic names to profiles for testing

// Import Supabase client
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Create a single Supabase client for interacting with your database
const supabaseUrl = process.env.SUPABASE_URL || 'http://localhost:8083';
const supabaseKey = process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24ifQ.625_WdcF3KHqz5amU0x2X5WWHP-OEs_4qj0ssLNHzTs';
const supabase = createClient(supabaseUrl, supabaseKey);

// Test data mapping English names to Arabic names
const arabicNames = [
  { id: 'random-uuid-1', name: '<PERSON>', name_ar: 'جون سميث' },
  { id: 'random-uuid-2', name: '<PERSON>', name_ar: 'سارة ويليامز' },
  { id: 'random-uuid-3', name: '<PERSON> Hassan', name_ar: 'أحمد حسن' },
  { id: 'random-uuid-4', name: 'Fatima Khalil', name_ar: 'فاطمة خليل' },
  // Add more sample users as needed
];

async function main() {
  console.log('Updating profiles with Arabic names...');
  
  try {
    // First get existing profiles
    const { data: profiles, error: fetchError } = await supabase
      .from('profiles')
      .select('id, name')
      .limit(5);
    
    if (fetchError) {
      throw fetchError;
    }
    
    if (profiles && profiles.length > 0) {
      console.log(`Found ${profiles.length} profiles to update.`);
      
      // Add Arabic names to existing profiles
      for (let i = 0; i < profiles.length && i < arabicNames.length; i++) {
        const { error } = await supabase
          .from('profiles')
          .update({ name_ar: arabicNames[i].name_ar })
          .eq('id', profiles[i].id);
          
        if (error) {
          console.error(`Error updating profile ${profiles[i].id}:`, error);
        } else {
          console.log(`Updated profile ${profiles[i].id} (${profiles[i].name}) with Arabic name ${arabicNames[i].name_ar}`);
        }
      }
    } else {
      console.log('No profiles found to update. Creating sample profiles instead.');
      
      // Add some sample profiles with both English and Arabic names
      for (const user of arabicNames) {
        const { data, error } = await supabase
          .from('profiles')
          .insert([{ 
            id: user.id,
            name: user.name,
            name_ar: user.name_ar,
            email: `${user.name.toLowerCase().split(' ')[0]}@example.com`,
            roles: ['user'],
          }]);
          
        if (error) {
          console.error(`Error creating profile for ${user.name}:`, error);
        } else {
          console.log(`Created profile for ${user.name} with Arabic name ${user.name_ar}`);
        }
      }
    }
    
    console.log('Profile update completed!');
  } catch (error) {
    console.error('An error occurred:', error);
  }
}

main();
