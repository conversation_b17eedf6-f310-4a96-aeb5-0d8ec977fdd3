// Test debug function to diagnose issues with Edge Functions
import dotenv from 'dotenv';
import fetch from 'node-fetch';

// Load environment variables
dotenv.config({ path: '.env.local' });

const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const EDGE_FUNCTION_SECRET = process.env.VITE_EDGE_FUNCTION_SECRET || 'default-dev-secret';

async function testDebugFunction() {
  try {
    console.log('Testing Debug Edge Function...');
    console.log('URL:', `${SUPABASE_URL}/functions/v1/debug`);
    console.log('Secret:', EDGE_FUNCTION_SECRET.substring(0, 5) + '...' + EDGE_FUNCTION_SECRET.substring(EDGE_FUNCTION_SECRET.length - 5));
    
    const response = await fetch(`${SUPABASE_URL}/functions/v1/debug`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${EDGE_FUNCTION_SECRET}`
      }
    });
    
    // Print response status
    console.log('Response status:', response.status);
    
    // Parse response
    const text = await response.text();
    console.log('Raw response:', text);
    
    try {
      const data = JSON.parse(text);
      console.log('Debug function output:');
      console.log(JSON.stringify(data, null, 2));
      
      // Check if auth matches
      if (data.environment && data.environment.AUTH_MATCHES === 'yes') {
        console.log('✅ Authorization is working correctly');
      } else {
        console.log('❌ Authorization is NOT working correctly');
        console.log('Environment secret:', data.environment.SECRET_EXISTS, 'Length:', data.environment.SECRET_LENGTH);
        console.log('Auth header detected:', data.environment.AUTH_HEADER.substring(0, 20) + '...');
      }
    } catch (parseError) {
      console.error('Error parsing JSON response:', parseError.message);
    }
  } catch (error) {
    console.error('Error calling debug function:', error.message);
  }
}

// Run the test
testDebugFunction();
