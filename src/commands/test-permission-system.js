// Script to test the permission system
import { supabase } from "../lib/supabaseAuth.js";

// ANSI color codes
const GREEN = '\x1b[32m';
const RED = '\x1b[31m';
const YELLOW = '\x1b[33m';
const BLUE = '\x1b[34m';
const RESET = '\x1b[0m';

const success = (msg) => console.log(`${GREEN}✓ ${msg}${RESET}`);
const error = (msg) => console.log(`${RED}✗ ${msg}${RESET}`);
const info = (msg) => console.log(`${BLUE}→ ${msg}${RESET}`);
const warning = (msg) => console.log(`${YELLOW}! ${msg}${RESET}`);

// Test assigning roles
async function testRoleAssignment() {
  info('Testing role assignment...');
  
  try {
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError) {
      error(`Failed to get current user: ${userError.message}`);
      return false;
    }
    
    if (!user) {
      error('No authenticated user found. Please login first.');
      return false;
    }

    // Assign the viewer role to the user
    info(`Assigning 'viewer' role to user ${user.id}...`);
    const { error: assignError } = await supabase.rpc('assign_user_role', {
      p_admin_user_id: user.id, // Assuming the current user is an admin for this test
      p_target_user_id: user.id,
      p_role_name: 'viewer'
    });
    
    if (assignError) {
      error(`Failed to assign role: ${assignError.message}`);
      return false;
    }
    
    success('Successfully assigned viewer role');
    
    // Test if the user has the viewer role
    info('Checking if user has viewer role...');
    const { data: hasViewerRole, error: checkError } = await supabase.rpc('check_user_permission', {
      p_user_id: user.id,
      p_module_code: 'product_management',
      p_required_level: 'view' 
    });
    
    if (checkError) {
      error(`Failed to check role: ${checkError.message}`);
      return false;
    }
    
    if (hasViewerRole) {
      success('User has view permission for product_management');
    } else {
      error('User does not have view permission for product_management');
      return false;
    }
    
    // Test that the user does not have create permission
    info('Checking if user lacks create permission...');
    const { data: hasCreatePerm, error: checkCreateError } = await supabase.rpc('check_user_permission', {
      p_user_id: user.id,
      p_module_code: 'product_management',
      p_required_level: 'create'
    });
    
    if (checkCreateError) {
      error(`Failed to check create permission: ${checkCreateError.message}`);
      return false;
    }
    
    if (!hasCreatePerm) {
      success('User correctly lacks create permission for product_management');
    } else {
      warning('User has create permission for product_management (unexpected)');
    }
    
    return true;
  } catch (err) {
    error(`Unexpected error: ${err.message}`);
    return false;
  }
}

// Test domain-specific permissions
async function testDomainPermissions() {
  info('Testing domain-specific permissions...');
  
  try {
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      error(`Failed to get current user: ${userError?.message || 'No user found'}`);
      return false;
    }
    
    // Get domain ID for economic statistics
    info('Fetching domain ID for economic_statistics...');
    const { data: domains, error: domainsError } = await supabase
      .from('statistical_domains')
      .select('id')
      .eq('code', 'economic_statistics')
      .limit(1);
    
    if (domainsError || !domains || domains.length === 0) {
      error(`Failed to get domain ID: ${domainsError?.message || 'No domain found'}`);
      return false;
    }
    
    const economicDomainId = domains[0].id;
    
    // Assign general_director role for economic statistics domain
    info(`Assigning 'general_director' role for economic_statistics domain...`);
    const { error: assignError } = await supabase.rpc('assign_user_role', {
      p_admin_user_id: user.id,
      p_target_user_id: user.id,
      p_role_name: 'general_director',
      p_domain_code: 'economic_statistics'
    });
    
    if (assignError) {
      error(`Failed to assign domain role: ${assignError.message}`);
      return false;
    }
    
    success('Successfully assigned general_director role for economic_statistics');
    
    // Test if the user has manage permission for economic statistics
    info('Checking if user has manage permission for economic_statistics...');
    const { data: hasEconManagePerm, error: checkError } = await supabase.rpc('check_user_permission', {
      p_user_id: user.id,
      p_module_code: 'product_management',
      p_required_level: 'manage',
      p_domain_id: economicDomainId
    });
    
    if (checkError) {
      error(`Failed to check permission: ${checkError.message}`);
      return false;
    }
    
    if (hasEconManagePerm) {
      success('User has manage permission for product_management in economic_statistics domain');
    } else {
      error('User does not have manage permission for product_management in economic_statistics domain');
    }
    
    // Test that the user still only has view permission for other domains
    info('Checking if user has limited permissions in other domains...');
    
    // Get domain ID for social statistics
    const { data: socialDomains, error: socialDomainsError } = await supabase
      .from('statistical_domains')
      .select('id')
      .eq('code', 'social_statistics')
      .limit(1);
    
    if (socialDomainsError || !socialDomains || socialDomains.length === 0) {
      error(`Failed to get social domain ID: ${socialDomainsError?.message || 'No domain found'}`);
      return false;
    }
    
    const socialDomainId = socialDomains[0].id;
    
    // Check if user has only view permission in social statistics domain
    const { data: hasSocialManagePerm, error: checkSocialError } = await supabase.rpc('check_user_permission', {
      p_user_id: user.id,
      p_module_code: 'product_management',
      p_required_level: 'manage',
      p_domain_id: socialDomainId
    });
    
    if (checkSocialError) {
      error(`Failed to check social permission: ${checkSocialError.message}`);
      return false;
    }
    
    if (!hasSocialManagePerm) {
      success('User correctly has limited permissions in social_statistics domain');
    } else {
      warning('User has unexpected permissions in social_statistics domain');
    }
    
    return true;
  } catch (err) {
    error(`Unexpected error: ${err.message}`);
    return false;
  }
}

// Main function
async function main() {
  console.log('\n=== Permission System Test ===\n');
  
  try {
    // Check if the permission tables exist
    info('Checking if permission system tables exist...');
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .in('table_name', ['system_roles', 'statistical_domains', 'permission_levels', 'user_roles'])
      .eq('table_schema', 'public');
    
    if (tablesError) {
      error(`Database error: ${tablesError.message}`);
      return;
    }
    
    if (!tables || tables.length < 4) {
      error('Permission system tables are missing. Please run the migration script first.');
      console.log('Tables found:', tables?.map(t => t.table_name).join(', ') || 'none');
      return;
    }
    
    success('Permission system tables exist');
    
    // Run the tests
    const roleResult = await testRoleAssignment();
    const domainResult = await testDomainPermissions();
    
    console.log('\n=== Test Summary ===\n');
    
    if (roleResult) {
      success('Role assignment test passed');
    } else {
      error('Role assignment test failed');
    }
    
    if (domainResult) {
      success('Domain permissions test passed');
    } else {
      error('Domain permissions test failed');
    }
    
    if (roleResult && domainResult) {
      console.log(`\n${GREEN}All tests passed! The permission system is working correctly.${RESET}\n`);
    } else {
      console.log(`\n${RED}Some tests failed. Please check the error messages above.${RESET}\n`);
    }
    
  } catch (err) {
    error(`Unexpected error: ${err.message}`);
  }
}

main();
