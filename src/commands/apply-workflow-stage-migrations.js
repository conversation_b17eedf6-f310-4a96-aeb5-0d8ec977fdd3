const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('Applying workflow stage migrations...');

try {
  const migrationsDir = path.join(__dirname, '../../supabase/migrations');
  
  // First apply the kanban enhancements migration that creates the workflow_stages table
  console.log('Applying migration: 20250317_kanban_enhancements.sql');
  const kanbanMigrationPath = path.join(migrationsDir, '20250317_kanban_enhancements.sql');
  
  if (fs.existsSync(kanbanMigrationPath)) {
    const psqlCommand = `psql "${process.env.DATABASE_URL}" -f "${kanbanMigrationPath}"`;
    console.log('Running command:', psqlCommand);
    execSync(psqlCommand, { stdio: 'inherit' });
    console.log('Successfully applied kanban enhancements migration');
  } else {
    console.error('Error: Kanban migration file not found at:', kanbanMigrationPath);
    process.exit(1);
  }
  
  // Then apply the fix for workflow tasks relationship
  console.log('Applying migration: 20250318_fix_workflow_tasks_relationship.sql');
  const fixRelationshipPath = path.join(migrationsDir, '20250318_fix_workflow_tasks_relationship.sql');
  
  if (fs.existsSync(fixRelationshipPath)) {
    const psqlCommand = `psql "${process.env.DATABASE_URL}" -f "${fixRelationshipPath}"`;
    console.log('Running command:', psqlCommand);
    execSync(psqlCommand, { stdio: 'inherit' });
    console.log('Successfully applied workflow tasks relationship fix');
  } else {
    console.error('Error: Relationship fix migration file not found at:', fixRelationshipPath);
    process.exit(1);
  }
  
  // Reload the PostgREST schema cache
  console.log('Forcing PostgREST schema cache refresh...');
  const reloadSchemaCmd = `psql "${process.env.DATABASE_URL}" -c "NOTIFY pgrst, 'reload schema';"`;
  execSync(reloadSchemaCmd, { stdio: 'inherit' });
  
  console.log('\n✅ All migrations have been successfully applied!');
  console.log('You should now be able to create and manage workflow stages.');
  
} catch (error) {
  console.error('Error applying migrations:', error.message);
  console.error('\nTroubleshooting tips:');
  console.error('1. Make sure your DATABASE_URL environment variable is set correctly');
  console.error('2. Ensure you have psql installed and available in your PATH');
  console.error('3. Check that you have permissions to access the database');
  console.error('\nAlternative approach:');
  console.error('You can also try using the Supabase CLI to apply migrations:');
  console.error('cd supabase/ && supabase db reset');
  process.exit(1);
}
