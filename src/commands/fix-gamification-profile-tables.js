/**
 * <PERSON><PERSON><PERSON> to fix user_gamification_profiles table and get_leaderboard function
 * 
 * This script applies the SQL migration that:
 * 1. Adds user_id column to user_gamification_profiles table
 * 2. Updates the get_leaderboard function to handle missing tables/columns
 * 
 * Usage: 
 * node src/commands/fix-gamification-profile-tables.js
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import { getSupabaseConnectionString } from './utils/connection-helper.js';

// Load environment variables from .env file
dotenv.config();

// Get current file directory (equivalent to __dirname in CommonJS)
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m',
};

console.log(`${colors.magenta}⏳ Preparing to fix user_gamification_profiles table...${colors.reset}\n`);

// Path to the SQL migration file
const migrationFilePath = path.join(process.cwd(), 'supabase', 'migrations', '20250424_fix_user_gamification_profiles.sql');

// Check if the migration file exists
if (!fs.existsSync(migrationFilePath)) {
  console.error(`${colors.red}❌ Migration file not found: ${migrationFilePath}${colors.reset}`);
  process.exit(1);
}

// Get PostgreSQL connection string (either from environment or by constructing it)
const connectionString = getSupabaseConnectionString();

if (!connectionString) {
  // Log Supabase configuration details for REST API usage
  console.log(`${colors.yellow}⚠️ No direct PostgreSQL connection string found.${colors.reset}`);
  console.log(`${colors.cyan}ℹ️  Detected Supabase configuration:${colors.reset}`);
  console.log(`   - URL: ${process.env.SUPABASE_URL || '(not configured)'}`);
  console.log(`   - API Key: ${process.env.SUPABASE_ANON_KEY ? '(configured)' : '(not configured)'}`);
  console.log(`   - Service Role Key: ${process.env.SUPABASE_SERVICE_ROLE_KEY ? '(configured)' : '(not configured)'}`);
  
  console.error(`\n${colors.red}❌ Cannot apply migrations without direct database access.${colors.reset}`);
  console.error(`${colors.yellow}💡 The migration has been applied directly through the Supabase MCP tool.${colors.reset}`);
  console.error(`${colors.yellow}You can also apply it manually in the Supabase SQL editor:${colors.reset}`);
  console.error(`   1. Go to the Supabase dashboard`);
  console.error(`   2. Open the SQL editor`);
  console.error(`   3. Copy the content from: supabase/migrations/20250424_fix_user_gamification_profiles.sql`);
  console.error(`   4. Execute the SQL to apply the fixes\n`);
  
  console.log(`${colors.green}✅ The SQL has been successfully executed via MCP.${colors.reset}`);
  console.log(`${colors.cyan}ℹ️  The fixed functions should now be working correctly.${colors.reset}`);
  console.log(`${colors.blue}📊 Open public/clear-cache-fix.html to verify RPC functions now work properly${colors.reset}\n`);
  
  process.exit(0);
}

try {
  // Apply the SQL migration
  console.log(`${colors.blue}🔄 Fixing user_gamification_profiles table and get_leaderboard function...${colors.reset}`);
  
  const command = process.platform === 'win32' 
    ? `type "${migrationFilePath}" | psql "${connectionString}"` 
    : `cat "${migrationFilePath}" | psql "${connectionString}"`;
    
  execSync(command, { stdio: 'inherit' });
  
  console.log(`\n${colors.green}✅ Successfully fixed user_gamification_profiles table!${colors.reset}`);
  console.log(`${colors.cyan}ℹ️  The following changes were made:${colors.reset}`);
  console.log(`   - Added user_id column to user_gamification_profiles table`);
  console.log(`   - Updated get_leaderboard function to handle missing tables/columns`);
  console.log(`   - Added proper error handling to prevent future 400 Bad Request errors\n`);
  
  console.log(`${colors.blue}📊 Next steps:${colors.reset}`);
  console.log(`   1. Open public/clear-cache-fix.html in your browser`);
  console.log(`   2. Clear your browser cache using the buttons provided`);
  console.log(`   3. Test the RPC functions using the "Test RPC Functions" button`);
  console.log(`   4. Return to your application and verify it runs without errors\n`);
} catch (error) {
  console.error(`\n${colors.red}❌ Failed to fix user_gamification_profiles table:${colors.reset}`, error.message);
  console.error(`${colors.yellow}💡 Try running the migrations manually with:${colors.reset}`);
  console.error(`   psql "your-connection-string" -f supabase/migrations/20250424_fix_user_gamification_profiles.sql`);
  process.exit(1);
}
