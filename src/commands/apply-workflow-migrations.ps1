# PowerShell script to apply workflow stage migrations

Write-Host "Applying workflow stage migrations..." -ForegroundColor Cyan

# Define the SQL migrations to run
$kanbanMigrationPath = Join-Path -Path $PSScriptRoot -ChildPath "..\..\supabase\migrations\20250317_kanban_enhancements.sql"
$fixRelationshipPath = Join-Path -Path $PSScriptRoot -ChildPath "..\..\supabase\migrations\20250318_fix_workflow_tasks_relationship.sql"

# Check if files exist
if (-not (Test-Path $kanbanMigrationPath)) {
    Write-Host "Error: Kanban migration file not found at: $kanbanMigrationPath" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path $fixRelationshipPath)) {
    Write-Host "Error: Relationship fix migration file not found at: $fixRelationshipPath" -ForegroundColor Red
    exit 1
}

# Check if Supabase CLI is installed
try {
    $supabasePath = Get-Command supabase -ErrorAction Stop
    Write-Host "Using Supabase CLI at: $($supabasePath.Source)" -ForegroundColor Green
    
    # Change to supabase directory
    $supabaseDir = Join-Path -Path $PSScriptRoot -ChildPath "..\..\supabase"
    Set-Location -Path $supabaseDir
    
    # Apply migrations using Supabase CLI
    Write-Host "Running supabase db reset..." -ForegroundColor Yellow
    supabase db reset
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Error: Failed to apply migrations with 'supabase db reset'" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "`n✅ Migrations applied successfully!" -ForegroundColor Green
    Write-Host "You should now be able to create and manage workflow stages."
    Write-Host "Please restart your application if it's currently running."
    
    exit 0
}
catch {
    Write-Host "Supabase CLI not found. Attempting to use psql directly..." -ForegroundColor Yellow
    
    # Check if psql is installed
    try {
        $psqlPath = Get-Command psql -ErrorAction Stop
        Write-Host "Using psql at: $($psqlPath.Source)" -ForegroundColor Green
        
        # Check if DATABASE_URL is set
        if (-not $env:DATABASE_URL) {
            Write-Host "Error: DATABASE_URL environment variable is not set" -ForegroundColor Red
            Write-Host "Please set the DATABASE_URL environment variable to your Supabase database URL"
            exit 1
        }
        
        # Apply the migrations directly using psql
        Write-Host "Applying migration: 20250317_kanban_enhancements.sql" -ForegroundColor Yellow
        & psql $env:DATABASE_URL -f $kanbanMigrationPath
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Error: Failed to apply kanban enhancements migration" -ForegroundColor Red
            exit 1
        }
        
        Write-Host "Applying migration: 20250318_fix_workflow_tasks_relationship.sql" -ForegroundColor Yellow
        & psql $env:DATABASE_URL -f $fixRelationshipPath
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Error: Failed to apply workflow tasks relationship fix" -ForegroundColor Red
            exit 1
        }
        
        # Force PostgREST schema refresh
        Write-Host "Forcing PostgREST schema cache refresh..." -ForegroundColor Yellow
        & psql $env:DATABASE_URL -c "NOTIFY pgrst, 'reload schema';"
        
        Write-Host "`n✅ All migrations have been successfully applied!" -ForegroundColor Green
        Write-Host "You should now be able to create and manage workflow stages."
        Write-Host "Please restart your application if it's currently running."
        
        exit 0
    }
    catch {
        Write-Host "Error: Neither Supabase CLI nor psql is installed" -ForegroundColor Red
        Write-Host "Please install either:"
        Write-Host "  - Supabase CLI: https://supabase.com/docs/guides/cli"
        Write-Host "  - PostgreSQL client (psql): https://www.postgresql.org/download/"
        exit 1
    }
}
