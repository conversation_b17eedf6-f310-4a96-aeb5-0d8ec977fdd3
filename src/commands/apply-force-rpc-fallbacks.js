/**
 * <PERSON><PERSON><PERSON> to apply Force RPC Fallbacks methodology for handling 404 errors
 * 
 * This script modifies the gateway to use client-side fallbacks when server-side
 * RPC functions are unavailable (404 Not Found errors).
 * 
 * Usage: 
 * node src/commands/apply-force-rpc-fallbacks.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get current file directory (equivalent to __dirname in CommonJS)
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m',
};

console.log(`${colors.magenta}🛠  Preparing to modify Supabase gateway for RPC fallbacks...${colors.reset}\n`);

// Path to the gateway file
const gatewayPath = path.join(process.cwd(), 'src', 'integrations', 'supabase', 'gateway.ts');

// Check if the gateway file exists
if (!fs.existsSync(gatewayPath)) {
  console.error(`${colors.red}❌ Gateway file not found: ${gatewayPath}${colors.reset}`);
  console.error(`${colors.yellow}Please make sure you're running this script from the project root directory.${colors.reset}`);
  process.exit(1);
}

try {
  // Read the gateway file
  let gatewayContent = fs.readFileSync(gatewayPath, 'utf8');
  
  // Check if the patch has already been applied
  if (gatewayContent.includes('// RPC FALLBACKS PATCH')) {
    console.log(`${colors.yellow}⚠️  RPC fallbacks patch already applied to gateway.ts${colors.reset}`);
    console.log(`${colors.cyan}ℹ️  You can manually remove the patch by editing ${gatewayPath}${colors.reset}`);
    console.log(`${colors.cyan}   and removing the section marked with "RPC FALLBACKS PATCH" comments.${colors.reset}`);
    process.exit(0);
  }
  
  console.log(`${colors.blue}🔄 Modifying gateway.ts to support RPC fallbacks...${colors.reset}`);
  
  // Find the position to insert the patch - after imports but before functions
  const importEndPosition = gatewayContent.lastIndexOf('import') + 1;
  let importEndLine = gatewayContent.indexOf('\n', importEndPosition) + 1;
  
  // Find the first empty line after imports
  while (importEndLine < gatewayContent.length && 
         gatewayContent.charAt(importEndLine) !== '\n' &&
         !gatewayContent.substring(importEndLine, importEndLine + 2).includes('\n\n')) {
    importEndLine = gatewayContent.indexOf('\n', importEndLine) + 1;
  }
  
  // Make sure we found a reasonable position
  if (importEndLine <= 0 || importEndLine >= gatewayContent.length / 2) {
    // Fallback to a simpler approach - just add after the last import
    importEndLine = gatewayContent.lastIndexOf('import');
    importEndLine = gatewayContent.indexOf('\n', importEndLine) + 1;
  }
  
  // Create the patch code
  const rpcFallbacksPatch = `
// RPC FALLBACKS PATCH - Added by apply-force-rpc-fallbacks.js
// This section enables automatic client-side fallbacks when RPC functions return 404 errors

import {
  getLeaderboardFallback,
  getRolePermissionsMatrixFallback,
  getRolePermissionsMatrixAltFallback,
  getDynamicUserCapabilitiesFallback
} from '../../services/rpc-fallbacks';

// Flag to check if we should force using fallbacks
const shouldForceFallbacks = typeof localStorage !== "undefined" && 
  localStorage.getItem('force_rpc_fallbacks') === 'true';

if (shouldForceFallbacks && typeof window !== "undefined") {
  console.log('%cRPC FALLBACKS ENABLED: Will use client-side implementations', 
    'color: white; background: green; padding: 3px; border-radius: 3px;');
    
  // Add the global flag for other parts of the app to check
  // @ts-ignore
  window.__FORCE_RPC_FALLBACKS = true;
}

/**
 * Helper to wrap Supabase RPC calls with fallback support
 * @param client The Supabase client
 * @returns A wrapped client with fallback support
 */
function wrapRpcWithFallbacks(client) {
  const originalRpc = client.rpc.bind(client);
  
  client.rpc = function(fn, ...args) {
    // Check if we should force fallbacks
    if (shouldForceFallbacks) {
      console.log(\`Using client-side fallback for RPC function: \${fn}\`);
      
      // Use appropriate fallback based on function name
      if (fn === 'get_leaderboard') {
        // For leaderboard, check if we have parameters
        const params = args[0] || {};
        const limit = params.p_limit || 10;
        const offset = params.p_offset || 0;
        
        return {
          then: (callback) => {
            getLeaderboardFallback(limit, offset).then(data => {
              callback({ data, error: null });
            });
            return { catch: () => {} };
          }
        };
      }
      else if (fn === 'get_role_permissions_matrix') {
        return {
          then: (callback) => {
            getRolePermissionsMatrixFallback().then(data => {
              callback({ data, error: null });
            });
            return { catch: () => {} };
          }
        };
      }
      else if (fn === 'get_role_permissions_matrix_alt') {
        return {
          then: (callback) => {
            getRolePermissionsMatrixAltFallback().then(data => {
              callback({ data, error: null });
            });
            return { catch: () => {} };
          }
        };
      }
      else if (fn === 'get_dynamic_user_capabilities') {
        // For user capabilities, check if we have a user ID
        const params = args[0] || {};
        const userId = params.p_user_id;
        
        return {
          then: (callback) => {
            getDynamicUserCapabilitiesFallback(userId).then(data => {
              callback({ data, error: null });
            });
            return { catch: () => {} };
          }
        };
      }
    }
    
    // For all other cases, use the original RPC function
    // but add fallback handling for 404 errors
    const rpcResult = originalRpc(fn, ...args);
    
    // Add fallback handling
    const originalThen = rpcResult.then.bind(rpcResult);
    
    rpcResult.then = function(callback) {
      return originalThen(async (result) => {
        // Check if we got a 404 error and have a fallback
        if (result && result.error && result.status === 404) {
          console.warn(\`RPC function \${fn} returned 404, using fallback\`);
          
          // Use appropriate fallback based on function name
          if (fn === 'get_leaderboard') {
            const params = args[0] || {};
            const limit = params.p_limit || 10;
            const offset = params.p_offset || 0;
            
            const fallbackData = await getLeaderboardFallback(limit, offset);
            return callback({ data: fallbackData, error: null });
          }
          else if (fn === 'get_role_permissions_matrix') {
            const fallbackData = await getRolePermissionsMatrixFallback();
            return callback({ data: fallbackData, error: null });
          }
          else if (fn === 'get_role_permissions_matrix_alt') {
            const fallbackData = await getRolePermissionsMatrixAltFallback();
            return callback({ data: fallbackData, error: null });
          }
          else if (fn === 'get_dynamic_user_capabilities') {
            const params = args[0] || {};
            const userId = params.p_user_id;
            
            const fallbackData = await getDynamicUserCapabilitiesFallback(userId);
            return callback({ data: fallbackData, error: null });
          }
        }
        
        // If no fallback was applied, just return the original result
        return callback(result);
      });
    };
    
    return rpcResult;
  };
  
  return client;
}
// END RPC FALLBACKS PATCH
`;
  
  // Insert the patch
  gatewayContent = gatewayContent.slice(0, importEndLine) + rpcFallbacksPatch + gatewayContent.slice(importEndLine);
  
  // Find the position to add the wrapper in getSupabaseClient function
  const getClientFunctionPos = gatewayContent.indexOf('export function getSupabaseClient()');
  if (getClientFunctionPos === -1) {
    throw new Error('Could not find getSupabaseClient() function in gateway.ts');
  }
  
  // Find where the client is created and returned
  const clientCreationPos = gatewayContent.indexOf('let client = createClient', getClientFunctionPos);
  if (clientCreationPos === -1) {
    throw new Error('Could not find client creation in getSupabaseClient() function');
  }
  
  // Find where globalClient is assigned - this is where we'll add our wrapper
  const globalClientAssignmentPos = gatewayContent.indexOf('globalClient = ', clientCreationPos);
  if (globalClientAssignmentPos === -1) {
    throw new Error('Could not find globalClient assignment in getSupabaseClient() function');
  }
  
  // Find the end of the statement
  let statementEndPos = gatewayContent.indexOf(';', globalClientAssignmentPos);
  if (statementEndPos === -1) {
    // If no semicolon, look for line end
    statementEndPos = gatewayContent.indexOf('\n', globalClientAssignmentPos);
  }
  
  // Extract the line
  const assignmentLine = gatewayContent.substring(globalClientAssignmentPos, statementEndPos);
  
  // Create the replacement with the wrapper
  let replacementLine;
  if (assignmentLine.includes('instrumentSupabaseClient')) {
    // If already using instrumentSupabaseClient, wrap it
    replacementLine = assignmentLine.replace(
      'globalClient = instrumentSupabaseClient(client)',
      'globalClient = wrapRpcWithFallbacks(instrumentSupabaseClient(client))'
    );
  } else {
    // Otherwise just assign with the wrapper
    replacementLine = assignmentLine.replace(
      'globalClient = client',
      'globalClient = wrapRpcWithFallbacks(client)'
    );
  }
  
  // Replace the line
  gatewayContent = gatewayContent.substring(0, globalClientAssignmentPos) + 
                  replacementLine + 
                  gatewayContent.substring(statementEndPos);
  
  // Write the updated file
  fs.writeFileSync(gatewayPath, gatewayContent);
  
  console.log(`${colors.green}✅ Successfully modified gateway.ts to support RPC fallbacks!${colors.reset}`);
  console.log(`${colors.cyan}ℹ️  The following changes were made:${colors.reset}`);
  console.log(`   - Added RPC fallbacks patch to gateway.ts`);
  console.log(`   - Added detection of 'force_rpc_fallbacks' localStorage flag`);
  console.log(`   - Added automatic 404 error recovery using client-side fallbacks\n`);
  
  console.log(`${colors.blue}📊 Next steps:${colors.reset}`);
  console.log(`   1. Rebuild your application`);
  console.log(`   2. Load the application in your browser`);
  console.log(`   3. If you want to force using fallbacks, open /force-rpc-fallbacks.html\n`);
  
  // Create a message about the HTML tool
  console.log(`${colors.magenta}🔍 To learn more about RPC fallbacks:${colors.reset}`);
  console.log(`   - Read the docs/FORCE-FALLBACKS-GUIDE.md documentation`);
  console.log(`   - Use the public/force-rpc-fallbacks.html tool for easy toggling\n`);
  
} catch (error) {
  console.error(`${colors.red}❌ Failed to apply RPC fallbacks patch:${colors.reset}`, error);
  console.error(`${colors.yellow}💡 You can still use the manual approach:${colors.reset}`);
  console.error(`   1. Open public/force-rpc-fallbacks.html in your browser`);
  console.error(`   2. Click "Force RPC Fallbacks" button`);
  console.error(`   3. Reload your application`);
  process.exit(1);
}
