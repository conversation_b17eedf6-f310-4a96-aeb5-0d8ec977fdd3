// <PERSON>ript to fix all dependencies without requiring a specific user ID
// Usage: node src/commands/fix-all-dependencies.js

import { createClient } from '@supabase/supabase-js';

// Define Supabase client directly to avoid TypeScript import issues
// Get Supabase connection info from environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_KEY;

// Ensure environment variables are set
if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Missing Supabase environment variables.');
  console.error('Make sure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in your environment.');
  console.error('Create a .env file based on .env.example or set these variables in your environment.');
  process.exit(1);
}
const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

const fixAllDependencies = async () => {
  console.log(`=== Dependency Fix Tool (Auto Mode) ===`);
  
  try {
    // Step 1: Find all pending dependencies
    console.log("\nStep 1: Finding all pending dependencies...");
    const { data: pendingDependencies, error: dependenciesError } = await supabase
      .from('product_iteration_dependencies')
      .select('id, product_iteration_id, title, dependency_type, approval_status')
      .eq('approval_status', 'pending');
      
    if (dependenciesError) {
      console.error('Error fetching pending dependencies:', dependenciesError);
      return;
    }
    
    console.log(`Found ${pendingDependencies.length} pending dependencies`);
    
    if (pendingDependencies.length === 0) {
      console.log('No pending dependencies found to update.');
      return;
    }
    
    // Log the pending dependencies for reference
    console.log('\nPending dependencies:');
    pendingDependencies.forEach(dep => {
      console.log(`- ID: ${dep.id}, Title: ${dep.title || dep.dependency_type}, Product Iteration: ${dep.product_iteration_id}`);
    });
    
    // Step 2: Get unique product iteration IDs
    const productIterationIds = [...new Set(pendingDependencies
      .filter(dep => dep.product_iteration_id) // Filter out any null product_iteration_id
      .map(dep => dep.product_iteration_id))];
    
    console.log(`\nFound ${productIterationIds.length} unique product iterations to update`);
    
    if (productIterationIds.length === 0) {
      console.log('No valid product iterations found to update.');
      return;
    }
    
    // Step 3: Find an active user to use as the product owner
    console.log("\nStep 2: Finding an active user to use as product owner...");
    const { data: activeUsers, error: usersError } = await supabase
      .from('profiles')
      .select('id, name, email')
      .limit(5);
      
    if (usersError) {
      console.error('Error fetching active users:', usersError);
      return;
    }
    
    let selectedUser;
    
    if (!activeUsers || activeUsers.length === 0) {
      console.log('No active users found in the database. Using the current user ID from the dependency...');
      
      // Get the product iteration details to find the creator
      const { data: iterationData, error: iterationError } = await supabase
        .from('product_iterations')
        .select('id, created_by')
        .in('id', productIterationIds)
        .limit(1);
        
      if (iterationError || !iterationData || iterationData.length === 0) {
        console.log('Could not find iteration creator. Using a hardcoded user ID...');
        
        // Use a hardcoded user ID as a last resort
        selectedUser = {
          id: '40031c8e-859d-4240-9147-d29241a76bb0', // This is the user ID from the error message
          name: 'Temporary User',
          email: '<EMAIL>'
        };
      } else {
        const createdBy = iterationData[0].created_by;
        
        if (createdBy) {
          // Try to get the user profile
          const { data: userProfile, error: profileError } = await supabase
            .from('profiles')
            .select('id, name, email')
            .eq('id', createdBy)
            .single();
            
          if (!profileError && userProfile) {
            selectedUser = userProfile;
          } else {
            // Use the ID but with placeholder info
            selectedUser = {
              id: createdBy,
              name: 'Product Creator',
              email: '<EMAIL>'
            };
          }
        } else {
          // Use a hardcoded user ID as a last resort
          selectedUser = {
            id: '40031c8e-859d-4240-9147-d29241a76bb0', // This is the user ID from the error message
            name: 'Temporary User',
            email: '<EMAIL>'
          };
        }
      }
    } else {
      selectedUser = activeUsers[0];
    }
    
    console.log(`Selected user: ${selectedUser.name} (${selectedUser.email}) with ID: ${selectedUser.id}`);
    
    // Step 4: Check current state of product iterations
    console.log("\nStep 3: Checking current state of product iterations...");
    const { data: currentIterations, error: checkError } = await supabase
      .from('product_iterations')
      .select('id, product_owner_id, product_manager_id')
      .in('id', productIterationIds);
      
    if (checkError) {
      console.error('Error checking product iterations:', checkError);
      return;
    }
    
    console.log('Current state of product iterations:');
    currentIterations.forEach(iter => {
      console.log(`- ID: ${iter.id}, Owner: ${iter.product_owner_id || 'Not set'}, Manager: ${iter.product_manager_id || 'Not set'}`);
    });
    
    // Step 5: Update product iterations to set the selected user as owner
    console.log("\nStep 4: Updating product iterations...");
    const { data: updateResult, error: updateError } = await supabase
      .from('product_iterations')
      .update({ product_owner_id: selectedUser.id })
      .in('id', productIterationIds)
      .select('id, product_owner_id, product_manager_id');
      
    if (updateError) {
      console.error('Error updating product iterations:', updateError);
      return;
    }
    
    console.log(`\nSuccessfully updated ${updateResult.length} product iterations`);
    console.log('Updated product iterations:');
    updateResult.forEach(iter => {
      console.log(`- ID: ${iter.id}, Owner: ${iter.product_owner_id || 'Not set'}, Manager: ${iter.product_manager_id || 'Not set'}`);
    });
    
    console.log('\n=== Fix Complete ===');
    console.log('Your pending dependencies should now appear in the approvals dashboard.');
    console.log('Please refresh the approvals dashboard to see the changes.');
  } catch (error) {
    console.error('Error fixing dependencies:', error);
  }
};

fixAllDependencies().catch(console.error);
