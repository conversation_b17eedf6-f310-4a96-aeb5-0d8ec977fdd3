// <PERSON>ript to create SDG tables in Supabase using direct SQL
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('❌ Missing required environment variables.');
  console.error('Please ensure VITE_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in .env.local');
  process.exit(1);
}

// Initialize Supabase client with service role key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

// Function to execute each SQL statement separately
async function executeSql(sqlStatements) {
  const statements = sqlStatements
    .split(';')
    .map(stmt => stmt.trim())
    .filter(stmt => stmt.length > 0);
  
  for (const stmt of statements) {
    try {
      const { data, error } = await supabase.from('dummy_table_for_sql').select('*').eq('id', 0).options({
        head: true,
        count: 'exact',
        default_headers: { 'Prefer': 'params=single-object' },
        headers: { 'X-Raw-SQL': stmt }
      });
      
      if (error) {
        console.log(`❌ SQL statement failed: ${stmt.substring(0, 50)}...`);
        console.error(`Error: ${error.message}`);
        return false;
      }
    } catch (err) {
      console.error(`❌ Exception: ${err.message}`);
      return false;
    }
  }
  
  return true;
}

// Create individual tables manually
async function createTables() {
  try {
    console.log('Creating SDG Goals table...');
    let { error } = await supabase
      .from('sdg_goals')
      .insert({ code: 'test', title: 'Test', description: 'Just testing table creation' })
      .select();
    
    if (error && error.code === '42P01') {
      // Table doesn't exist, create it
      const { error: createError } = await supabase.rpc('create_sdg_goals_table');
      if (createError) {
        console.error('Error creating sdg_goals table:', createError.message);
        return false;
      }
    } else if (error) {
      console.error('Unexpected error testing sdg_goals:', error.message);
      return false;
    }

    // Delete the test record
    await supabase.from('sdg_goals').delete().eq('code', 'test');
    
    console.log('Creating remaining SDG tables...');
    const createTablesSuccess = await supabase.rpc('create_sdg_tables');
    if (createTablesSuccess.error) {
      console.error('Error creating remaining SDG tables:', createTablesSuccess.error.message);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error creating tables:', error.message);
    return false;
  }
}

// Create RPC functions to create tables
async function createRpcFunctions() {
  // Create an RPC function to create the goals table
  const createGoalsTableSql = `
  CREATE OR REPLACE FUNCTION create_sdg_goals_table()
  RETURNS boolean
  LANGUAGE plpgsql
  SECURITY DEFINER
  AS $$
  BEGIN
    -- SDG Goals table
    CREATE TABLE IF NOT EXISTS sdg_goals (
      id SERIAL PRIMARY KEY,
      code VARCHAR(10) NOT NULL UNIQUE,
      title VARCHAR(255) NOT NULL,
      description TEXT,
      color VARCHAR(20),
      icon VARCHAR(255),
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    RETURN TRUE;
  END;
  $$;
  `;
  
  // Create an RPC function to create the remaining tables
  const createTablesRpcSql = `
  CREATE OR REPLACE FUNCTION create_sdg_tables()
  RETURNS boolean
  LANGUAGE plpgsql
  SECURITY DEFINER
  AS $$
  BEGIN
    -- SDG Targets table
    CREATE TABLE IF NOT EXISTS sdg_targets (
      id SERIAL PRIMARY KEY,
      code VARCHAR(20) NOT NULL UNIQUE,
      description TEXT NOT NULL,
      goal_code VARCHAR(10) NOT NULL REFERENCES sdg_goals(code),
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    -- SDG Indicators table
    CREATE TABLE IF NOT EXISTS sdg_indicators (
      id SERIAL PRIMARY KEY,
      code VARCHAR(30) NOT NULL UNIQUE,
      title TEXT NOT NULL,
      description TEXT,
      goal_code VARCHAR(10) NOT NULL REFERENCES sdg_goals(code),
      target_code VARCHAR(20) NOT NULL REFERENCES sdg_targets(code),
      series_code VARCHAR(50),
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    -- SDG Observations table
    CREATE TABLE IF NOT EXISTS sdg_observations (
      id SERIAL PRIMARY KEY,
      indicator_code VARCHAR(30) NOT NULL REFERENCES sdg_indicators(code),
      geo_code VARCHAR(10) NOT NULL, 
      year INTEGER NOT NULL,
      value NUMERIC,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      UNIQUE(indicator_code, geo_code, year)
    );

    -- SDG Rankings table
    CREATE TABLE IF NOT EXISTS sdg_rankings (
      id SERIAL PRIMARY KEY,
      indicator_code VARCHAR(30) NOT NULL REFERENCES sdg_indicators(code),
      year INTEGER NOT NULL,
      saudi_rank INTEGER,
      total_countries INTEGER,
      percentile NUMERIC,
      best_country VARCHAR(10),
      best_value NUMERIC,
      worst_country VARCHAR(10),
      worst_value NUMERIC,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      UNIQUE(indicator_code, year)
    );
    
    RETURN TRUE;
  END;
  $$;
  `;
  
  // Create a function to insert sample data
  const insertSampleDataRpcSql = `
  CREATE OR REPLACE FUNCTION insert_sdg_sample_data()
  RETURNS boolean
  LANGUAGE plpgsql
  SECURITY DEFINER
  AS $$
  BEGIN
    -- Insert Goal 3
    INSERT INTO sdg_goals (code, title, description, color)
    VALUES ('3', 'Good Health and Well-being', 'Ensure healthy lives and promote well-being for all at all ages', '#4C9F38')
    ON CONFLICT (code) DO NOTHING;

    -- Insert targets for Goal 3
    INSERT INTO sdg_targets (code, description, goal_code)
    VALUES
      ('3.1', 'By 2030, reduce the global maternal mortality ratio to less than 70 per 100,000 live births', '3'),
      ('3.2', 'By 2030, end preventable deaths of newborns and children under 5 years of age', '3'),
      ('3.3', 'By 2030, end the epidemics of AIDS, tuberculosis, malaria and neglected tropical diseases and combat hepatitis', '3')
    ON CONFLICT (code) DO NOTHING;

    -- Insert indicators for Goal 3
    INSERT INTO sdg_indicators (code, title, goal_code, target_code, series_code)
    VALUES
      ('3.1.1', 'Maternal mortality ratio', '3', '3.1', 'SH_MMR_RISK'),
      ('3.1.2', 'Proportion of births attended by skilled health personnel', '3', '3.1', 'SH_STA_BRTATT'),
      ('3.2.1', 'Under-five mortality rate', '3', '3.2', 'SH_DYN_MORT'),
      ('3.3.1', 'Number of new HIV infections per 1,000 uninfected', '3', '3.3', 'SH_HIV_INCD')
    ON CONFLICT (code) DO NOTHING;

    -- Insert sample observations (with realistic values)
    INSERT INTO sdg_observations (indicator_code, geo_code, year, value)
    VALUES
      -- Saudi Arabia maternal mortality ratio (per 100,000 live births)
      ('3.1.1', 'SAU', 2020, 14.5),
      ('3.1.1', 'SAU', 2021, 13.2),
      ('3.1.1', 'SAU', 2022, 12.8),
      
      -- Saudi Arabia skilled birth attendance (percentage)
      ('3.1.2', 'SAU', 2020, 98.4),
      ('3.1.2', 'SAU', 2021, 98.7),
      ('3.1.2', 'SAU', 2022, 99.1),
      
      -- Saudi Arabia under-5 mortality (per 1,000 live births)
      ('3.2.1', 'SAU', 2020, 7.8),
      ('3.2.1', 'SAU', 2021, 7.5),
      ('3.2.1', 'SAU', 2022, 7.3),
      
      -- Saudi Arabia HIV incidence (per 1,000 uninfected population)
      ('3.3.1', 'SAU', 2020, 0.04),
      ('3.3.1', 'SAU', 2021, 0.03),
      ('3.3.1', 'SAU', 2022, 0.03)
    ON CONFLICT (indicator_code, geo_code, year) DO NOTHING;

    -- Insert sample rankings
    INSERT INTO sdg_rankings (indicator_code, year, saudi_rank, total_countries, percentile, best_country, best_value, worst_country, worst_value)
    VALUES
      ('3.1.1', 2022, 24, 183, 86.5, 'NOR', 4.2, 'SSD', 1150.0),
      ('3.1.2', 2022, 15, 187, 93.2, 'DEU', 100.0, 'SSD', 16.5),
      ('3.2.1', 2022, 30, 193, 84.7, 'FIN', 2.1, 'SSD', 135.0),
      ('3.3.1', 2022, 42, 175, 75.5, 'DNK', 0.01, 'LSO', 12.7)
    ON CONFLICT (indicator_code, year) DO NOTHING;
    
    RETURN TRUE;
  END;
  $$;
  `;
  
  try {
    // Create the RPC functions
    console.log('Creating RPC functions...');
    const { error: error1 } = await supabase.rpc('execute_sql', { sql: createGoalsTableSql });
    if (error1) {
      if (error1.message.includes('execute_sql')) {
        console.error('The execute_sql function does not exist. Creating manually...');
        // We'll do this another way
      } else {
        console.error('Error creating goals table RPC function:', error1.message);
        return false;
      }
    }
    
    // Create the functions directly using raw SQL via the REST API
    console.log('Creating functions using direct PostgreSQL...');
    await supabase.postgrest.rpc('create_sdg_goals_table_function', {
      sql: createGoalsTableSql
    });
    
    await supabase.postgrest.rpc('create_sdg_tables_function', {
      sql: createTablesRpcSql  
    });
    
    await supabase.postgrest.rpc('create_sdg_sample_data_function', {
      sql: insertSampleDataRpcSql
    });
    
    return true;
  } catch (error) {
    console.error('Error creating functions:', error.message);
    return false;
  }
}

// Insert sample data directly using supabase client
async function insertSampleData() {
  try {
    // Insert Goal 3
    console.log('Inserting SDG Goal 3...');
    const { error: goalsError } = await supabase
      .from('sdg_goals')
      .insert({
        code: '3',
        title: 'Good Health and Well-being',
        description: 'Ensure healthy lives and promote well-being for all at all ages',
        color: '#4C9F38'
      })
      .select();
    
    if (goalsError && !goalsError.message.includes('duplicate key')) {
      console.error('Error inserting goal:', goalsError.message);
      return false;
    }
    
    // Insert targets
    console.log('Inserting SDG Targets...');
    const targets = [
      {
        code: '3.1',
        description: 'By 2030, reduce the global maternal mortality ratio to less than 70 per 100,000 live births',
        goal_code: '3'
      },
      {
        code: '3.2',
        description: 'By 2030, end preventable deaths of newborns and children under 5 years of age',
        goal_code: '3'
      },
      {
        code: '3.3',
        description: 'By 2030, end the epidemics of AIDS, tuberculosis, malaria and neglected tropical diseases and combat hepatitis',
        goal_code: '3'
      }
    ];
    
    for (const target of targets) {
      const { error: targetError } = await supabase
        .from('sdg_targets')
        .insert(target)
        .select();
      
      if (targetError && !targetError.message.includes('duplicate key')) {
        console.error('Error inserting target:', targetError.message);
        return false;
      }
    }
    
    // Insert indicators
    console.log('Inserting SDG Indicators...');
    const indicators = [
      {
        code: '3.1.1',
        title: 'Maternal mortality ratio',
        goal_code: '3',
        target_code: '3.1',
        series_code: 'SH_MMR_RISK'
      },
      {
        code: '3.1.2',
        title: 'Proportion of births attended by skilled health personnel',
        goal_code: '3',
        target_code: '3.1',
        series_code: 'SH_STA_BRTATT'
      },
      {
        code: '3.2.1',
        title: 'Under-five mortality rate',
        goal_code: '3',
        target_code: '3.2',
        series_code: 'SH_DYN_MORT'
      },
      {
        code: '3.3.1',
        title: 'Number of new HIV infections per 1,000 uninfected',
        goal_code: '3',
        target_code: '3.3',
        series_code: 'SH_HIV_INCD'
      }
    ];
    
    for (const indicator of indicators) {
      const { error: indicatorError } = await supabase
        .from('sdg_indicators')
        .insert(indicator)
        .select();
      
      if (indicatorError && !indicatorError.message.includes('duplicate key')) {
        console.error('Error inserting indicator:', indicatorError.message);
        return false;
      }
    }
    
    // Insert observations
    console.log('Inserting SDG Observations...');
    const observations = [
      // Saudi Arabia maternal mortality ratio (per 100,000 live births)
      { indicator_code: '3.1.1', geo_code: 'SAU', year: 2020, value: 14.5 },
      { indicator_code: '3.1.1', geo_code: 'SAU', year: 2021, value: 13.2 },
      { indicator_code: '3.1.1', geo_code: 'SAU', year: 2022, value: 12.8 },
      
      // Saudi Arabia skilled birth attendance (percentage)
      { indicator_code: '3.1.2', geo_code: 'SAU', year: 2020, value: 98.4 },
      { indicator_code: '3.1.2', geo_code: 'SAU', year: 2021, value: 98.7 },
      { indicator_code: '3.1.2', geo_code: 'SAU', year: 2022, value: 99.1 },
      
      // Saudi Arabia under-5 mortality (per 1,000 live births)
      { indicator_code: '3.2.1', geo_code: 'SAU', year: 2020, value: 7.8 },
      { indicator_code: '3.2.1', geo_code: 'SAU', year: 2021, value: 7.5 },
      { indicator_code: '3.2.1', geo_code: 'SAU', year: 2022, value: 7.3 },
      
      // Saudi Arabia HIV incidence (per 1,000 uninfected population)
      { indicator_code: '3.3.1', geo_code: 'SAU', year: 2020, value: 0.04 },
      { indicator_code: '3.3.1', geo_code: 'SAU', year: 2021, value: 0.03 },
      { indicator_code: '3.3.1', geo_code: 'SAU', year: 2022, value: 0.03 }
    ];
    
    const { error: obsError } = await supabase
      .from('sdg_observations')
      .insert(observations)
      .select();
    
    if (obsError && !obsError.message.includes('duplicate key')) {
      console.error('Error inserting observations:', obsError.message);
      return false;
    }
    
    // Insert rankings
    console.log('Inserting SDG Rankings...');
    const rankings = [
      {
        indicator_code: '3.1.1',
        year: 2022,
        saudi_rank: 24, 
        total_countries: 183,
        percentile: 86.5,
        best_country: 'NOR',
        best_value: 4.2,
        worst_country: 'SSD',
        worst_value: 1150.0
      },
      {
        indicator_code: '3.1.2',
        year: 2022,
        saudi_rank: 15,
        total_countries: 187,
        percentile: 93.2,
        best_country: 'DEU',
        best_value: 100.0,
        worst_country: 'SSD',
        worst_value: 16.5
      },
      {
        indicator_code: '3.2.1',
        year: 2022,
        saudi_rank: 30,
        total_countries: 193,
        percentile: 84.7,
        best_country: 'FIN',
        best_value: 2.1,
        worst_country: 'SSD',
        worst_value: 135.0
      },
      {
        indicator_code: '3.3.1',
        year: 2022,
        saudi_rank: 42,
        total_countries: 175,
        percentile: 75.5,
        best_country: 'DNK',
        best_value: 0.01,
        worst_country: 'LSO',
        worst_value: 12.7
      }
    ];
    
    const { error: rankError } = await supabase
      .from('sdg_rankings')
      .insert(rankings)
      .select();
    
    if (rankError && !rankError.message.includes('duplicate key')) {
      console.error('Error inserting rankings:', rankError.message);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error inserting sample data:', error.message);
    return false;
  }
}

// Main function to create tables and insert sample data
async function setupSDGTables() {
  console.log('Setting up SDG tables in Supabase...');
  
  // Create tables
  console.log('Creating SDG tables...');
  try {
    // Create goals table
    console.log('Creating goals table...');
    const { data: goalsResult, error: goalsError } = await supabase
      .from('sdg_goals')
      .select()
      .limit(1);
    
    if (goalsError && goalsError.code === '42P01') {
      // Table doesn't exist, create it
      const { error } = await supabase.rpc('create_tables');
      if (error) {
        console.error('Error creating tables:', error.message);
      }
    }
  } catch (error) {
    console.error('Error checking table existence:', error.message);
  }
  
  // Create tables directly
  try {
    // Create goals table
    const { error: goalsError } = await supabase.rpc('create_tables', {
      sql: `
        CREATE TABLE IF NOT EXISTS sdg_goals (
          id SERIAL PRIMARY KEY,
          code VARCHAR(10) NOT NULL UNIQUE,
          title VARCHAR(255) NOT NULL,
          description TEXT,
          color VARCHAR(20),
          icon VARCHAR(255),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    });
    
    if (goalsError) {
      console.warn('Could not create tables using RPC:', goalsError.message);
      console.log('Attempting direct table creation...');
      
      // Direct table creation
      console.log('Creating SDG Goals table...');
      await supabase
        .from('sdg_goals')
        .insert({
          code: 'temp',
          title: 'Temporary'
        })
        .select();
    }
  } catch (error) {
    console.warn('Error in table setup:', error.message);
  }
  
  // Just try inserting data directly
  console.log('\nInserting sample data directly...');
  const dataInserted = await insertSampleData();
  if (!dataInserted) {
    console.error('❌ Issues inserting sample data.');
  } else {
    console.log('✅ Sample data inserted.');
  }
  
  // Count records in each table to verify
  try {
    const { data: goalsCount, error: goalsError } = await supabase.from('sdg_goals').select('*', { count: 'exact' });
    const { data: targetsCount, error: targetsError } = await supabase.from('sdg_targets').select('*', { count: 'exact' });
    const { data: indicatorsCount, error: indicatorsError } = await supabase.from('sdg_indicators').select('*', { count: 'exact' });
    const { data: observationsCount, error: observationsError } = await supabase.from('sdg_observations').select('*', { count: 'exact' });
    const { data: rankingsCount, error: rankingsError } = await supabase.from('sdg_rankings').select('*', { count: 'exact' });
    
    if (goalsError || targetsError || indicatorsError || observationsError || rankingsError) {
      console.error('Error counting records:', goalsError || targetsError || indicatorsError || observationsError || rankingsError);
    } else {
      console.log('\nTable record counts:');
      console.log(`- sdg_goals: ${goalsCount?.length || 0} goals`);
      console.log(`- sdg_targets: ${targetsCount?.length || 0} targets`);
      console.log(`- sdg_indicators: ${indicatorsCount?.length || 0} indicators`);
      console.log(`- sdg_observations: ${observationsCount?.length || 0} observations`);
      console.log(`- sdg_rankings: ${rankingsCount?.length || 0} rankings`);
    }
    
    console.log('\n🎉 SDG database setup completed!');
    console.log('You can now refresh the SDG Dashboard to see the populated data.');
  } catch (error) {
    console.error('❌ Error verifying table counts:', error.message);
  }
}

// Run setup
setupSDGTables();
