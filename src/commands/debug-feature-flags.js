/**
 * Debug Feature Flags
 * 
 * This script can be run in the browser console to diagnose and fix feature flags issues.
 * It provides detailed information about the current state of feature flags in localStorage and
 * allows for direct manipulation to troubleshoot persistence problems.
 * 
 * How to use:
 * 1. Open browser dev tools (F12)
 * 2. Copy and paste this entire script into the console
 * 3. Run the diagnostic functions as needed
 */

// Global namespace for our debug tools
window.ffDebug = {};

/**
 * Show detailed information about feature flags
 */
window.ffDebug.showStatus = function() {
  const cache = localStorage.getItem('feature_flags_cache');
  const override = localStorage.getItem('feature_flags_override');
  
  console.group('%c🔍 Feature Flags Status', 'color: blue; font-weight: bold; font-size: 14px');
  
  // Parse and display primary cache
  if (cache) {
    try {
      const parsedCache = JSON.parse(cache);
      console.log('%c✓ Primary Cache (feature_flags_cache):', 'color: green; font-weight: bold');
      console.table(parsedCache);
    } catch (err) {
      console.log('%c❌ Primary Cache Error:', 'color: red; font-weight: bold', err);
      console.log('Raw value:', cache);
    }
  } else {
    console.log('%c❌ Primary Cache Missing', 'color: red; font-weight: bold');
  }
  
  // Parse and display secondary cache (override)
  if (override) {
    try {
      const parsedOverride = JSON.parse(override);
      console.log('%c✓ Legacy Cache (feature_flags_override):', 'color: orange; font-weight: bold');
      console.table(parsedOverride);
    } catch (err) {
      console.log('%c❌ Legacy Cache Error:', 'color: red; font-weight: bold', err);
      console.log('Raw value:', override);
    }
  } else {
    console.log('%c❌ Legacy Cache Missing', 'color: orange; font-weight: bold');
  }
  
  // Check for key mismatches
  if (cache && override) {
    try {
      const parsedCache = JSON.parse(cache);
      const parsedOverride = JSON.parse(override);
      
      const cacheKeys = Object.keys(parsedCache).sort().join(',');
      const overrideKeys = Object.keys(parsedOverride).sort().join(',');
      
      if (cacheKeys !== overrideKeys) {
        console.log('%c⚠️ Key Mismatch Between Caches', 'color: red; font-weight: bold');
        console.log('Primary keys:', cacheKeys);
        console.log('Legacy keys:', overrideKeys);
      } else {
        console.log('%c✓ Key Consistency Check Passed', 'color: green; font-weight: bold');
      }
      
      // Check for value mismatches
      const mismatchedValues = [];
      Object.keys(parsedCache).forEach(key => {
        if (parsedCache[key] !== parsedOverride[key]) {
          mismatchedValues.push({ 
            key, 
            primaryValue: parsedCache[key], 
            legacyValue: parsedOverride[key] 
          });
        }
      });
      
      if (mismatchedValues.length > 0) {
        console.log('%c⚠️ Value Mismatches Between Caches', 'color: red; font-weight: bold');
        console.table(mismatchedValues);
      } else {
        console.log('%c✓ Value Consistency Check Passed', 'color: green; font-weight: bold');
      }
    } catch (err) {
      console.log('%c❌ Consistency Check Error:', 'color: red; font-weight: bold', err);
    }
  }
  
  console.groupEnd();
  
  return "Status check complete. See console for details.";
};

/**
 * Fix any inconsistencies between primary and legacy storage
 */
window.ffDebug.fixInconsistencies = function() {
  const cache = localStorage.getItem('feature_flags_cache');
  const override = localStorage.getItem('feature_flags_override');
  
  // Default flags if we need to recreate everything
  const defaultFlags = {
    useCustomWorkflowStages: false,
    useSwimlanes: true,
    useWipLimits: false,
    useAgingIndicators: false
  };
  
  let sourceFlags;
  
  // Figure out which source to use
  if (cache) {
    try {
      sourceFlags = JSON.parse(cache);
      console.log('Using primary cache as source');
    } catch (err) {
      console.log('Primary cache parse error, trying legacy cache');
      
      if (override) {
        try {
          sourceFlags = JSON.parse(override);
          console.log('Using legacy cache as source');
        } catch (err) {
          console.log('Both caches invalid, using defaults');
          sourceFlags = defaultFlags;
        }
      } else {
        console.log('No valid cache found, using defaults');
        sourceFlags = defaultFlags;
      }
    }
  } else if (override) {
    try {
      sourceFlags = JSON.parse(override);
      console.log('Using legacy cache as source (primary missing)');
    } catch (err) {
      console.log('Legacy cache parse error, using defaults');
      sourceFlags = defaultFlags;
    }
  } else {
    console.log('No cache found, using defaults');
    sourceFlags = defaultFlags;
  }
  
  // Ensure we have all the expected keys with correct types
  const validatedFlags = {
    useCustomWorkflowStages: Boolean(sourceFlags.useCustomWorkflowStages),
    useSwimlanes: sourceFlags.useSwimlanes === undefined ? true : Boolean(sourceFlags.useSwimlanes),
    useWipLimits: Boolean(sourceFlags.useWipLimits),
    useAgingIndicators: Boolean(sourceFlags.useAgingIndicators)
  };
  
  // Save to both storage locations
  const serialized = JSON.stringify(validatedFlags);
  localStorage.setItem('feature_flags_cache', serialized);
  localStorage.setItem('feature_flags_override', serialized);
  
  console.log('%c✓ Feature flags fixed and synchronized', 'color: green; font-weight: bold');
  console.table(validatedFlags);
  
  return "Fix complete. Refresh the page to apply changes.";
};

/**
 * Force all flags to be enabled
 */
window.ffDebug.enableAll = function() {
  const flags = {
    useCustomWorkflowStages: true,
    useSwimlanes: true,
    useWipLimits: true,
    useAgingIndicators: true
  };
  
  localStorage.setItem('feature_flags_cache', JSON.stringify(flags));
  localStorage.setItem('feature_flags_override', JSON.stringify(flags));
  
  console.log('%c✓ All feature flags enabled!', 'color: green; font-weight: bold');
  console.table(flags);
  
  return "All flags enabled. Refresh the page to apply changes.";
};

/**
 * Force all flags to be disabled
 */
window.ffDebug.disableAll = function() {
  const flags = {
    useCustomWorkflowStages: false,
    useSwimlanes: false,
    useWipLimits: false,
    useAgingIndicators: false
  };
  
  localStorage.setItem('feature_flags_cache', JSON.stringify(flags));
  localStorage.setItem('feature_flags_override', JSON.stringify(flags));
  
  console.log('%c✓ All feature flags disabled!', 'color: red; font-weight: bold');
  console.table(flags);
  
  return "All flags disabled. Refresh the page to apply changes.";
};

/**
 * Reset to default system values
 */
window.ffDebug.resetToDefaults = function() {
  const flags = {
    useCustomWorkflowStages: false,
    useSwimlanes: true,
    useWipLimits: false,
    useAgingIndicators: false
  };
  
  localStorage.setItem('feature_flags_cache', JSON.stringify(flags));
  localStorage.setItem('feature_flags_override', JSON.stringify(flags));
  
  console.log('%c✓ Feature flags reset to defaults!', 'color: blue; font-weight: bold');
  console.table(flags);
  
  return "Flags reset to defaults. Refresh the page to apply changes.";
};

/**
 * Clear all storage and start fresh
 */
window.ffDebug.clearAll = function() {
  localStorage.removeItem('feature_flags_cache');
  localStorage.removeItem('feature_flags_override');
  
  console.log('%c✓ All feature flags storage cleared!', 'color: orange; font-weight: bold');
  
  return "Storage cleared. Refresh the page to restart with system defaults.";
};

// Print usage instructions
console.log(`
=============================================
Feature Flags Debug Tools
=============================================

The following commands are available:

ffDebug.showStatus()
  - Show detailed information about feature flags in localStorage

ffDebug.fixInconsistencies()
  - Fix any inconsistencies between storage locations

ffDebug.enableAll()
  - Force all feature flags to be enabled

ffDebug.disableAll()
  - Force all feature flags to be disabled

ffDebug.resetToDefaults()
  - Reset to system default values

ffDebug.clearAll()
  - Clear all feature flags storage completely

After using any of these commands, refresh the page to apply changes.
`);
