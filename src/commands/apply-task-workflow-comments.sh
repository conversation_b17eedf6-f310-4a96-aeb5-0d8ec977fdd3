#!/bin/bash
# apply-task-workflow-comments.sh
# Run the task workflow and comments migration on Unix/Linux/macOS

# Get the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Navigate to the project root directory (2 levels up)
cd "$SCRIPT_DIR/../.."

# Run the migration script
echo "Running task workflow and comments migration..."
node --experimental-modules src/commands/apply_task_workflow_comments.js

# Check if the script executed successfully
if [ $? -eq 0 ]; then
  echo "Migration script completed successfully!"
else
  echo "Migration script failed. Check the error messages above."
  exit 1
fi
