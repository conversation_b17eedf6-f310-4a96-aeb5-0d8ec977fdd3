# PowerShell script to run the admin capabilities assignment

# Check if node is installed
try {
    Get-Command node -ErrorAction Stop | Out-Null
} catch {
    Write-Host "Node.js is not installed. Please install Node.js to run this script." -ForegroundColor Red
    exit 1
}

# Get the directory where the script is located
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path

# Run the JavaScript file with Node.js
Write-Host "Running admin capabilities assignment script..."
node "$scriptDir\assign-admin-capabilities.js"

# Check if the command was successful
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Admin capabilities successfully assigned!" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to assign admin capabilities. See error messages above." -ForegroundColor Red
    exit 1
}
