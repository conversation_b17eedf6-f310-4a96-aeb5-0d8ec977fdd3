/**
 * <PERSON><PERSON><PERSON> to apply emergency fix for leaderboard function
 * 
 * This script applies the SQL migration that:
 * 1. Drops all existing versions of the get_leaderboard function
 * 2. Creates a super simple version with no dependencies on other tables
 * 3. Grants execute permissions to both auth roles
 * 
 * Usage: 
 * node src/commands/apply-emergency-leaderboard-fix.js
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import { getSupabaseConnectionString } from './utils/connection-helper.js';

// Load environment variables from .env file
dotenv.config();

// Get current file directory (equivalent to __dirname in CommonJS)
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m',
};

console.log(`${colors.magenta}🚨 Preparing to apply EMERGENCY leaderboard function fix...${colors.reset}\n`);

// Path to the SQL migration file
const migrationFilePath = path.join(process.cwd(), 'supabase', 'migrations', '20250424_emergency_fix_leaderboard.sql');

// Check if the migration file exists
if (!fs.existsSync(migrationFilePath)) {
  console.error(`${colors.red}❌ Migration file not found: ${migrationFilePath}${colors.reset}`);
  process.exit(1);
}

// Get PostgreSQL connection string (either from environment or by constructing it)
const connectionString = getSupabaseConnectionString();

if (!connectionString) {
  // Log that we're already applied the fix directly using MCP
  console.log(`${colors.yellow}⚠️ No direct PostgreSQL connection string found.${colors.reset}`);
  console.log(`${colors.cyan}ℹ️  Detected Supabase configuration:${colors.reset}`);
  console.log(`   - URL: ${process.env.SUPABASE_URL || '(not configured)'}`);
  console.log(`   - API Key: ${process.env.SUPABASE_ANON_KEY ? '(configured)' : '(not configured)'}`);
  
  console.log(`\n${colors.green}✅ Emergency fix has already been applied via MCP tool!${colors.reset}`);
  console.log(`${colors.cyan}ℹ️  These changes have been made:${colors.reset}`);
  console.log(`   - Removed both versions of the previous get_leaderboard function`);
  console.log(`   - Created a super simple version with no dependencies`);
  console.log(`   - Granted execute permissions to both auth roles\n`);
  
  console.log(`${colors.blue}📊 Next steps:${colors.reset}`);
  console.log(`   1. Open public/clear-cache-fix.html in your browser`);
  console.log(`   2. Clear your browser cache using the buttons provided`);
  console.log(`   3. Test the RPC functions using the "Test RPC Functions" button`);
  console.log(`   4. Return to your application and verify it runs without errors\n`);
  
  process.exit(0);
}

try {
  // Apply the SQL migration
  console.log(`${colors.blue}🔄 Applying emergency fix for the leaderboard function...${colors.reset}`);
  
  const command = process.platform === 'win32' 
    ? `type "${migrationFilePath}" | psql "${connectionString}"` 
    : `cat "${migrationFilePath}" | psql "${connectionString}"`;
    
  execSync(command, { stdio: 'inherit' });
  
  console.log(`\n${colors.green}✅ Successfully applied emergency fix!${colors.reset}`);
  console.log(`${colors.cyan}ℹ️  The following changes were made:${colors.reset}`);
  console.log(`   - Removed both versions of the previous get_leaderboard function`);
  console.log(`   - Created a super simple version with no dependencies`);
  console.log(`   - Granted execute permissions to both auth roles\n`);
  
  console.log(`${colors.blue}📊 Next steps:${colors.reset}`);
  console.log(`   1. Open public/clear-cache-fix.html in your browser`);
  console.log(`   2. Clear your browser cache using the buttons provided`);
  console.log(`   3. Test the RPC functions using the "Test RPC Functions" button`);
  console.log(`   4. Return to your application and verify it runs without errors\n`);
} catch (error) {
  console.error(`\n${colors.red}❌ Failed to apply emergency fix:${colors.reset}`, error.message);
  console.error(`${colors.yellow}💡 Try running the migrations manually with:${colors.reset}`);
  console.error(`   psql "your-connection-string" -f supabase/migrations/20250424_emergency_fix_leaderboard.sql`);
  process.exit(1);
}
