// <PERSON>ript to update product iterations with missing owner/manager IDs
// Usage: node src/commands/update-product-iterations.js YOUR_USER_ID

import { createClient } from '@supabase/supabase-js';

// Define Supabase client directly to avoid TypeScript import issues
// Get Supabase connection info from environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_KEY;

// Ensure environment variables are set
if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Missing Supabase environment variables.');
  console.error('Make sure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in your environment.');
  console.error('Create a .env file based on .env.example or set these variables in your environment.');
  process.exit(1);
}
const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

const updateProductIterations = async (userId) => {
  console.log(`Updating product iterations for user ${userId}...`);
  
  // Step 1: Find all pending dependencies
  const { data: pendingDependencies, error: dependenciesError } = await supabase
    .from('product_iteration_dependencies')
    .select('id, product_iteration_id, title, dependency_type, approval_status')
    .eq('approval_status', 'pending');
    
  if (dependenciesError) {
    console.error('Error fetching pending dependencies:', dependenciesError);
    return;
  }
  
  console.log(`Found ${pendingDependencies.length} pending dependencies`);
  
  if (pendingDependencies.length === 0) {
    console.log('No pending dependencies found to update.');
    return;
  }
  
  // Log the pending dependencies for reference
  console.log('Pending dependencies:');
  pendingDependencies.forEach(dep => {
    console.log(`- ID: ${dep.id}, Title: ${dep.title || dep.dependency_type}, Product Iteration: ${dep.product_iteration_id}`);
  });
  
  // Step 2: Get unique product iteration IDs
  const productIterationIds = [...new Set(pendingDependencies
    .filter(dep => dep.product_iteration_id) // Filter out any null product_iteration_id
    .map(dep => dep.product_iteration_id))];
  
  console.log(`Found ${productIterationIds.length} unique product iterations to update`);
  
  if (productIterationIds.length === 0) {
    console.log('No valid product iterations found to update.');
    return;
  }
  
  // Step 3: Check current state of product iterations
  const { data: currentIterations, error: checkError } = await supabase
    .from('product_iterations')
    .select('id, product_owner_id, product_manager_id')
    .in('id', productIterationIds);
    
  if (checkError) {
    console.error('Error checking product iterations:', checkError);
    return;
  }
  
  console.log('Current state of product iterations:');
  currentIterations.forEach(iter => {
    console.log(`- ID: ${iter.id}, Owner: ${iter.product_owner_id || 'Not set'}, Manager: ${iter.product_manager_id || 'Not set'}`);
  });
  
  // Step 4: Update product iterations to set the user as owner
  const { data: updateResult, error: updateError } = await supabase
    .from('product_iterations')
    .update({ product_owner_id: userId })
    .in('id', productIterationIds)
    .select('id, product_owner_id, product_manager_id');
    
  if (updateError) {
    console.error('Error updating product iterations:', updateError);
    return;
  }
  
  console.log(`Successfully updated ${updateResult.length} product iterations`);
  console.log('Updated product iterations:');
  updateResult.forEach(iter => {
    console.log(`- ID: ${iter.id}, Owner: ${iter.product_owner_id || 'Not set'}, Manager: ${iter.product_manager_id || 'Not set'}`);
  });
  
  console.log('Update complete. Your pending dependencies should now appear in the approvals dashboard.');
  console.log('Please refresh the approvals dashboard to see the changes.');
};

// Get user ID from command line argument
const userId = process.argv[2];

if (!userId) {
  console.error('Please provide your user ID as a command line argument');
  console.error('Usage: node src/commands/update-product-iterations.js YOUR_USER_ID');
  process.exit(1);
}

updateProductIterations(userId).catch(console.error);
