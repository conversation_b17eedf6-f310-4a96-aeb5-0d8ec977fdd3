// Script to test URL parsing for product details page
// Usage: node src/commands/test-url-parsing.js

// Simulate URL parsing from React Router
function parseUrl(url) {
  console.log('Parsing URL:', url);
  
  // Extract the product ID from the URL
  const productIdMatch = url.match(/\/products\/([^/?]+)/);
  const productId = productIdMatch ? productIdMatch[1] : null;
  
  // Extract the iteration parameter from the URL
  const iterationMatch = url.match(/[?&]iteration=([^&]+)/);
  const iteration = iterationMatch ? iterationMatch[1] : null;
  
  return { productId, iteration };
}

// Test URLs
const testUrls = [
  '/products/bbe88976-5283-4752-b8f0-b307fcaf4fb7?iteration=2.0',
  '/products/bbe88976-5283-4752-b8f0-b307fcaf4fb7',
  '/products/bbe88976-5283-4752-b8f0-b307fcaf4fb7?iteration=1.0.0',
  '/products/invalid-id?iteration=2.0',
  '/products/bbe88976-5283-4752-b8f0-b307fcaf4fb7?iteration=invalid-iteration',
  '/products/bbe88976-5283-4752-b8f0-b307fcaf4fb7?iteration=',
  '/products/bbe88976-5283-4752-b8f0-b307fcaf4fb7?iteration=2.0&other=param',
  '/products/bbe88976-5283-4752-b8f0-b307fcaf4fb7?other=param&iteration=2.0',
];

// Test URL parsing
console.log('Testing URL parsing...');
testUrls.forEach(url => {
  const { productId, iteration } = parseUrl(url);
  console.log(`URL: ${url}`);
  console.log(`  Product ID: ${productId}`);
  console.log(`  Iteration: ${iteration}`);
  console.log('');
});

// Test URL encoding/decoding
console.log('Testing URL encoding/decoding...');
const specialChars = [
  { char: ' ', desc: 'space' },
  { char: '+', desc: 'plus' },
  { char: '/', desc: 'slash' },
  { char: '?', desc: 'question mark' },
  { char: '&', desc: 'ampersand' },
  { char: '=', desc: 'equals' },
  { char: '#', desc: 'hash' },
  { char: '%', desc: 'percent' },
];

specialChars.forEach(({ char, desc }) => {
  const encoded = encodeURIComponent(char);
  const decoded = decodeURIComponent(encoded);
  console.log(`Character: '${char}' (${desc})`);
  console.log(`  Encoded: ${encoded}`);
  console.log(`  Decoded back: '${decoded}'`);
  console.log('');
});

// Test with the problematic URL
const problematicUrl = '/products/bbe88976-5283-4752-b8f0-b307fcaf4fb7?iteration=2.0';
console.log('Testing problematic URL:', problematicUrl);
const { productId, iteration } = parseUrl(problematicUrl);
console.log(`  Product ID: ${productId}`);
console.log(`  Iteration: ${iteration}`);

// Check if the product ID or iteration might have special characters
console.log('\nChecking for special characters:');
console.log(`  Product ID has special chars: ${/[^a-zA-Z0-9-]/.test(productId)}`);
console.log(`  Iteration has special chars: ${/[^a-zA-Z0-9.]/.test(iteration)}`);

console.log('\nTest complete.');
