/**
 * <PERSON><PERSON><PERSON> to disable the gamification system temporarily
 * 
 * This script:
 * 1. Replaces the useGamification hook with a stub implementation
 * 2. Documents the changes made to the gamification system
 * 
 * Usage: 
 * node src/commands/disable-gamification.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get current file directory (equivalent to __dirname in CommonJS)
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m',
};

console.log(`${colors.magenta}🛠  Preparing to disable gamification system...${colors.reset}\n`);

// Paths to relevant files
const gamificationHookPath = path.join(process.cwd(), 'src', 'hooks', 'useGamification.ts');
const docsPath = path.join(process.cwd(), 'docs', 'GAMIFICATION-DISABLED.md');

// Check if the gamification hook file exists
if (!fs.existsSync(gamificationHookPath)) {
  console.error(`${colors.red}❌ Gamification hook file not found: ${gamificationHookPath}${colors.reset}`);
  console.error(`${colors.yellow}Please make sure you're running this script from the project root directory.${colors.reset}`);
  process.exit(1);
}

try {
  // Create a stub implementation of the useGamification hook
  const stubImplementation = `/**
 * GAMIFICATION DISABLED
 * 
 * The gamification system has been temporarily disabled to improve performance
 * and reduce console errors. This hook now returns stub implementations
 * that maintain API compatibility but don't perform any actual operations.
 */

import { useAuth } from '@/hooks/useAuth';

/**
 * Interface for user gamification profile - maintained for type compatibility
 */
export interface UserGamificationProfile {
  points: number;
  level: number;
  achievements: Array<{
    id: string;
    name: string;
    description: string;
    category: string;
    icon: string;
    earned_at: string;
  }>;
  rank?: number;
  recentEvents?: Array<{
    event_type: string;
    occurred_at: string;
    event_data?: Record<string, any>;
  }>;
}

/**
 * Hook for interacting with the gamification system
 * TEMPORARILY DISABLED - Returns stub implementations
 */
export function useGamification() {
  const { profile } = useAuth();
  
  // Stub implementation that maintains API compatibility
  return {
    // Status - always disabled
    isEnabled: false,
    isLoading: false,
    error: null,
    
    // Data - empty values
    userProfile: null,
    achievements: [],
    leaderboard: [],
    
    // Actions - no-op implementations
    grantAchievement: () => {
      console.debug('[Gamification Disabled] grantAchievement called');
      return Promise.resolve({ success: false, alreadyGranted: false });
    },
    addPoints: () => {
      console.debug('[Gamification Disabled] addPoints called');
      return Promise.resolve({ success: false });
    },
    trackEvent: () => {
      console.debug('[Gamification Disabled] trackEvent called');
      return Promise.resolve();
    },
    refetchProfile: () => {
      console.debug('[Gamification Disabled] refetchProfile called');
      return Promise.resolve();
    },
    
    // Mutations state - always false
    isGrantingAchievement: false,
    isAddingPoints: false,
    isTrackingEvent: false,
    
    // Helper for debugging
    setupStatus: {
      missingTables: false,
      connectionIssue: false
    }
  };
}

// Removed import for getSupabaseClient since it's not needed anymore
`;

  // Check if the hook has already been replaced with the stub implementation
  const currentContent = fs.readFileSync(gamificationHookPath, 'utf8');
  
  if (currentContent.includes('GAMIFICATION DISABLED') && currentContent.includes('This hook now returns stub implementations')) {
    console.log(`${colors.yellow}⚠️  Gamification hook already disabled${colors.reset}`);
  } else {
    // Replace the hook with the stub implementation
    fs.writeFileSync(gamificationHookPath, stubImplementation);
    console.log(`${colors.green}✅ Successfully replaced useGamification hook with stub implementation${colors.reset}`);
  }
  
  // Create documentation for the gamification disabling
  if (!fs.existsSync(docsPath)) {
    const docsContent = `# Gamification System Temporarily Disabled

## Overview

The gamification system has been temporarily disabled to improve application performance and reduce console errors. This document explains the changes made and the plan for future reimplementation.

## Issues Addressed

The gamification system was causing several issues:

1. **Performance Impact**: Numerous RPC calls and database queries were affecting application responsiveness
2. **Console Errors**: Thousands of errors related to gamification were flooding the browser console
3. **Dependency Issues**: The system had dependencies on other systems that were also experiencing issues
4. **Cascading Failures**: Errors in gamification functions were triggering errors in other parts of the application

## Changes Made

### 1. Hook Implementation

The \`useGamification\` hook has been replaced with a stub implementation that:

- Maintains the same API surface for compatibility
- Returns default/empty values instead of making API calls
- Logs to console.debug when methods are called (for debugging purposes)
- Always reports \`isEnabled: false\`

### 2. UI Components

With the hook disabled, UI components that use gamification data will:

- Not display gamification-related UI elements
- Not make API calls to gamification endpoints
- Continue to function normally otherwise

## Effect on User Experience

Users will no longer see:

- Achievement notifications
- Points/level information
- Leaderboards
- Profile gamification data

The core functionality of the application remains unchanged.

## Restoration Plan

The gamification system will be reimplemented in a future release with:

1. Better performance optimization
2. Improved error handling
3. Reduced dependency coupling
4. Proper fallback mechanisms

## Technical Details

### Original Components

The following components were affected:

- \`useGamification\` hook (\`src/hooks/useGamification.ts\`)
- Gamification service (\`src/services/gamification/\`)
- UI components in \`src/components/gamification/\`

### Database

The database tables for gamification remain intact, allowing for:

- Data preservation for when gamification is re-enabled
- Potential background data collection (if desired)
- Easier reintegration in the future

## For Developers

When working with code that may use gamification:

1. The \`useGamification\` hook can still be imported and used normally
2. All methods will return sensible defaults and empty arrays
3. No actual API calls will be made
4. Check \`isEnabled\` (always false) to conditionally render UI

## Timeline

The gamification system will be reimplemented after:

1. Core performance issues are resolved
2. A comprehensive review of the gamification architecture is completed
3. Proper testing infrastructure is in place
`;
    
    fs.writeFileSync(docsPath, docsContent);
    console.log(`${colors.green}✅ Created documentation: docs/GAMIFICATION-DISABLED.md${colors.reset}`);
  } else {
    console.log(`${colors.yellow}⚠️  Documentation file already exists: docs/GAMIFICATION-DISABLED.md${colors.reset}`);
  }
  
  console.log(`${colors.green}\n✅ Successfully disabled gamification system!${colors.reset}`);
  console.log(`${colors.cyan}ℹ️  The following changes were made:${colors.reset}`);
  console.log(`   - Replaced useGamification hook with stub implementation`);
  console.log(`   - Created documentation in docs/GAMIFICATION-DISABLED.md\n`);
  
  console.log(`${colors.blue}📊 Next steps:${colors.reset}`);
  console.log(`   1. Rebuild your application`);
  console.log(`   2. Verify that no gamification-related errors appear in the console`);
  console.log(`   3. Test that the application functions correctly without gamification\n`);
  
} catch (error) {
  console.error(`${colors.red}❌ Failed to disable gamification system:${colors.reset}`, error);
  process.exit(1);
}
