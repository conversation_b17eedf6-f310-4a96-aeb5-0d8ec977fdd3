// <PERSON>ript to fetch and populate SDG data from the UN API
import dotenv from 'dotenv';
import fetch from 'node-fetch';

// Load environment variables
dotenv.config({ path: '.env.local' });

const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
// Use the hardcoded token for consistency with our authentication fix
const EDGE_FUNCTION_SECRET = 'SIMPLE_SDG_SECRET_KEY_2025';

if (!SUPABASE_URL || !EDGE_FUNCTION_SECRET) {
  console.error('❌ Missing required environment variables.');
  console.error('Please ensure VITE_SUPABASE_URL and VITE_EDGE_FUNCTION_SECRET are set in .env.local');
  process.exit(1);
}

// Function to call the Edge Function with a specific action
async function callEdgeFunction(action, params = {}) {
  try {
    console.log(`📡 Calling SDG ETL function with action: ${action}`);
    
    const requestBody = {
      action,
      ...params
    };
    
    const response = await fetch(`${SUPABASE_URL}/functions/v1/sdg-etl`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${EDGE_FUNCTION_SECRET}`
      },
      body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Edge Function failed with status ${response.status}: ${errorText}`);
    }
    
    const result = await response.json();
    console.log(`✅ ${action} completed successfully`);
    return result;
  } catch (error) {
    console.error(`❌ Error calling Edge Function for ${action}:`, error.message);
    throw error;
  }
}

// Main process to fetch SDG goals, targets, indicators and some observations
async function populateSDGData() {
  try {
    console.log('🚀 Starting SDG data population process');
    
    // Step 1: Fetch all SDG goals (and their targets and indicators)
    console.log('Step 1: Fetching SDG goals, targets, and indicators');
    const goalsResult = await callEdgeFunction('all', { lang: 'en' });
    console.log(`Stored ${goalsResult.goals} goals, ${goalsResult.targets} targets, and ${goalsResult.indicators} indicators`);
    
    // Step 2: Fetch some key observations for Saudi Arabia
    console.log('\nStep 2: Fetching observations for selected indicators');
    
    // Define key indicators and their series codes
    const keyIndicators = [
      { indicator: '3.1.1', seriesCode: 'SH_MMR_RISK', description: 'Maternal mortality ratio' },
      { indicator: '3.1.2', seriesCode: 'SH_STA_BRTATT', description: 'Proportion of births attended by skilled health personnel' },
      { indicator: '3.2.1', seriesCode: 'SH_DYN_MORT', description: 'Under-five mortality rate' },
      { indicator: '3.3.1', seriesCode: 'SH_HIV_INCD', description: 'Number of new HIV infections' }
    ];
    
    // Fetch observations for each indicator
    for (const indicator of keyIndicators) {
      console.log(`\nFetching data for indicator ${indicator.indicator}: ${indicator.description}`);
      try {
        const observationResult = await callEdgeFunction('observations', {
          indicatorCode: indicator.indicator,
          seriesCode: indicator.seriesCode,
          geoCode: 'SAU' // Saudi Arabia
        });
        console.log(`Stored ${observationResult.observations.count} observation(s)`);
      } catch (error) {
        console.warn(`⚠️ Could not fetch observations for ${indicator.indicator}: ${error.message}`);
      }
    }
    
    // Step 3: Calculate rankings for these indicators
    console.log('\nStep 3: Calculating rankings for indicators');
    const rankingsResult = await callEdgeFunction('rankings', {
      calculateBenchmarks: true,
      year: 2022 // Use 2022 as a default year for rankings
    });
    
    console.log(`\nRankings calculation results:`);
    console.log(`- Processed: ${rankingsResult.processed} indicators`);
    console.log(`- Succeeded: ${rankingsResult.succeeded} indicators`);
    console.log(`- Failed: ${rankingsResult.failed} indicators`);
    
    console.log('\n🎉 SDG data population completed successfully!');
    console.log('You can now refresh the SDG Dashboard to see the populated cards.');
    
    return {
      success: true,
      goals: goalsResult.goals,
      targets: goalsResult.targets,
      indicators: goalsResult.indicators,
      processed: rankingsResult.processed
    };
  } catch (error) {
    console.error('❌ Error populating SDG data:', error.message);
    return { success: false, error: error.message };
  }
}

// Run the population process
populateSDGData();
