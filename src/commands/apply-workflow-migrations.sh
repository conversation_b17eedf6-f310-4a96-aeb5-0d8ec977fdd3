#!/bin/bash

echo "Applying workflow stage migrations using Supabase CLI..."

# Go to the supabase directory
cd "$(dirname "$0")/../../supabase/" || { echo "Could not find supabase directory"; exit 1; }

# Check if supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "❌ Error: supabase CLI is not installed or not in PATH"
    echo "Please install the Supabase CLI: https://supabase.com/docs/guides/cli"
    exit 1
fi

echo "Running Supabase database migrations..."
supabase db reset || { echo "❌ Error applying migrations with 'supabase db reset'"; exit 1; }

echo "✅ Migrations applied successfully!"
echo "You should now be able to create and manage workflow stages."
echo "Please restart your application if it's currently running."
