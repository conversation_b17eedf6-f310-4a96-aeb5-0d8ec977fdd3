/**
 * Fix Gamification Functions
 * 
 * This script repairs missing SQL functions needed for the gamification system.
 * It specifically addresses the get_leaderboard function that might not exist in the database.
 */

import { createClient } from '@supabase/supabase-js';

// Define the SQL functions that might need to be created or repaired
const sqlFunctions = [
  {
    name: 'get_leaderboard',
    sql: `
      CREATE OR REPLACE FUNCTION get_leaderboard(limit_val INTEGER, offset_val INTEGER)
      RETURNS TABLE (
        user_id UUID,
        display_name TEXT,
        total_points INTEGER,
        level INTEGER,
        achievements_count BIGINT,
        rank BIGINT
      )
      LANGUAGE SQL
      AS $$
        WITH ranked_users AS (
          SELECT 
            up.user_id,
            p.raw_user_meta_data->>'name' as display_name,
            up.total_points,
            up.level,
            COUNT(ua.id) as achievements_count,
            ROW_NUMBER() OVER (ORDER BY up.total_points DESC) as rank
          FROM 
            user_points up
          LEFT JOIN 
            auth.users p ON up.user_id = p.id
          LEFT JOIN 
            user_achievements ua ON up.user_id = ua.user_id
          GROUP BY 
            up.user_id, p.raw_user_meta_data, up.total_points, up.level
        )
        SELECT * FROM ranked_users
        LIMIT limit_val
        OFFSET offset_val;
      $$;
    `
  },
  {
    name: 'get_role_permissions_matrix_alt',
    sql: `
      CREATE OR REPLACE FUNCTION get_role_permissions_matrix_alt(
        limit_val INTEGER DEFAULT 100,
        offset_val INTEGER DEFAULT 0
      )
      RETURNS TABLE (
        user_id UUID,
        display_name TEXT,
        total_points INTEGER,
        level INTEGER,
        achievements_count BIGINT,
        rank BIGINT
      )
      LANGUAGE SQL
      AS $$
        WITH ranked_users AS (
          SELECT 
            up.user_id,
            p.raw_user_meta_data->>'name' as display_name,
            up.total_points,
            up.level,
            COUNT(ua.id) as achievements_count,
            ROW_NUMBER() OVER (ORDER BY up.total_points DESC) as rank
          FROM 
            user_points up
          LEFT JOIN 
            auth.users p ON up.user_id = p.id
          LEFT JOIN 
            user_achievements ua ON up.user_id = ua.user_id
          GROUP BY 
            up.user_id, p.raw_user_meta_data, up.total_points, up.level
        )
        SELECT * FROM ranked_users
        LIMIT limit_val
        OFFSET offset_val;
      $$;
    `
  }
];

/**
 * Fix the missing gamification functions
 */
async function fixGamificationFunctions() {
  // Get environment variables
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY || import.meta.env.VITE_SUPABASE_KEY;

  if (!supabaseUrl || !supabaseKey) {
    console.error('Error: Supabase URL or Key is missing in environment');
    return false;
  }

  // Initialize Supabase client
  const supabase = createClient(supabaseUrl, supabaseKey);

  console.log('🛠️ Fixing gamification functions...');

  // Check if we can execute SQL directly
  const canExecuteSQL = await checkSQLPermissions(supabase);

  if (canExecuteSQL) {
    // Create each SQL function
    for (const func of sqlFunctions) {
      try {
        console.log(`Attempting to create function ${func.name}...`);
        
        const { error } = await supabase.rpc('execute_sql', {
          sql: func.sql
        });
        
        if (error) {
          console.error(`Error creating function ${func.name} via RPC: ${error.message}`);
          console.log(`Trying alternative method...`);
          
          // Try direct SQL execution (might require elevated permissions)
          const { error: directError } = await supabase.rpc('execute_direct_sql', {
            sql: func.sql
          });
          
          if (directError) {
            console.error(`Error creating function via direct SQL: ${directError.message}`);
          } else {
            console.log(`✅ Function ${func.name} created via direct SQL.`);
          }
        } else {
          console.log(`✅ Function ${func.name} created successfully.`);
        }
      } catch (err) {
        console.error(`Error creating function ${func.name}:`, err);
      }
    }
  } else {
    console.error('❌ Cannot execute SQL functions. Please ask an administrator to run the setup script.');
    
    // Create a fallback service function
    console.log('Creating fallback implementation in the frontend service...');
    
    // Alert the user that a patch will be applied in analyticsService
    console.log('A patch will be applied to the analytics service to provide a client-side fallback.');
  }
  
  return true;
}

/**
 * Check if we have permission to execute SQL
 */
async function checkSQLPermissions(supabase) {
  try {
    // Try a simple SQL command to check permissions
    const { error } = await supabase.rpc('execute_sql', {
      sql: 'SELECT 1;'
    });
    
    if (error) {
      if (error.code === '42883') { // function doesn't exist
        console.log('execute_sql function not available.');
        return false;
      } else if (error.code === '42501') { // permission denied
        console.log('Permission denied to execute SQL.');
        return false;
      } else {
        console.log(`Unknown error checking SQL permissions: ${error.message}`);
        return false;
      }
    }
    
    return true;
  } catch (err) {
    console.error('Error checking SQL permissions:', err);
    return false;
  }
}

// Run the function if this script is executed directly
if (import.meta.url === import.meta.env.VITE_ENTRY_URL) {
  fixGamificationFunctions()
    .then(success => {
      if (success) {
        console.log('✅ Gamification functions repair completed.');
      } else {
        console.error('❌ Gamification functions repair failed.');
      }
    })
    .catch(err => {
      console.error('Fatal error:', err);
    });
}

export { fixGamificationFunctions };
