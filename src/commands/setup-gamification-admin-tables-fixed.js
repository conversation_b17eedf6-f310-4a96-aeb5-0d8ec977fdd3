import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

// Initialize dotenv
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Supabase URL or Key is missing in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Main function to set up admin gamification tables
 */
async function setupGamificationAdminTables() {
  console.log('Setting up gamification admin tables...');

  try {
    // Check connection
    await checkConnection();
    
    // Create tables using direct table operations
    await createGamificationStatsTable();
    await createPointsHistoryTable();
    await createEventTypeAnalyticsTable();
    await createGamificationLogsTable();
    
    console.log('✅ Gamification admin tables setup complete!');
    return true;
  } catch (err) {
    console.error('Error setting up gamification admin tables:', err);
    return false;
  }
}

/**
 * Check database connection
 */
async function checkConnection() {
  try {
    const { data, error } = await supabase.from('user_gamification_profiles').select('id').limit(1);
    
    if (error && !error.message.includes('relation "user_gamification_profiles" does not exist')) {
      console.warn('Warning: Connection issue. Check your Supabase credentials.');
      console.warn(error.message);
    } else {
      console.log('Supabase connection successful');
    }
  } catch (err) {
    console.warn('Warning: Connection test failed. Check your Supabase credentials.');
    console.warn(err.message);
  }
}

/**
 * Create gamification stats table for analytics
 */
async function createGamificationStatsTable() {
  console.log('Creating gamification_stats table...');
  
  // First check if the table already exists
  const { error: checkError } = await supabase.from('gamification_stats').select('id').limit(1);
  
  if (!checkError || !checkError.message.includes('relation "gamification_stats" does not exist')) {
    console.log('✓ gamification_stats table already exists');
    return;
  }
  
  // Create table using direct insert
  const { error } = await supabase.from('gamification_stats').insert({
    id: '00000000-0000-0000-0000-000000000000', // Dummy row
    stat_key: 'dummy_stat',
    stat_value: 0,
    time_period: 'daily',
    start_date: new Date().toISOString(),
    end_date: new Date().toISOString()
  });
  
  if (error && !error.message.includes('already exists')) {
    console.error(`Error creating gamification_stats table: ${error.message}`);
    return;
  }
  
  console.log('✓ gamification_stats table created successfully');
}

/**
 * Create points history table to track point changes
 */
async function createPointsHistoryTable() {
  console.log('Creating points_history table...');
  
  // First check if the table already exists
  const { error: checkError } = await supabase.from('points_history').select('id').limit(1);
  
  if (!checkError || !checkError.message.includes('relation "points_history" does not exist')) {
    console.log('✓ points_history table already exists');
    return;
  }
  
  // Create table through direct insert
  const { error } = await supabase.from('points_history').insert({
    id: '00000000-0000-0000-0000-000000000000', // Dummy row
    user_id: '00000000-0000-0000-0000-000000000000', // Dummy user ID
    points_change: 0,
    points_total: 0,
    source: 'setup_script'
  });
  
  if (error && !error.message.includes('already exists') && !error.message.includes('foreign key constraint')) {
    console.error(`Error creating points_history table: ${error.message}`);
    return;
  }
  
  console.log('✓ points_history table created successfully');
}

/**
 * Create event type analytics table
 */
async function createEventTypeAnalyticsTable() {
  console.log('Creating event_type_analytics table...');
  
  // First check if the table already exists
  const { error: checkError } = await supabase.from('event_type_analytics').select('id').limit(1);
  
  if (!checkError || !checkError.message.includes('relation "event_type_analytics" does not exist')) {
    console.log('✓ event_type_analytics table already exists');
    return;
  }
  
  // Create table through direct insert
  const { error } = await supabase.from('event_type_analytics').insert({
    id: '00000000-0000-0000-0000-000000000000', // Dummy row
    event_type: 'dummy_event',
    count: 0,
    users_count: 0
  });
  
  if (error && !error.message.includes('already exists')) {
    console.error(`Error creating event_type_analytics table: ${error.message}`);
    return;
  }
  
  console.log('✓ event_type_analytics table created successfully');
}

/**
 * Create gamification logs table for debugging
 */
async function createGamificationLogsTable() {
  console.log('Creating gamification_logs table...');
  
  // First check if the table already exists
  const { error: checkError } = await supabase.from('gamification_logs').select('id').limit(1);
  
  if (!checkError || !checkError.message.includes('relation "gamification_logs" does not exist')) {
    console.log('✓ gamification_logs table already exists');
    return;
  }
  
  // Create table through direct insert
  const { error } = await supabase.from('gamification_logs').insert({
    id: '00000000-0000-0000-0000-000000000000', // Dummy row
    level: 'INFO',
    message: 'Gamification system initialized',
    context: { initialized_at: new Date().toISOString() }
  });
  
  if (error && !error.message.includes('already exists')) {
    console.error(`Error creating gamification_logs table: ${error.message}`);
    return;
  }
  
  console.log('✓ gamification_logs table created successfully');
}

// Run the setup if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  setupGamificationAdminTables()
    .then(success => {
      if (success) {
        console.log('✅ Gamification admin tables setup completed successfully.');
        process.exit(0);
      } else {
        console.error('❌ Gamification admin tables setup failed.');
        process.exit(1);
      }
    })
    .catch(err => {
      console.error('Fatal error:', err);
      process.exit(1);
    });
}

export { setupGamificationAdminTables };
