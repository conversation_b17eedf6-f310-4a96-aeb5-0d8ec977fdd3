/**
 * Feature Flags Browser Setup Script
 * 
 * This script can be used to set up feature flags directly in the browser when
 * the database migration can't be applied through normal means.
 * 
 * How to use:
 * 1. Open your browser developer console (F12)
 * 2. Copy and paste this entire script
 * 3. Press Enter to run it
 * 
 * IMPORTANT: This script has been updated to work with the roles array
 * in the profiles table instead of a single role field.
 */

// Helpers for directly manipulating user roles
async function grantRole(role) {
  const { data: user } = await window.supabase?.auth.getUser();
  if (!user?.user?.id) {
    console.error('No authenticated user found');
    return false;
  }

  // Get current profile
  const { data: profile } = await window.supabase
    .from('profiles')
    .select('roles')
    .eq('id', user.user.id)
    .single();

  // Add role to roles array if not already there
  if (profile) {
    const roles = Array.isArray(profile.roles) ? profile.roles : [];
    
    if (!roles.includes(role)) {
      roles.push(role);
      
      // Update profile
      const { error } = await window.supabase
        .from('profiles')
        .update({ roles })
        .eq('id', user.user.id);
        
      if (error) {
        console.error(`Error updating roles to add ${role}:`, error);
        return false;
      }
      
      console.log(`%c✅ ${role} role granted!`, 'color: green; font-weight: bold');
      return true;
    } else {
      console.log(`User already has ${role} role`);
      return true;
    }
  }
  
  return false;
}

async function grantAdminRole() {
  return grantRole('admin');
}

async function grantEarlyAdopterRole() {
  return grantRole('early_adopter');
}

// Setup function to enable swimlanes
async function setupFeatureFlags() {
  // Check if window.statLinkFactory exists
  if (!window.statLinkFactory) {
    console.error('Error: window.statLinkFactory not found. Make sure you are on a page where the application is loaded.');
    return;
  }

  try {
    // Store original feature flags for reference
    const originalFlags = await window.statLinkFactory.getFeatureFlags?.() || {};
    console.log('Original feature flags:', originalFlags);

    // Enable swimlanes feature
    console.log('Enabling swimlanes feature...');
    await window.statLinkFactory.setFeatureFlagOverrides?.({
      useSwimlanes: true
    });
    
    // Verify that the flag was set
    const updatedFlags = await window.statLinkFactory.getFeatureFlags?.() || {};
    console.log('Updated feature flags:', updatedFlags);
    
    if (updatedFlags.useSwimlanes === true) {
      console.log('%c✅ Swimlanes feature enabled successfully!', 'color: green; font-weight: bold');
      console.log('Refresh the page to see the changes take effect.');
    } else {
      console.log('%c❌ Failed to enable swimlanes feature.', 'color: red; font-weight: bold');
    }
    
    // Add a helper function to the window object for future use
    window.enableSwimlanesFeature = async function() {
      await window.statLinkFactory.setFeatureFlagOverrides?.({
        useSwimlanes: true
      });
      console.log('%c✅ Swimlanes feature enabled! Refresh the page to see changes.', 'color: green; font-weight: bold');
    }
    
    console.log('You can now use window.enableSwimlanesFeature() at any time to enable swimlanes.');
    
  } catch (error) {
    console.error('Error setting feature flags:', error);
  }
}

// Run the setup
setupFeatureFlags().catch(console.error);

// Alternative manual instructions in case the above doesn't work
console.log(`
=============================================
Alternative: Manual Feature Flag Setup
=============================================

If the automatic setup didn't work, you can try:

1. Directly set override in localStorage:
   localStorage.setItem('feature_flags_cache', '{"useSwimlanes":true,"useCustomWorkflowStages":true,"useWipLimits":true,"useAgingIndicators":true}');

2. Then refresh the page

If you need admin access:
   In the console, run: grantAdminRole()
`);

// Make the functions available globally
window.grantAdminRole = grantAdminRole;
window.grantEarlyAdopterRole = grantEarlyAdopterRole;
window.grantRole = grantRole;
