// Script to test the productService.getProduct function directly
// Usage: node src/commands/test-get-product.js PRODUCT_ID ITERATION_VERSION

import { createClient } from '@supabase/supabase-js';
// Remove dependency on supabase-helper.js

// Define Supabase client directly to avoid TypeScript import issues
// Get Supabase connection info from environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_KEY;

// Ensure environment variables are set
if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Missing Supabase environment variables.');
  console.error('Make sure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in your environment.');
  console.error('Create a .env file based on .env.example or set these variables in your environment.');
  process.exit(1);
}
const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

// Simplified version of the productService.getProduct function
const getProduct = async (id, iterationVersion) => {
  console.log('Fetching product with ID:', id, 'Version:', iterationVersion);
  
  try {
    const { data: productBase, error: baseError } = await supabase
      .from('product_bases')
      .select(`
        id,
        name,
        name_ar,
        department,
        department_ar,
        category,
        frequency,
        created_at,
        updated_at,
        iterations:product_iterations!product_iterations_product_base_id_fkey(
          id,
          version,
          iteration,
          description,
          description_ar,
          status,
          confidentiality_level,
          created_at,
          updated_at,
          product_owner:persons!product_iterations_product_owner_id_fkey(
            id,
            name,
            name_ar,
            email
          ),
          product_manager:persons!product_iterations_product_manager_id_fkey(
            id,
            name,
            name_ar,
            email
          )
        )
      `)
      .eq('id', id)
      .single();

    if (baseError) {
      console.error('Error fetching product:', baseError);
      throw baseError;
    }

    if (!productBase) {
      console.error(`Product with ID ${id} not found`);
      throw new Error('Product not found');
    }

    console.log('Product base:', productBase);
    console.log('Iterations:', productBase.iterations);

    const iterations = productBase.iterations || [];
    
    if (iterations.length === 0) {
      console.warn(`Product ${id} has no iterations`);
    }
    
    iterations.sort((a, b) => {
      const aVer = ((a.iteration || a.version) || "").split('.').map(Number);
      const bVer = ((b.iteration || b.version) || "").split('.').map(Number);
      for (let i = 0; i < Math.max(aVer.length, bVer.length); i++) {
        const aNum = aVer[i] || 0;
        const bNum = bVer[i] || 0;
        if (aNum !== bNum) return bNum - aNum;
      }
      return 0;
    });

    let targetIteration = null;
    
    if (iterationVersion && iterations.length > 0) {
      targetIteration = iterations.find(iter => 
        (iter.iteration === iterationVersion) || (iter.version === iterationVersion)
      );
      
      if (!targetIteration) {
        console.warn(`Requested iteration ${iterationVersion} not found for product ${id}, falling back to latest`);
        targetIteration = iterations[0];
      }
    } else if (iterations.length > 0) {
      targetIteration = iterations[0];
    }

    console.log('Target iteration:', targetIteration);

    const mapDbStatus = (dbStatus) => {
      if (dbStatus === "published" || dbStatus === "approved") {
        return "active";
      }
      return "non_active";
    };
    
    const iterationValue = targetIteration?.iteration || targetIteration?.version || "1.0";

    // Simplified conversion for testing
    const productOwner = targetIteration?.product_owner || null;
    const productManager = targetIteration?.product_manager || null;

    const product = {
      id: productBase.id,
      name: productBase.name,
      name_ar: productBase.name_ar,
      description: targetIteration?.description || "",
      description_ar: targetIteration?.description_ar,
      department: productBase.department,
      department_ar: productBase.department_ar,
      category: productBase.category,
      status: mapDbStatus(targetIteration?.status || "draft"),
      confidentiality_level: targetIteration?.confidentiality_level || "public",
      iteration: iterationValue,
      version: iterationValue,
      created_at: productBase.created_at,
      updated_at: productBase.updated_at,
      frequency: productBase.frequency,
      product_owner: productOwner,
      product_manager: productManager,
      all_versions: iterations.map(iter => ({
        iteration: iter.iteration || iter.version || "",
        version: iter.version || iter.iteration || "",
        created_at: iter.created_at
      })),
      iterations: iterations // Include the full iterations array for debugging
    };

    console.log('Final product:', product);
    return product;
  } catch (error) {
    console.error('Error fetching product:', error);
    throw error;
  }
};

// Get product ID and iteration version from command line arguments
const productId = process.argv[2] || 'bbe88976-5283-4752-b8f0-b307fcaf4fb7'; // Default to the problematic ID
const iterationVersion = process.argv[3] || '2.0'; // Default to the problematic version

// Test the getProduct function
getProduct(productId, iterationVersion)
  .then(product => {
    console.log('\nTest successful! Product data retrieved:');
    console.log(`- ID: ${product.id}`);
    console.log(`- Name: ${product.name}`);
    console.log(`- Iteration: ${product.iteration}`);
    console.log(`- Status: ${product.status}`);
    
    // Check if iterations are included
    if (product.iterations) {
      console.log(`- Iterations count: ${product.iterations.length}`);
    }
    
    // Check if all_versions are included
    if (product.all_versions) {
      console.log(`- All versions count: ${product.all_versions.length}`);
      console.log(`- All versions: ${product.all_versions.map(v => v.version).join(', ')}`);
    }
  })
  .catch(error => {
    console.error('\nTest failed with error:', error);
  })
  .finally(() => {
    console.log('\nTest complete.');
  });
