/**
 * <PERSON><PERSON><PERSON> to fix iteration ID mapping in the products stage system
 * 
 * This script:
 * 1. Adds a warning limiter to the product stages code
 * 2. Adds version number to UUID mapping logic
 * 3. Enhances error handling and UI behavior
 * 
 * Usage: 
 * node src/commands/apply-iteration-id-mapping-fix.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get current file directory (equivalent to __dirname in CommonJS)
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m',
};

console.log(`${colors.magenta}🛠  Preparing to fix iteration ID mapping...${colors.reset}\n`);

// Paths to relevant files
const useProductStagesPath = path.join(process.cwd(), 'src', 'components', 'products', 'stage', 'hooks', 'useProductStages.ts');
const productStagesPath = path.join(process.cwd(), 'src', 'components', 'products', 'ProductStages.tsx');
const docsPath = path.join(process.cwd(), 'docs', 'ITERATION-ID-MAPPING-FIX.md');

// Check if the required files exist
if (!fs.existsSync(useProductStagesPath)) {
  console.error(`${colors.red}❌ useProductStages hook file not found: ${useProductStagesPath}${colors.reset}`);
  process.exit(1);
}

if (!fs.existsSync(productStagesPath)) {
  console.error(`${colors.red}❌ ProductStages component file not found: ${productStagesPath}${colors.reset}`);
  process.exit(1);
}

try {
  // Modification for useProductStages.ts
  let useProductStagesContent = fs.readFileSync(useProductStagesPath, 'utf8');
  let useProductStagesModified = false;
  
  // Add warning limiter
  if (!useProductStagesContent.includes('warningShownRef')) {
    // Add the warning limiter reference
    const importLine = 'import { useState, useCallback, useEffect, useRef } from "react";';
    if (useProductStagesContent.includes(importLine)) {
      // Imports already exist, no need to change
    } else {
      console.log(`${colors.yellow}⚠️  Couldn't find expected import line in useProductStages.ts${colors.reset}`);
    }
    
    // Add warning limiter reference declaration after other refs
    const refsPattern = /const toastShownRef = useRef<Set<string>>\(new Set\(\)\);/;
    if (useProductStagesContent.match(refsPattern)) {
      useProductStagesContent = useProductStagesContent.replace(
        refsPattern,
        `const toastShownRef = useRef<Set<string>>(new Set());\n  const warningShownRef = useRef<Set<string>>(new Set());`
      );
      useProductStagesModified = true;
      console.log(`${colors.green
