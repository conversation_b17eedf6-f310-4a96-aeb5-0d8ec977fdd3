import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Initialize environment variables
dotenv.config();

const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_KEY = process.env.VITE_SUPABASE_ANON_KEY;
const SERVICE_KEY = process.env.VITE_SUPABASE_SERVICE_KEY || SUPABASE_KEY; // Fallback to anon key if service key not available

console.log('Starting dynamic permissions system migration...');

// Execute the migration
runMigration().catch(error => {
  console.error('Migration failed:', error);
  process.exit(1);
});

/**
 * Main migration function
 */
async function runMigration() {
  if (!SUPABASE_URL || !SUPABASE_KEY) {
    console.error('Error: Missing Supabase credentials');
    process.exit(1);
  }

  // Initialize the Supabase client with service key for admin privileges
  const supabase = createClient(SUPABASE_URL, SERVICE_KEY);

  try {
    console.log('Creating basic roles...');
    
    // Create tables if needed
    await createRolesTable(supabase);
    
    // Create admin role and other basic roles
    await createBasicRoles(supabase);
    console.log('✅ Basic roles created successfully');
    
    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

/**
 * Create roles table if it doesn't exist
 */
async function createRolesTable(supabase) {
  // Check if the table exists
  const { error: checkError } = await supabase
    .from('dynamic_roles')
    .select('id')
    .limit(1);
  
  // If there's an error other than relation/table not found, it's a problem
  if (checkError && !checkError.message.includes('relation') && !checkError.message.includes('does not exist')) {
    throw new Error(`Error checking roles table: ${checkError.message}`);
  }
  
  // If the table doesn't exist, create it
  if (checkError && (checkError.message.includes('relation') || checkError.message.includes('does not exist'))) {
    console.log('Creating dynamic_roles table...');
    const createTableSQL = `
    -- Create dynamic_roles table
    CREATE TABLE IF NOT EXISTS dynamic_roles (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      name TEXT NOT NULL UNIQUE,
      name_ar TEXT,
      description TEXT,
      is_active BOOLEAN DEFAULT TRUE,
      metadata JSONB DEFAULT '{}'::jsonb,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
    );

    -- Create dynamic_user_roles table
    CREATE TABLE IF NOT EXISTS dynamic_user_roles (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID NOT NULL,
      role_id UUID NOT NULL REFERENCES dynamic_roles(id) ON DELETE CASCADE,
      constraints JSONB DEFAULT '{}'::jsonb,
      valid_from TIMESTAMP WITH TIME ZONE DEFAULT now(),
      valid_until TIMESTAMP WITH TIME ZONE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
      created_by UUID,
      updated_by UUID,
      UNIQUE(user_id, role_id)
    );
    `;
    
    const { error: createError } = await supabase.rpc('execute_sql', { sql: createTableSQL });
    if (createError) {
      throw new Error(`Failed to create tables: ${createError.message}`);
    }
    console.log('Tables created successfully');
  } else {
    console.log('Tables already exist');
  }
}

/**
 * Create basic roles in the system
 */
async function createBasicRoles(supabase) {
  const roles = [
    {
      name: 'admin',
      name_ar: 'مدير النظام',
      description: 'Complete system administration',
      is_active: true,
      metadata: { is_system_role: true }
    },
    {
      name: 'viewer',
      name_ar: 'مشاهد',
      description: 'Read-only access to authorized resources',
      is_active: true,
      metadata: { is_system_role: true }
    },
    {
      name: 'general_director',
      name_ar: 'مدير عام',
      description: 'Domain-level management',
      is_active: true,
      metadata: { is_system_role: true }
    },
    {
      name: 'department_director',
      name_ar: 'مدير إدارة',
      description: 'Department-level management',
      is_active: true,
      metadata: { is_system_role: true }
    },
    {
      name: 'product_owner',
      name_ar: 'مالك المنتج',
      description: 'Strategic ownership of products and product portfolios',
      is_active: true,
      metadata: { is_system_role: true }
    },
    {
      name: 'product_manager',
      name_ar: 'مدير المنتج',
      description: 'Tactical management of specific products',
      is_active: true,
      metadata: { is_system_role: true }
    },
    {
      name: 'editor',
      name_ar: 'محرر',
      description: 'Content creation and editing',
      is_active: true,
      metadata: { is_system_role: true }
    }
  ];

  // Insert roles one by one, ignoring if they already exist
  for (const role of roles) {
    try {
      const { data, error } = await supabase
        .from('dynamic_roles')
        .insert([role])
        .select('id, name');

      if (error) {
        if (error.code === '23505') { // Unique violation, role already exists
          console.log(`Role '${role.name}' already exists.`);
        } else {
          console.error(`Error creating role '${role.name}':`, error.message);
        }
      } else {
        console.log(`Created role: ${role.name} (${data[0].id})`);
      }
    } catch (error) {
      console.error(`Error creating role '${role.name}':`, error.message);
    }
  }

  return true;
}
