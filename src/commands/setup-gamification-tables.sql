-- Gamification System Tables Setup SQL Script
-- Run this in the Supabase SQL Editor to set up the gamification system

-- User Profiles Table
CREATE TABLE IF NOT EXISTS user_gamification_profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  points INTEGER DEFAULT 0 NOT NULL,
  level INTEGER DEFAULT 1 NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL
);

CREATE INDEX IF NOT EXISTS user_gamification_profiles_points_idx ON user_gamification_profiles (points DESC);

-- Achievements Table
CREATE TABLE IF NOT EXISTS achievements (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  name_ar TEXT,
  description TEXT NOT NULL,
  description_ar TEXT,
  icon TEXT DEFAULT 'trophy' NOT NULL,
  points INTEGER DEFAULT 50 NOT NULL,
  category TEXT DEFAULT 'general' NOT NULL,
  is_active BOOLEAN DEFAULT true NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL,
  created_by UUID REFERENCES auth.users(id),
  updated_by UUID REFERENCES auth.users(id)
);

CREATE INDEX IF NOT EXISTS achievements_category_idx ON achievements (category);

-- User Achievements Junction Table
CREATE TABLE IF NOT EXISTS user_achievements (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  achievement_id UUID NOT NULL REFERENCES achievements(id) ON DELETE CASCADE,
  earned_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL,
  UNIQUE(user_id, achievement_id)
);

CREATE INDEX IF NOT EXISTS user_achievements_user_id_idx ON user_achievements (user_id);
CREATE INDEX IF NOT EXISTS user_achievements_achievement_id_idx ON user_achievements (achievement_id);

-- Gamification Events Table
CREATE TABLE IF NOT EXISTS gamification_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  event_type TEXT NOT NULL,
  event_data JSONB,
  occurred_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL
);

CREATE INDEX IF NOT EXISTS gamification_events_user_id_idx ON gamification_events (user_id);
CREATE INDEX IF NOT EXISTS gamification_events_event_type_idx ON gamification_events (event_type);
CREATE INDEX IF NOT EXISTS gamification_events_occurred_at_idx ON gamification_events (occurred_at DESC);

-- Gamification Config Table
CREATE TABLE IF NOT EXISTS gamification_config (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  key TEXT UNIQUE NOT NULL,
  category TEXT DEFAULT 'general' NOT NULL,
  value JSONB NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT true NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL,
  created_by UUID REFERENCES auth.users(id),
  updated_by UUID REFERENCES auth.users(id)
);

CREATE INDEX IF NOT EXISTS gamification_config_key_idx ON gamification_config (key);
CREATE INDEX IF NOT EXISTS gamification_config_category_idx ON gamification_config (category);

-- Gamification Rules Table
CREATE TABLE IF NOT EXISTS gamification_rules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  name_ar TEXT,
  description TEXT,
  description_ar TEXT,
  conditions JSONB NOT NULL,
  actions JSONB NOT NULL,
  category TEXT DEFAULT 'general' NOT NULL,
  is_active BOOLEAN DEFAULT true NOT NULL,
  priority INTEGER DEFAULT 100 NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL,
  created_by UUID REFERENCES auth.users(id),
  updated_by UUID REFERENCES auth.users(id)
);

CREATE INDEX IF NOT EXISTS gamification_rules_category_idx ON gamification_rules (category);
CREATE INDEX IF NOT EXISTS gamification_rules_priority_idx ON gamification_rules (priority DESC);

-- Statistics and Analytics Tables
CREATE TABLE IF NOT EXISTS gamification_stats (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  stat_key TEXT NOT NULL,
  stat_value INTEGER DEFAULT 0 NOT NULL,
  time_period TEXT NOT NULL,
  start_date TIMESTAMP WITH TIME ZONE NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL,
  UNIQUE(stat_key, time_period, start_date, end_date)
);

CREATE INDEX IF NOT EXISTS gamification_stats_key_period_idx ON gamification_stats (stat_key, time_period);
CREATE INDEX IF NOT EXISTS gamification_stats_date_range_idx ON gamification_stats (start_date, end_date);

-- Points History Table
CREATE TABLE IF NOT EXISTS points_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  points_change INTEGER NOT NULL,
  points_total INTEGER NOT NULL,
  reason TEXT,
  source TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL
);

CREATE INDEX IF NOT EXISTS points_history_user_id_idx ON points_history (user_id);
CREATE INDEX IF NOT EXISTS points_history_created_at_idx ON points_history (created_at DESC);

-- Event Type Analytics Table
CREATE TABLE IF NOT EXISTS event_type_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_type TEXT UNIQUE NOT NULL,
  count INTEGER DEFAULT 0 NOT NULL,
  users_count INTEGER DEFAULT 0 NOT NULL,
  last_occurred_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL
);

CREATE INDEX IF NOT EXISTS event_type_analytics_count_idx ON event_type_analytics (count DESC);

-- Gamification Logs Table
CREATE TABLE IF NOT EXISTS gamification_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  level TEXT NOT NULL,
  message TEXT NOT NULL,
  context JSONB,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL
);

CREATE INDEX IF NOT EXISTS gamification_logs_level_idx ON gamification_logs (level);
CREATE INDEX IF NOT EXISTS gamification_logs_created_at_idx ON gamification_logs (created_at DESC);
CREATE INDEX IF NOT EXISTS gamification_logs_user_id_idx ON gamification_logs (user_id);

-- Analytics Views
-- User achievements summary view
DROP VIEW IF EXISTS user_achievements_summary;
CREATE VIEW user_achievements_summary AS
SELECT 
  ua.user_id,
  COUNT(DISTINCT ua.achievement_id) as achievements_count,
  SUM(a.points) as total_achievement_points,
  MAX(ua.earned_at) as last_achievement_at
FROM 
  user_achievements ua
JOIN 
  achievements a ON ua.achievement_id = a.id
GROUP BY 
  ua.user_id;
  
-- Leaderboard view
DROP VIEW IF EXISTS gamification_leaderboard;
CREATE VIEW gamification_leaderboard AS
SELECT 
  p.id as user_id,
  p.points as total_points,
  p.level,
  COALESCE(uas.achievements_count, 0) as achievements_count,
  COALESCE(ua.email, 'User-' || LEFT(p.id::text, 6)) as display_name,
  RANK() OVER (ORDER BY p.points DESC) as rank
FROM 
  user_gamification_profiles p
LEFT JOIN 
  user_achievements_summary uas ON p.id = uas.user_id
LEFT JOIN 
  auth.users ua ON p.id = ua.id
ORDER BY 
  rank ASC;
  
-- Achievement stats view
DROP VIEW IF EXISTS achievement_stats;
CREATE VIEW achievement_stats AS
SELECT 
  a.id as achievement_id,
  a.name,
  a.description,
  a.icon,
  a.points,
  a.category,
  COUNT(ua.id) as earned_count
FROM 
  achievements a
LEFT JOIN 
  user_achievements ua ON a.id = ua.achievement_id
GROUP BY 
  a.id
ORDER BY 
  earned_count DESC;
  
-- User engagement view
DROP VIEW IF EXISTS user_engagement_stats;
CREATE VIEW user_engagement_stats AS
SELECT
  COUNT(DISTINCT u.id) as total_users,
  COUNT(DISTINCT CASE WHEN e.occurred_at > NOW() - INTERVAL '30 days' THEN e.user_id END) as active_users,
  COALESCE(AVG(p.points), 0) as average_points,
  COALESCE(AVG(CASE WHEN uas.achievements_count IS NOT NULL THEN uas.achievements_count ELSE 0 END), 0) as average_achievements
FROM
  auth.users u
LEFT JOIN
  user_gamification_profiles p ON u.id = p.id
LEFT JOIN
  user_achievements_summary uas ON u.id = uas.user_id
LEFT JOIN
  gamification_events e ON u.id = e.user_id;

-- Initial Data: Achievements
INSERT INTO achievements (name, description, icon, points, category)
VALUES 
('Welcome Aboard', 'Created your account and joined the system', 'Award', 10, 'onboarding'),
('First Product View', 'Viewed a product for the first time', 'Eye', 5, 'products'),
('Explorer', 'Visited 5 different pages in the system', 'Compass', 20, 'navigation'),
('Contributor', 'Created your first product', 'Plus', 50, 'products'),
('Team Player', 'Collaborated on a product with others', 'Users', 30, 'collaboration')
ON CONFLICT DO NOTHING;

-- Initial Data: Configuration
INSERT INTO gamification_config (key, category, value, description)
VALUES
('gamification_enabled', 'system', '{"enabled": true}', 'Master switch for the gamification system'),
('level_formula', 'points', '{"formula": "Math.floor(Math.sqrt(points / 100)) + 1"}', 'Formula to calculate user level based on points'),
('achievement_notification', 'notifications', '{"enabled": true}', 'Show notifications when users earn achievements'),
('level_up_notification', 'notifications', '{"enabled": true}', 'Show notifications when users level up'),
('points_notification', 'notifications', '{"enabled": true, "threshold": 10}', 'Show notifications when users earn points (with minimum threshold)'),
('leaderboard_enabled', 'social', '{"enabled": true}', 'Enable or disable leaderboard functionality')
ON CONFLICT DO NOTHING;

-- Security Policies
-- Very basic policies that don't rely on user_roles

-- Enable RLS on tables
ALTER TABLE user_gamification_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE gamification_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE gamification_config ENABLE ROW LEVEL SECURITY;
ALTER TABLE gamification_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE gamification_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE points_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE event_type_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE gamification_logs ENABLE ROW LEVEL SECURITY;

-- Basic read-access policies for everyone
DROP POLICY IF EXISTS user_gamification_profiles_select ON user_gamification_profiles;
CREATE POLICY user_gamification_profiles_select ON user_gamification_profiles
  FOR SELECT USING (true);

DROP POLICY IF EXISTS achievements_select ON achievements;
CREATE POLICY achievements_select ON achievements
  FOR SELECT USING (true);

DROP POLICY IF EXISTS user_achievements_select ON user_achievements;
CREATE POLICY user_achievements_select ON user_achievements
  FOR SELECT USING (true);

DROP POLICY IF EXISTS gamification_config_select ON gamification_config;
CREATE POLICY gamification_config_select ON gamification_config
  FOR SELECT USING (true);

DROP POLICY IF EXISTS gamification_rules_select ON gamification_rules;
CREATE POLICY gamification_rules_select ON gamification_rules
  FOR SELECT USING (true);

-- Self-access policies for user data
DROP POLICY IF EXISTS user_gamification_profiles_self ON user_gamification_profiles;
CREATE POLICY user_gamification_profiles_self ON user_gamification_profiles
  FOR ALL USING (auth.uid() = id);

DROP POLICY IF EXISTS user_achievements_self ON user_achievements;
CREATE POLICY user_achievements_self ON user_achievements
  FOR ALL USING (auth.uid() = user_id);

DROP POLICY IF EXISTS gamification_events_self ON gamification_events;
CREATE POLICY gamification_events_self ON gamification_events
  FOR ALL USING (auth.uid() = user_id);

DROP POLICY IF EXISTS points_history_self ON points_history;
CREATE POLICY points_history_self ON points_history
  FOR ALL USING (auth.uid() = user_id);

-- For admin-only tables, we'll allow all operations for everyone during testing
-- In production, these would need more restrictive policies
DROP POLICY IF EXISTS gamification_stats_all ON gamification_stats;
CREATE POLICY gamification_stats_all ON gamification_stats FOR ALL USING (true);

DROP POLICY IF EXISTS event_type_analytics_all ON event_type_analytics;
CREATE POLICY event_type_analytics_all ON event_type_analytics FOR ALL USING (true);

DROP POLICY IF EXISTS gamification_logs_all ON gamification_logs;
CREATE POLICY gamification_logs_all ON gamification_logs FOR ALL USING (true);

-- Allow all users to modify achievements and rules for testing purposes
-- In production, these would be admin-only
DROP POLICY IF EXISTS achievements_all ON achievements;
CREATE POLICY achievements_all ON achievements FOR ALL USING (true);

DROP POLICY IF EXISTS gamification_rules_all ON gamification_rules;
CREATE POLICY gamification_rules_all ON gamification_rules FOR ALL USING (true);

DROP POLICY IF EXISTS gamification_config_all ON gamification_config;
CREATE POLICY gamification_config_all ON gamification_config FOR ALL USING (true);
