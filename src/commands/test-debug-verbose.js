// Test the debug function with verbose output
import dotenv from 'dotenv';
import fetch from 'node-fetch';

// Load environment variables
dotenv.config({ path: '.env.local' });

const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const EDGE_FUNCTION_SECRET = process.env.VITE_EDGE_FUNCTION_SECRET;

if (!SUPABASE_URL || !EDGE_FUNCTION_SECRET) {
  console.error('❌ Missing required environment variables.');
  console.error('Please ensure VITE_SUPABASE_URL and VITE_EDGE_FUNCTION_SECRET are set in .env.local');
  process.exit(1);
}

async function testDebugFunction() {
  console.log('=== Debug Function Test With Verbose Output ===');
  console.log(`URL: ${SUPABASE_URL}/functions/v1/debug`);
  console.log(`Secret (length): ${EDGE_FUNCTION_SECRET.length} chars`);
  console.log(`Secret (first 5): ${EDGE_FUNCTION_SECRET.substring(0, 5)}...`);
  console.log(`Secret (last 5): ...${EDGE_FUNCTION_SECRET.substring(EDGE_FUNCTION_SECRET.length - 5)}`);
  
  // Make a direct request to the debug function
  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/debug`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${EDGE_FUNCTION_SECRET}`
      }
    });
    
    console.log('\n=== Server Response ===');
    console.log(`Status: ${response.status} ${response.statusText}`);
    console.log('Headers:', JSON.stringify(Object.fromEntries([...response.headers.entries()]), null, 2));
    
    const responseText = await response.text();
    try {
      const jsonData = JSON.parse(responseText);
      console.log('\n=== Response Body ===');
      console.log(JSON.stringify(jsonData, null, 2));
    } catch (e) {
      console.log('\n=== Response Text (not JSON) ===');
      console.log(responseText);
    }
  } catch (error) {
    console.error(`❌ Error making request: ${error.message}`);
  }
}

// Run the test
testDebugFunction();
