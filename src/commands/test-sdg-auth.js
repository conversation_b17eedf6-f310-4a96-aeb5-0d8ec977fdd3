// <PERSON>ript to test SDG Edge Function authentication
import fetch from 'node-fetch';
import dotenv from 'dotenv';
import chalk from 'chalk';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Required environment variables
const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const EDGE_FUNCTION_SECRET = 'SIMPLE_SDG_SECRET_KEY_2025'; // Fixed value for testing

// Validate environment variables
if (!SUPABASE_URL || !EDGE_FUNCTION_SECRET) {
  console.error(chalk.red('❌ Missing required environment variables.'));
  console.error(chalk.yellow('Please ensure .env.local contains VITE_SUPABASE_URL and VITE_EDGE_FUNCTION_SECRET'));
  process.exit(1);
}

// Test SDG Edge Function with authentication
async function testSDGAuth() {
  const endpoint = `${SUPABASE_URL}/functions/v1/sdg-etl`;
  
  console.log(chalk.blue('=== SDG Edge Function Authentication Test ==='));
  console.log(chalk.gray(`URL: ${endpoint}`));
  console.log(chalk.gray(`Secret (length): ${EDGE_FUNCTION_SECRET.length} chars`));
  console.log(chalk.gray(`Secret (first 5): ${EDGE_FUNCTION_SECRET.substring(0, 5)}...`));
  console.log(chalk.gray(`Secret (last 5): ...${EDGE_FUNCTION_SECRET.substring(EDGE_FUNCTION_SECRET.length - 5)}\n`));

  // Test parameters
  const testParams = {
    action: 'test'
  };

  try {
    console.log(chalk.blue('Sending test request with authentication...'));
    
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${EDGE_FUNCTION_SECRET}`
      },
      body: JSON.stringify(testParams)
    });
    
    console.log(chalk.yellow('\n=== Server Response ==='));
    console.log(chalk.green(`Status: ${response.status} ${response.statusText}`));
    
    // Log headers for debugging
    console.log(chalk.yellow('\n=== Response Headers ==='));
    console.log(chalk.gray(JSON.stringify(Object.fromEntries([...response.headers]), null, 2)));
    
    // Parse response body
    const responseData = await response.json();
    
    console.log(chalk.yellow('\n=== Response Body ==='));
    console.log(chalk.gray(JSON.stringify(responseData, null, 2)));
    
    if (response.status === 200) {
      console.log(chalk.green('\n✅ Authentication successful!'));
    } else {
      console.log(chalk.red('\n❌ Authentication failed with status code:', response.status));
    }

    // Check specifically for the timestamp field to confirm it's really our endpoint
    if (responseData.timestamp) {
      console.log(chalk.green('✅ Response contains timestamp, confirming valid Edge Function response'));
    } else {
      console.log(chalk.yellow('⚠️ Response does not contain expected timestamp field'));
    }

    // Test with environment variable inspection
    console.log(chalk.blue('\nTesting environment variable inspection...'));
    
    const inspectResponse = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${EDGE_FUNCTION_SECRET}`
      },
      body: JSON.stringify({ action: '--debug-env' })
    });
    
    const inspectData = await inspectResponse.json();
    console.log(chalk.yellow('\n=== Environment Variables in Edge Function ==='));
    console.log(chalk.gray(JSON.stringify(inspectData, null, 2)));

    return responseData;
  } catch (error) {
    console.error(chalk.red('\n❌ Error making request:'), error.message);
    return null;
  }
}

// Run the test
testSDGAuth();
