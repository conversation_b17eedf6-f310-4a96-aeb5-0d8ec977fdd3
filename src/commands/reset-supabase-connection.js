/**
 * Reset Supabase Connection
 * 
 * This utility script completely resets the Supabase connection state:
 * 1. Clears all Supabase-related localStorage items
 * 2. Resets the client instance
 * 3. Disables offline mode
 * 4. Verifies connectivity to the Supabase project
 * 
 * Run this script when you've updated Supabase credentials or are experiencing
 * authentication issues.
 */

// Import the gateway directly to access its utilities
import { 
  completeSupabaseReset, 
  checkSupabaseConnectivity, 
  setOfflineMode,
  SUPABASE_URL
} from '../integrations/supabase/gateway.js';

// Display the current Supabase URL
console.log(`Current Supabase URL: ${SUPABASE_URL}`);

// Perform complete reset (clears localStorage, tokens, etc.)
console.log('Performing complete Supabase state reset...');
completeSupabaseReset();

// Disable offline mode if it was enabled
console.log('Disabling offline mode...');
setOfflineMode(false);

// Check connectivity to verify the new configuration
console.log('Checking connectivity to Supabase project...');
checkSupabaseConnectivity()
  .then(isConnected => {
    if (isConnected) {
      console.log('✅ Successfully connected to Supabase!');
      console.log('You can now log in with your credentials.');
    } else {
      console.error('❌ Could not connect to Supabase.');
      console.error('Please check your environment variables in .env:');
      console.error(`VITE_SUPABASE_URL: ${SUPABASE_URL}`);
      console.error('VITE_SUPABASE_ANON_KEY: [hidden for security]');
    }
  })
  .catch(error => {
    console.error('Error checking connectivity:', error);
  });
