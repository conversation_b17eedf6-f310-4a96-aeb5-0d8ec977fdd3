import * as dotenv from 'dotenv';
import { getSupabaseClient } from '../integrations/supabase/gateway.ts';

// Skip Node.js specific modules for browser compatibility
// import { fileURLToPath } from 'url';
// import { dirname } from 'path';

// Initialize dotenv
dotenv.config();

// Browser-friendly alternative to Node.js path functions
// const __filename = fileURLToPath(import.meta.url);
// const __dirname = dirname(__filename);

// Initialize Supabase client
const supabase = getSupabaseClient();

/**
 * Main function to set up admin gamification tables
 */
async function setupGamificationAdminTables() {
  console.log('Setting up gamification admin tables...');

  try {
    // Check if we have admin access
    const { data: rpcData, error: rpcError } = await supabase.rpc('get_current_user_role');
    
    if (rpcError) {
      console.warn('Warning: Unable to check user role. Proceeding anyway, but you may encounter permissions issues.');
      console.warn(rpcError.message);
    } else {
      console.log(`Current user role: ${rpcData}`);
      if (rpcData !== 'postgres' && rpcData !== 'service_role') {
        console.warn('Warning: You are not running with admin privileges. Some operations might fail.');
      }
    }

    // Create tables
    await createGamificationStatsTable();
    await createPointsHistoryTable();
    await createEventTypeAnalyticsTable();
    await createGamificationLogsTable();
    
    // Set up initial data
    await createStatsViews();
    
    // Set up RLS policies
    await setupRlsPolicies();
    
    console.log('✅ Gamification admin tables setup complete!');
    
  } catch (err) {
    console.error('Error setting up gamification admin tables:', err);
    process.exit(1);
  }
}

/**
 * Create gamification stats table for analytics
 */
async function createGamificationStatsTable() {
  console.log('Creating gamification_stats table...');
  
  const { error } = await supabase.rpc('execute_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS gamification_stats (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        stat_key TEXT NOT NULL,
        stat_value INTEGER DEFAULT 0 NOT NULL,
        time_period TEXT NOT NULL,
        start_date TIMESTAMP WITH TIME ZONE NOT NULL,
        end_date TIMESTAMP WITH TIME ZONE NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL,
        UNIQUE(stat_key, time_period, start_date, end_date)
      );
      
      CREATE INDEX IF NOT EXISTS gamification_stats_key_period_idx ON gamification_stats (stat_key, time_period);
      CREATE INDEX IF NOT EXISTS gamification_stats_date_range_idx ON gamification_stats (start_date, end_date);
    `
  });
  
  if (error) {
    throw new Error(`Failed to create gamification_stats table: ${error.message}`);
  }
  
  console.log('✓ gamification_stats table created or already exists');
}

/**
 * Create points history table to track point changes
 */
async function createPointsHistoryTable() {
  console.log('Creating points_history table...');
  
  const { error } = await supabase.rpc('execute_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS points_history (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
        points_change INTEGER NOT NULL,
        points_total INTEGER NOT NULL,
        reason TEXT,
        source TEXT NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL
      );
      
      CREATE INDEX IF NOT EXISTS points_history_user_id_idx ON points_history (user_id);
      CREATE INDEX IF NOT EXISTS points_history_created_at_idx ON points_history (created_at DESC);
    `
  });
  
  if (error) {
    throw new Error(`Failed to create points_history table: ${error.message}`);
  }
  
  console.log('✓ points_history table created or already exists');
}

/**
 * Create event type analytics table
 */
async function createEventTypeAnalyticsTable() {
  console.log('Creating event_type_analytics table...');
  
  const { error } = await supabase.rpc('execute_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS event_type_analytics (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        event_type TEXT UNIQUE NOT NULL,
        count INTEGER DEFAULT 0 NOT NULL,
        users_count INTEGER DEFAULT 0 NOT NULL,
        last_occurred_at TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL
      );
      
      CREATE INDEX IF NOT EXISTS event_type_analytics_count_idx ON event_type_analytics (count DESC);
    `
  });
  
  if (error) {
    throw new Error(`Failed to create event_type_analytics table: ${error.message}`);
  }
  
  console.log('✓ event_type_analytics table created or already exists');
}

/**
 * Create gamification logs table for debugging
 */
async function createGamificationLogsTable() {
  console.log('Creating gamification_logs table...');
  
  const { error } = await supabase.rpc('execute_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS gamification_logs (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        level TEXT NOT NULL,
        message TEXT NOT NULL,
        context JSONB,
        user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()) NOT NULL
      );
      
      CREATE INDEX IF NOT EXISTS gamification_logs_level_idx ON gamification_logs (level);
      CREATE INDEX IF NOT EXISTS gamification_logs_created_at_idx ON gamification_logs (created_at DESC);
      CREATE INDEX IF NOT EXISTS gamification_logs_user_id_idx ON gamification_logs (user_id);
    `
  });
  
  if (error) {
    throw new Error(`Failed to create gamification_logs table: ${error.message}`);
  }
  
  console.log('✓ gamification_logs table created or already exists');
}

/**
 * Create analytics views for easier querying
 */
async function createStatsViews() {
  console.log('Creating analytics views...');
  
  const { error } = await supabase.rpc('execute_sql', {
    sql: `
      -- User achievements summary view
      DROP VIEW IF EXISTS user_achievements_summary;
      CREATE VIEW user_achievements_summary AS
      SELECT 
        ua.user_id,
        COUNT(DISTINCT ua.achievement_id) as achievements_count,
        SUM(a.points) as total_achievement_points,
        MAX(ua.earned_at) as last_achievement_at
      FROM 
        user_achievements ua
      JOIN 
        achievements a ON ua.achievement_id = a.id
      GROUP BY 
        ua.user_id;
        
      -- Leaderboard view
      DROP VIEW IF EXISTS gamification_leaderboard;
      CREATE VIEW gamification_leaderboard AS
      SELECT 
        p.id as user_id,
        p.points as total_points,
        p.level,
        COALESCE(uas.achievements_count, 0) as achievements_count,
        COALESCE(ua.display_name, ua.username, 'User-' || LEFT(p.id::text, 6)) as display_name,
        RANK() OVER (ORDER BY p.points DESC) as rank
      FROM 
        user_gamification_profiles p
      LEFT JOIN 
        user_achievements_summary uas ON p.id = uas.user_id
      LEFT JOIN 
        auth.users ua ON p.id = ua.id
      ORDER BY 
        rank ASC;
        
      -- Achievement stats view
      DROP VIEW IF EXISTS achievement_stats;
      CREATE VIEW achievement_stats AS
      SELECT 
        a.id as achievement_id,
        a.name,
        a.description,
        a.icon,
        a.points,
        a.category,
        COUNT(ua.id) as earned_count,
        a.is_active
      FROM 
        achievements a
      LEFT JOIN 
        user_achievements ua ON a.id = ua.achievement_id
      GROUP BY 
        a.id
      ORDER BY 
        earned_count DESC;
        
      -- User engagement view
      DROP VIEW IF EXISTS user_engagement_stats;
      CREATE VIEW user_engagement_stats AS
      SELECT
        COUNT(DISTINCT u.id) as total_users,
        COUNT(DISTINCT CASE WHEN e.occurred_at > NOW() - INTERVAL '30 days' THEN e.user_id END) as active_users,
        COALESCE(AVG(p.points), 0) as average_points,
        COALESCE(AVG(CASE WHEN uas.achievements_count IS NOT NULL THEN uas.achievements_count ELSE 0 END), 0) as average_achievements
      FROM
        auth.users u
      LEFT JOIN
        user_gamification_profiles p ON u.id = p.id
      LEFT JOIN
        user_achievements_summary uas ON u.id = uas.user_id
      LEFT JOIN
        gamification_events e ON u.id = e.user_id;
    `
  });
  
  if (error) {
    throw new Error(`Failed to create analytics views: ${error.message}`);
  }
  
  console.log('✓ Analytics views created successfully');
}

/**
 * Setup Row Level Security (RLS) policies for admin tables
 */
async function setupRlsPolicies() {
  console.log('Setting up Row Level Security (RLS) policies for admin tables...');
  
  // Define SQL for enabling RLS and creating policies
  const rlsSql = `
    -- Enable RLS on tables
    ALTER TABLE gamification_stats ENABLE ROW LEVEL SECURITY;
    ALTER TABLE points_history ENABLE ROW LEVEL SECURITY;
    ALTER TABLE event_type_analytics ENABLE ROW LEVEL SECURITY;
    ALTER TABLE gamification_logs ENABLE ROW LEVEL SECURITY;
    
    -- Policy for gamification_stats
    DROP POLICY IF EXISTS gamification_stats_select_policy ON gamification_stats;
    CREATE POLICY gamification_stats_select_policy ON gamification_stats
      FOR SELECT USING (
        EXISTS (
          SELECT 1 FROM user_roles 
          WHERE user_id = auth.uid() AND role IN ('admin', 'analyst')
        )
      ); -- Only admins and analysts can view stats
    
    DROP POLICY IF EXISTS gamification_stats_insert_update_delete_policy ON gamification_stats;
    CREATE POLICY gamification_stats_insert_update_delete_policy ON gamification_stats
      FOR ALL USING (
        EXISTS (
          SELECT 1 FROM user_roles 
          WHERE user_id = auth.uid() AND role = 'admin'
        )
      ); -- Only admins can modify stats
    
    -- Policy for points_history
    DROP POLICY IF EXISTS points_history_select_policy ON points_history;
    CREATE POLICY points_history_select_policy ON points_history
      FOR SELECT USING (
        auth.uid() = user_id OR 
        EXISTS (
          SELECT 1 FROM user_roles 
          WHERE user_id = auth.uid() AND role IN ('admin', 'analyst')
        )
      ); -- Users can view their own history, admins and analysts can view all
    
    DROP POLICY IF EXISTS points_history_insert_policy ON points_history;
    CREATE POLICY points_history_insert_policy ON points_history
      FOR INSERT WITH CHECK (
        EXISTS (
          SELECT 1 FROM user_roles 
          WHERE user_id = auth.uid() AND role = 'admin'
        )
      ); -- Only admins can add points history entries
    
    -- Policy for event_type_analytics
    DROP POLICY IF EXISTS event_type_analytics_select_policy ON event_type_analytics;
    CREATE POLICY event_type_analytics_select_policy ON event_type_analytics
      FOR SELECT USING (
        EXISTS (
          SELECT 1 FROM user_roles 
          WHERE user_id = auth.uid() AND role IN ('admin', 'analyst')
        )
      ); -- Only admins and analysts can view analytics
    
    DROP POLICY IF EXISTS event_type_analytics_insert_update_delete_policy ON event_type_analytics;
    CREATE POLICY event_type_analytics_insert_update_delete_policy ON event_type_analytics
      FOR ALL USING (
        EXISTS (
          SELECT 1 FROM user_roles 
          WHERE user_id = auth.uid() AND role = 'admin'
        )
      ); -- Only admins can modify analytics
    
    -- Policy for gamification_logs
    DROP POLICY IF EXISTS gamification_logs_select_policy ON gamification_logs;
    CREATE POLICY gamification_logs_select_policy ON gamification_logs
      FOR SELECT USING (
        EXISTS (
          SELECT 1 FROM user_roles 
          WHERE user_id = auth.uid() AND role = 'admin'
        )
      ); -- Only admins can view logs
    
    DROP POLICY IF EXISTS gamification_logs_insert_policy ON gamification_logs;
    CREATE POLICY gamification_logs_insert_policy ON gamification_logs
      FOR INSERT WITH CHECK (
        EXISTS (
          SELECT 1 FROM user_roles 
          WHERE user_id = auth.uid() AND role = 'admin'
        ) OR
        auth.uid() = user_id
      ); -- Admins can add any logs, users can add logs about themselves
  `;
  
  // Execute the SQL
  const { error } = await supabase.rpc('execute_sql', { sql: rlsSql });
  
  if (error) {
    console.warn('Warning: Unable to set up RLS policies. This might be due to permissions or the user_roles table not existing.');
    console.warn(error.message);
    console.log('Continuing without setting up RLS policies. You may need to set them up manually.');
    return;
  }
  
  console.log('✓ RLS policies for admin tables created successfully');
}

// Run the setup
setupGamificationAdminTables().catch(err => {
  console.error('Fatal error:', err);
  process.exit(1);
});
