#!/usr/bin/env node

/**
 * This script verifies the department tables in Supabase
 * It checks if the tables exist and logs their status
 * 
 * Usage: node src/commands/verify-department-tables.js
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get Supabase URL and key from environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Supabase URL and key must be set in environment variables');
  console.error('Make sure you have a .env file with VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

async function verifyDepartmentTables() {
  console.log('Verifying department tables in Supabase...');
  
  try {
    // Check if general_departments table exists
    console.log('Checking general_departments table...');
    const { data: generalDepts, error: generalDeptsError } = await supabase
      .from('general_departments')
      .select('id, name, category')
      .limit(5);
      
    if (generalDeptsError) {
      console.error('Error accessing general_departments table:', generalDeptsError);
      console.log('The general_departments table may not exist or you may not have access to it.');
    } else {
      console.log(`Found ${generalDepts.length} general departments.`);
      if (generalDepts.length > 0) {
        console.log('Sample general departments:');
        generalDepts.forEach(dept => {
          console.log(`- ${dept.name} (${dept.category || 'No category'})`);
        });
      } else {
        console.log('The general_departments table exists but is empty.');
      }
    }
    
    // Check if departments table exists
    console.log('\nChecking departments table...');
    const { data: depts, error: deptsError } = await supabase
      .from('departments')
      .select('id, name, general_department_id')
      .limit(5);
      
    if (deptsError) {
      console.error('Error accessing departments table:', deptsError);
      console.log('The departments table may not exist or you may not have access to it.');
    } else {
      console.log(`Found ${depts.length} departments.`);
      if (depts.length > 0) {
        console.log('Sample departments:');
        depts.forEach(dept => {
          console.log(`- ${dept.name} (General Dept ID: ${dept.general_department_id})`);
        });
      } else {
        console.log('The departments table exists but is empty.');
      }
    }
    
    // Check if profiles table has department_id column
    console.log('\nChecking profiles table for department_id column...');
    try {
      const { data: profiles, error: profilesError } = await supabase
        .from('profiles')
        .select('id, department_id')
        .limit(1);
        
      if (profilesError) {
        console.error('Error accessing profiles table:', profilesError);
      } else {
        console.log('The profiles table has a department_id column.');
      }
    } catch (error) {
      console.error('Error checking profiles table:', error);
    }
    
    console.log('\nDepartment tables verification complete!');
  } catch (error) {
    console.error('Error verifying department tables:', error);
  }
}

// Run the verification
verifyDepartmentTables();
