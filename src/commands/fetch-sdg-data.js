// Command line script to fetch and process SDG data
import dotenv from 'dotenv';
import fetch from 'node-fetch';

// Load environment variables
dotenv.config({ path: '.env.local' });

const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const EDGE_FUNCTION_SECRET = process.env.VITE_EDGE_FUNCTION_SECRET || 'default-dev-secret';

// Get command line arguments
const args = process.argv.slice(2);
const action = args[0] || 'all';
const additionalParams = {};

// Parse additional parameters
args.slice(1).forEach(arg => {
  if (arg === '--test-only') {
    additionalParams.testOnly = true;
  } else if (arg.includes('=')) {
    const [key, value] = arg.split('=');
    additionalParams[key] = value;
  }
});

// Test the Edge Function with a simple request
async function testEdgeFunction() {
  try {
    console.log('Testing SDG ETL Edge Function with a simple request...');
    
    const edgeFunctionUrl = `${SUPABASE_URL}/functions/v1/sdg-etl`;
    
    const response = await fetch(edgeFunctionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${EDGE_FUNCTION_SECRET}`
      },
      body: JSON.stringify({ action: 'test' })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Edge Function test failed with status ${response.status}: ${errorText}`);
    }
    
    console.log('✅ Edge Function connection test successful!');
    return true;
  } catch (error) {
    console.error('❌ Error testing Edge Function:', error.message);
    return false;
  }
}

// Perform SDG ETL operation
async function performSdgEtl() {
  try {
    console.log(`Fetching SDG data for action: ${action}`);
    
    // Build request parameters
    const params = {
      action,
      ...additionalParams
    };
    
    // Print request details
    console.log('Request parameters:', params);
    
    // Call Edge Function
    const edgeFunctionUrl = `${SUPABASE_URL}/functions/v1/sdg-etl`;
    
    const response = await fetch(edgeFunctionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${EDGE_FUNCTION_SECRET}`
      },
      body: JSON.stringify(params)
    });
    
    // Handle response
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Edge Function failed with status ${response.status}: ${errorText}`);
    }
    
    const result = await response.json();
    
    // Output result
    console.log('SDG ETL completed successfully:');
    console.log(JSON.stringify(result, null, 2));
    
    return result;
  } catch (error) {
    console.error('Error performing SDG ETL operation:', error.message);
    process.exit(1);
  }
}

// Display usage help
function showHelp() {
  console.log(`
SDG Data Fetching Tool
======================

Usage: node fetch-sdg-data.js [action] [params]

Actions:
  - all           Fetch goals, targets, and indicators
  - goals         Fetch SDG goals
  - targets       Fetch SDG targets
  - indicators    Fetch SDG indicators
  - observations  Fetch SDG observations (requires seriesCode and geoCode)
  - rankings      Calculate rankings (optional: indicatorCode, year)

Parameters:
  - lang=en|ar                Language (default: en)
  - includeMetadata=true|false  Include metadata (default: true)
  - seriesCode=XXX           Required for observations
  - geoCode=XXX              Required for observations (e.g., SAU for Saudi Arabia)
  - indicatorCode=XXX        Optional for rankings (specific indicator)
  - year=XXXX                Optional for rankings (default: current year - 1)
  - calculateBenchmarks=true|false  Calculate benchmarks (default: false)

Examples:
  node fetch-sdg-data.js goals
  node fetch-sdg-data.js indicators lang=ar
  node fetch-sdg-data.js observations seriesCode=SI_POV_DAY1 geoCode=SAU
  node fetch-sdg-data.js rankings year=2022
  node fetch-sdg-data.js rankings indicatorCode=3.1.1 year=2022
`);
}

// Main execution
if (args.includes('--help') || args.includes('-h')) {
  showHelp();
} else if (additionalParams.testOnly) {
  testEdgeFunction()
    .then(success => {
      if (!success) process.exit(1);
    })
    .catch(error => {
      console.error('Error:', error.message);
      process.exit(1);
    });
} else {
  performSdgEtl()
    .then(() => {
      console.log('SDG data processing completed.');
    })
    .catch(error => {
      console.error('Error:', error.message);
      process.exit(1);
    });
}
