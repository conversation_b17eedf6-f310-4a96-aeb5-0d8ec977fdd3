// <PERSON>ript to create SDG tables in Supabase
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('❌ Missing required environment variables.');
  console.error('Please ensure VITE_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in .env.local');
  process.exit(1);
}

// Initialize Supabase client with service role key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

// Function to execute SQL
async function executeSql(sql) {
  try {
    const { error } = await supabase.rpc('exec_sql', { sql_query: sql });
    if (error) {
      console.error(`❌ Error executing SQL: ${error.message}`);
      return false;
    }
    return true;
  } catch (err) {
    console.error(`❌ Exception executing SQL: ${err.message}`);
    return false;
  }
}

// SQL to create tables
const createTablesSql = `
-- SDG Goals table
CREATE TABLE IF NOT EXISTS sdg_goals (
  id SERIAL PRIMARY KEY,
  code VARCHAR(10) NOT NULL UNIQUE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  color VARCHAR(20),
  icon VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- SDG Targets table
CREATE TABLE IF NOT EXISTS sdg_targets (
  id SERIAL PRIMARY KEY,
  code VARCHAR(20) NOT NULL UNIQUE,
  description TEXT NOT NULL,
  goal_code VARCHAR(10) NOT NULL REFERENCES sdg_goals(code),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- SDG Indicators table
CREATE TABLE IF NOT EXISTS sdg_indicators (
  id SERIAL PRIMARY KEY,
  code VARCHAR(30) NOT NULL UNIQUE,
  title TEXT NOT NULL,
  description TEXT,
  goal_code VARCHAR(10) NOT NULL REFERENCES sdg_goals(code),
  target_code VARCHAR(20) NOT NULL REFERENCES sdg_targets(code),
  series_code VARCHAR(50),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- SDG Observations table
CREATE TABLE IF NOT EXISTS sdg_observations (
  id SERIAL PRIMARY KEY,
  indicator_code VARCHAR(30) NOT NULL REFERENCES sdg_indicators(code),
  geo_code VARCHAR(10) NOT NULL, 
  year INTEGER NOT NULL,
  value NUMERIC,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(indicator_code, geo_code, year)
);

-- SDG Rankings table
CREATE TABLE IF NOT EXISTS sdg_rankings (
  id SERIAL PRIMARY KEY,
  indicator_code VARCHAR(30) NOT NULL REFERENCES sdg_indicators(code),
  year INTEGER NOT NULL,
  saudi_rank INTEGER,
  total_countries INTEGER,
  percentile NUMERIC,
  best_country VARCHAR(10),
  best_value NUMERIC,
  worst_country VARCHAR(10),
  worst_value NUMERIC,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(indicator_code, year)
);
`;

// Insert sample data for Goal 3 (Good Health and Well-being)
const insertSampleDataSql = `
-- Insert Goal 3
INSERT INTO sdg_goals (code, title, description, color)
VALUES ('3', 'Good Health and Well-being', 'Ensure healthy lives and promote well-being for all at all ages', '#4C9F38')
ON CONFLICT (code) DO NOTHING;

-- Insert targets for Goal 3
INSERT INTO sdg_targets (code, description, goal_code)
VALUES
  ('3.1', 'By 2030, reduce the global maternal mortality ratio to less than 70 per 100,000 live births', '3'),
  ('3.2', 'By 2030, end preventable deaths of newborns and children under 5 years of age', '3'),
  ('3.3', 'By 2030, end the epidemics of AIDS, tuberculosis, malaria and neglected tropical diseases and combat hepatitis', '3')
ON CONFLICT (code) DO NOTHING;

-- Insert indicators for Goal 3
INSERT INTO sdg_indicators (code, title, goal_code, target_code, series_code)
VALUES
  ('3.1.1', 'Maternal mortality ratio', '3', '3.1', 'SH_MMR_RISK'),
  ('3.1.2', 'Proportion of births attended by skilled health personnel', '3', '3.1', 'SH_STA_BRTATT'),
  ('3.2.1', 'Under-five mortality rate', '3', '3.2', 'SH_DYN_MORT'),
  ('3.3.1', 'Number of new HIV infections per 1,000 uninfected', '3', '3.3', 'SH_HIV_INCD')
ON CONFLICT (code) DO NOTHING;

-- Insert sample observations (with realistic values)
INSERT INTO sdg_observations (indicator_code, geo_code, year, value)
VALUES
  -- Saudi Arabia maternal mortality ratio (per 100,000 live births)
  ('3.1.1', 'SAU', 2020, 14.5),
  ('3.1.1', 'SAU', 2021, 13.2),
  ('3.1.1', 'SAU', 2022, 12.8),
  
  -- Saudi Arabia skilled birth attendance (percentage)
  ('3.1.2', 'SAU', 2020, 98.4),
  ('3.1.2', 'SAU', 2021, 98.7),
  ('3.1.2', 'SAU', 2022, 99.1),
  
  -- Saudi Arabia under-5 mortality (per 1,000 live births)
  ('3.2.1', 'SAU', 2020, 7.8),
  ('3.2.1', 'SAU', 2021, 7.5),
  ('3.2.1', 'SAU', 2022, 7.3),
  
  -- Saudi Arabia HIV incidence (per 1,000 uninfected population)
  ('3.3.1', 'SAU', 2020, 0.04),
  ('3.3.1', 'SAU', 2021, 0.03),
  ('3.3.1', 'SAU', 2022, 0.03)
ON CONFLICT (indicator_code, geo_code, year) DO NOTHING;

-- Insert sample rankings
INSERT INTO sdg_rankings (indicator_code, year, saudi_rank, total_countries, percentile, best_country, best_value, worst_country, worst_value)
VALUES
  ('3.1.1', 2022, 24, 183, 86.5, 'NOR', 4.2, 'SSD', 1150.0),
  ('3.1.2', 2022, 15, 187, 93.2, 'DEU', 100.0, 'SSD', 16.5),
  ('3.2.1', 2022, 30, 193, 84.7, 'FIN', 2.1, 'SSD', 135.0),
  ('3.3.1', 2022, 42, 175, 75.5, 'DNK', 0.01, 'LSO', 12.7)
ON CONFLICT (indicator_code, year) DO NOTHING;
`;

// Main function to create tables and insert sample data
async function setupSDGTables() {
  console.log('Setting up SDG tables in Supabase...');
  
  // Create tables
  console.log('Creating SDG tables...');
  const tablesCreated = await executeSql(createTablesSql);
  if (!tablesCreated) {
    console.error('❌ Failed to create SDG tables.');
    return;
  }
  console.log('✅ SDG tables created successfully.');
  
  // Insert sample data
  console.log('Inserting sample SDG data...');
  const dataInserted = await executeSql(insertSampleDataSql);
  if (!dataInserted) {
    console.error('❌ Failed to insert sample SDG data.');
    return;
  }
  console.log('✅ Sample SDG data inserted successfully.');
  
  // Count records in each table to verify
  try {
    const counts = await Promise.all([
      supabase.from('sdg_goals').select('count', { count: 'exact' }),
      supabase.from('sdg_targets').select('count', { count: 'exact' }),
      supabase.from('sdg_indicators').select('count', { count: 'exact' }),
      supabase.from('sdg_observations').select('count', { count: 'exact' }),
      supabase.from('sdg_rankings').select('count', { count: 'exact' })
    ]);
    
    console.log('\nTable record counts:');
    console.log(`- sdg_goals: ${counts[0].count} goals`);
    console.log(`- sdg_targets: ${counts[1].count} targets`);
    console.log(`- sdg_indicators: ${counts[2].count} indicators`);
    console.log(`- sdg_observations: ${counts[3].count} observations`);
    console.log(`- sdg_rankings: ${counts[4].count} rankings`);
    
    console.log('\n🎉 SDG database setup completed successfully!');
    console.log('You can now refresh the SDG Dashboard to see the populated data.');
  } catch (error) {
    console.error('❌ Error verifying table counts:', error.message);
  }
}

// Run setup
setupSDGTables();
