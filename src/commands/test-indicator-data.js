// <PERSON>ript to test fetching specific SDG indicators for Goal 3
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// SDG Helper function (inlined to avoid import issues)
function normalizeIndicatorCode(code) {
  // If code is missing, return empty string
  if (!code) return '';
  
  // Remove any underscores and replace with periods
  return code.replace(/_/g, '.');
}

// Load environment variables
dotenv.config({ path: '.env.local' });

// Required environment variables
const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_KEY = process.env.VITE_SUPABASE_ANON_KEY;

if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('❌ Missing required environment variables');
  console.error('Please ensure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in .env.local');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

// Goal 3 indicator codes we're interested in
const GOAL3_INDICATORS = [
  '3.1.1',
  '3.1.2',
  '3.2.1',
  '3.3.1'
];

async function testIndicatorData() {
  console.log('📊 Testing SDG Goal 3 Indicator Data\n');
  
  try {
    // 1. First check if Goal 3 exists in the database
    console.log('Checking for Goal 3 in database...');
    const { data: goal3, error: goal3Error } = await supabase
      .from('sdg_goals')
      .select('*')
      .eq('goal_code', '3')
      .single();
    
    if (goal3Error) {
      console.error(`❌ Error fetching Goal 3: ${goal3Error.message}`);
    } else if (!goal3) {
      console.log('⚠️ Goal 3 not found in database');
    } else {
      console.log('✅ Found Goal 3:', goal3.title);
    }
    
    // 2. Check for each indicator in Goal 3
    console.log('\nChecking for Goal 3 indicators...');
    
    for (const indicatorCode of GOAL3_INDICATORS) {
      console.log(`\n--- Indicator ${indicatorCode} ---`);
      
      // Try different formats of the indicator code
      const normalizedCode = normalizeIndicatorCode(indicatorCode);
      const alternativeCode = indicatorCode.replace(/\./g, '_');
      
      // Check with original format
      let { data: indicator, error: indicatorError } = await supabase
        .from('sdg_indicators')
        .select('*')
        .eq('indicator_code', indicatorCode)
        .single();
      
      // If not found, try with normalized code
      if (!indicator && !indicatorError) {
        console.log(`Trying alternative format: ${alternativeCode}`);
        
        ({ data: indicator, error: indicatorError } = await supabase
          .from('sdg_indicators')
          .select('*')
          .eq('indicator_code', alternativeCode)
          .single());
      }
      
      if (indicatorError) {
        console.error(`❌ Error fetching indicator ${indicatorCode}: ${indicatorError.message}`);
        continue;
      }
      
      if (!indicator) {
        console.log(`⚠️ Indicator ${indicatorCode} not found in database`);
        continue;
      }
      
      console.log(`✅ Found indicator: ${indicator.description}`);
      
      // 3. Check for observations for this indicator
      console.log('\nChecking observations...');
      const { data: observations, error: observationsError } = await supabase
        .from('sdg_observations')
        .select('*')
        .eq('indicator_code', indicator.indicator_code)
        .eq('geo_code', 'SAU')
        .order('year', { ascending: false });
      
      if (observationsError) {
        console.error(`❌ Error fetching observations: ${observationsError.message}`);
      } else if (!observations || observations.length === 0) {
        console.log('⚠️ No observations found for this indicator');
      } else {
        console.log(`✅ Found ${observations.length} observations`);
        console.log('Latest observation:', observations[0]);
      }
      
      // 4. Check for rankings for this indicator
      console.log('\nChecking rankings...');
      const { data: rankings, error: rankingsError } = await supabase
        .from('sdg_rankings')
        .select('*')
        .eq('indicator_code', indicator.indicator_code)
        .eq('geo_code', 'SAU')
        .order('year', { ascending: false });
      
      if (rankingsError) {
        console.error(`❌ Error fetching rankings: ${rankingsError.message}`);
      } else if (!rankings || rankings.length === 0) {
        console.log('⚠️ No rankings found for this indicator');
      } else {
        console.log(`✅ Found ${rankings.length} rankings`);
        console.log('Latest ranking:', rankings[0]);
      }
    }
    
    console.log('\n✅ Test completed');
  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
  }
}

// Run the test
testIndicatorData();
