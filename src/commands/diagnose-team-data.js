/**
 * Diagnostic script to check teams and team members in the database
 * Run with:
 *   node src/commands/diagnose-team-data.js <iteration-id>
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Get parameters
const iterationId = process.argv[2];

if (!iterationId) {
  console.error('Please provide iteration ID as an argument');
  console.log('Usage: node src/commands/diagnose-team-data.js <iteration-id>');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

async function main() {
  console.log(`Diagnosing team data for iteration ID: ${iterationId}`);
  
  // Check if iteration exists
  console.log('\nChecking iteration...');
  const { data: iteration, error: iterationError } = await supabase
    .from('product_iterations')
    .select('*')
    .eq('id', iterationId)
    .single();
  
  if (iterationError) {
    console.error('Error fetching iteration:', iterationError);
  } else if (!iteration) {
    console.error('Iteration not found!');
  } else {
    console.log('✅ Iteration found:', {
      id: iteration.id,
      product_base_id: iteration.product_base_id,
      version: iteration.version || iteration.iteration
    });
  }
  
  // Check teams for this iteration
  console.log('\nChecking teams for this iteration...');
  const { data: teams, error: teamsError } = await supabase
    .from('teams')
    .select('*')
    .eq('product_iteration_id', iterationId);
  
  if (teamsError) {
    console.error('Error fetching teams:', teamsError);
  } else if (!teams || teams.length === 0) {
    console.log('❌ No teams found for this iteration!');
    
    // Check if product has any teams
    if (iteration?.product_base_id) {
      console.log(`Checking teams for product ID: ${iteration.product_base_id}...`);
      const { data: productTeams, error: productTeamsError } = await supabase
        .from('teams')
        .select('*')
        .eq('product_base_id', iteration.product_base_id);
      
      if (productTeamsError) {
        console.error('Error fetching product teams:', productTeamsError);
      } else if (!productTeams || productTeams.length === 0) {
        console.log('❌ No teams found for the product either!');
      } else {
        console.log(`✅ Found ${productTeams.length} teams for the product:`, productTeams.map(t => ({ id: t.id, name: t.name })));
      }
    }
    
    // Suggest fix
    console.log('\nSuggested fix: Create a team for this iteration with:');
    console.log(`
    INSERT INTO teams (id, name, product_iteration_id)
    VALUES (gen_random_uuid(), 'Iteration Team', '${iterationId}');
    `);
  } else {
    console.log(`✅ Found ${teams.length} teams for this iteration:`, teams.map(t => ({ id: t.id, name: t.name })));
    
    // Check team members for these teams
    for (const team of teams) {
      console.log(`\nChecking members for team: ${team.id} (${team.name})...`);
      const { data: members, error: membersError } = await supabase
        .from('team_members')
        .select(`
          id,
          person_id,
          role,
          person:persons(id, name, email)
        `)
        .eq('team_id', team.id);
      
      if (membersError) {
        console.error('Error fetching team members:', membersError);
      } else if (!members || members.length === 0) {
        console.log('❌ No members found for this team!');
      } else {
        console.log(`✅ Found ${members.length} team members:`, members.map(m => ({
          id: m.id,
          person_id: m.person_id,
          role: m.role,
          person: m.person ? (Array.isArray(m.person) ? m.person[0] : m.person) : null
        })));
      }
    }
  }
  
  // Check if persons table exists and has entries
  console.log('\nChecking persons table...');
  const { data: persons, error: personsError } = await supabase
    .from('persons')
    .select('id, name, email')
    .limit(5);
  
  if (personsError) {
    console.error('Error fetching persons:', personsError);
  } else if (!persons || persons.length === 0) {
    console.log('❌ No persons found in the database!');
  } else {
    console.log(`✅ Found ${persons.length} persons (showing first 5):`, persons);
  }
  
  // Final assessment
  console.log('\n----- DIAGNOSTIC SUMMARY -----');
  if (!iteration) {
    console.log('❌ CRITICAL: Iteration does not exist - fix your iteration ID');
  } else if (!teams || teams.length === 0) {
    console.log('❌ CRITICAL: No team exists for this iteration - create a team first');
  } else if (teams.some(team => !team.id)) {
    console.log('❌ CRITICAL: Team has invalid ID');
  } else if (!persons || persons.length === 0) {
    console.log('❌ CRITICAL: No persons in database - system has no users to add');
  } else {
    console.log('✅ Basic prerequisites seem to be in place');
    const teamWithNoMembers = teams.find(team => {
      const { data: members } = supabase
        .from('team_members')
        .select('id')
        .eq('team_id', team.id)
        .maybeSingle();
      return !members;
    });
    
    if (teamWithNoMembers) {
      console.log('⚠️ WARNING: Team exists but has no members');
    }
  }
}

main().catch(err => {
  console.error('Unhandled error:', err);
}).finally(() => {
  process.exit(0);
});
