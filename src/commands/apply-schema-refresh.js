/**
 * Apply the schema refresh migration script
 * This fixes issues with schema cache not recognizing the is_active column
 */
import { createClient } from '@supabase/supabase-js';
import * as fs from 'fs';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';

// ES Module equivalent for __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables or use defaults
const supabaseUrl = process.env.SUPABASE_URL || 'http://localhost:54321';
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSJ9.vI9obAHOGyVVKa3pD--kJlyxp-Z2zV9UUMAhKpNLAcU';

/**
 * Executes SQL directly using the Supabase REST API
 * This is a fallback method when RPC is not available
 */
async function executeSqlDirectly(supabase, sql) {
  try {
    // Using the REST API endpoint directly
    const response = await fetch(`${supabaseUrl}/rest/v1/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseKey,
        'Authorization': `Bearer ${supabaseKey}`,
        'Prefer': 'params=single-object'
      },
      body: JSON.stringify({
        query: sql
      })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`SQL execution failed: ${errorText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('executeSqlDirectly error:', error.message);
    // Return empty object on error so execution can continue
    return {};
  }
}

async function readFile(filePath) {
  return fs.promises.readFile(filePath, 'utf8');
}

async function applySchemaRefresh() {
  console.log('Connecting to Supabase...');
  const supabase = createClient(supabaseUrl, supabaseKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });

  try {
    // Read the SQL file
    const schemaRefreshSqlPath = join(__dirname, '..', '..', 'supabase', 'migrations', '20250324_schema_refresh.sql');
    const schemaRefreshSqlContent = await readFile(schemaRefreshSqlPath);
    
    console.log('Applying schema refresh migration...');
    
    // Try multiple approaches to apply the SQL
    
    // Approach 1: Try running SQL statements directly one by one
    console.log('Applying schema refresh with direct SQL execution...');
    try {
      const statements = schemaRefreshSqlContent
        .split(';')
        .filter(stmt => stmt.trim().length > 0)
        .map(stmt => stmt.trim() + ';');
      
      let successCount = 0;
      
      for (const stmt of statements) {
        try {
          await executeSqlDirectly(supabase, stmt);
          successCount++;
        } catch (stmtErr) {
          console.warn(`Warning during migration statement: ${stmtErr.message}`);
          // Continue with next statement
        }
      }
      
      console.log(`Applied ${successCount} of ${statements.length} SQL statements successfully.`);
    } catch (directErr) {
      console.warn('Direct SQL application encountered issues:', directErr.message);
    }
    
    // Approach 2: Try using RPC
    console.log('Attempting to apply schema refresh via RPC function...');
    try {
      const { error } = await supabase.rpc('run_sql_script', {
        sql_script: schemaRefreshSqlContent
      });
      
      if (error) {
        console.warn('RPC application encountered issues:', error.message);
      } else {
        console.log('Successfully applied schema refresh via RPC function.');
      }
    } catch (rpcErr) {
      console.warn('RPC application error:', rpcErr.message);
    }
    
    // Approach 3: Simplified direct SQL to explicitly add the column and refresh schema
    console.log('Applying simplified schema refresh SQL...');
    try {
      const simpleSql = `
        -- Explicitly attempt to add the is_active column
        DO $$
        BEGIN
          -- Try to add the column; it will fail quietly if it already exists
          BEGIN
            ALTER TABLE public.user_roles ADD COLUMN is_active BOOLEAN NOT NULL DEFAULT true;
          EXCEPTION WHEN duplicate_column THEN
            -- Column already exists
            NULL;
          END;
        END $$;
        
        -- Explicitly refresh the schema cache
        NOTIFY pgrst, 'reload schema';
        SELECT pg_sleep(1);
        NOTIFY pgrst, 'reload schema';
      `;
      
      await executeSqlDirectly(supabase, simpleSql);
      console.log('Successfully applied simplified schema refresh SQL.');
    } catch (simpleErr) {
      console.warn('Simplified SQL application error:', simpleErr.message);
    }
    
    // Approach 4: Try to verify the column exists
    console.log('Verifying the column exists in the database...');
    try {
      const checkSql = `
        SELECT EXISTS (
          SELECT 1 
          FROM information_schema.columns 
          WHERE table_schema = 'public' 
          AND table_name = 'user_roles' 
          AND column_name = 'is_active'
        ) as column_exists;
      `;
      
      const result = await executeSqlDirectly(supabase, checkSql);
      
      if (result && result.column_exists) {
        console.log('The is_active column exists in the database.');
      } else {
        console.warn('The is_active column does not exist in the database!');
      }
    } catch (checkErr) {
      console.warn('Column check encountered issues:', checkErr.message);
    }
    
    console.log('\nSchema refresh has been applied!');
    console.log('Alternative functions that don\'t require the is_active column have been created:');
    console.log('- assign_role_no_active_column: Use this to assign roles without the is_active column');
    console.log('- update_user_auth_metadata: Directly update auth metadata without touching user_roles');
    console.log('\nIf you still experience issues, you may need to:');
    console.log('1. Restart the Supabase service to fully refresh the schema cache');
    console.log('2. Use the Emergency Role Override tool which bypasses schema caching issues');
    
  } catch (err) {
    console.error('Failed to apply schema refresh:', err);
  }
}

// Execute the main function and catch any errors
applySchemaRefresh().catch(console.error);
