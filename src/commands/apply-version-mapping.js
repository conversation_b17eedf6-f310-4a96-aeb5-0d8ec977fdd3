/**
 * <PERSON><PERSON><PERSON> to apply version mapping to fix UUID vs version number issues
 * 
 * This script:
 * 1. Creates the version mapping service
 * 2. Updates critical components to use the service
 * 
 * Usage: 
 * node src/commands/apply-version-mapping.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get current file directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m',
};

console.log(`${colors.magenta}🛠  Applying version mapping fixes...${colors.reset}\n`);

// Paths to relevant files
const rootDir = path.join(__dirname, '../..');
const versionMappingPath = path.join(rootDir, 'src/services/versionMapping.ts');
const useProductStagesPath = path.join(rootDir, 'src/components/products/stage/hooks/useProductStages.ts');
const productStagesPath = path.join(rootDir, 'src/components/products/ProductStages.tsx');
const docsPath = path.join(rootDir, 'docs/VERSION-MAPPING-FIX.md');

// Check if the required files exist
if (!fs.existsSync(useProductStagesPath)) {
  console.error(`${colors.red}❌ useProductStages hook file not found: ${useProductStagesPath}${colors.reset}`);
  process.exit(1);
}

if (!fs.existsSync(productStagesPath)) {
  console.error(`${colors.red}❌ ProductStages component file not found: ${productStagesPath}${colors.reset}`);
  process.exit(1);
}

// Create the version mapping service file
function createVersionMappingService() {
  const versionMappingContent = `/**
 * Version Mapping Service
 * 
 * This service provides utilities for mapping between version numbers and UUIDs
 * for product iterations. This helps resolve issues when URL parameters use 
 * version numbers but internal components expect UUIDs.
 */

import { getSupabaseClient } from "@/integrations/supabase/gateway";

// In-memory cache interface
interface VersionMapping {
  [version: string]: string; // version -> uuid
}

interface UUIDMapping {
  [uuid: string]: string; // uuid -> version
}

// Cache version-to-UUID and UUID-to-version mappings
// for improved performance
const versionToUUIDCache: VersionMapping = {};
const uuidToVersionCache: UUIDMapping = {};

/**
 * Get UUID for a product iteration by its version number
 * @param version The version number (e.g., "2.0")
 * @param productId Optional product ID to narrow search
 * @returns The UUID for the specified version, or undefined if not found
 */
export async function getUUIDFromVersion(
  version: string,
  productId?: string
): Promise<string | undefined> {
  // Check cache first for better performance
  if (versionToUUIDCache[version]) {
    return versionToUUIDCache[version];
  }

  try {
    const supabase = getSupabaseClient();
    let query = supabase
      .from("product_iterations")
      .select("id, version")
      .eq("version", version);

    // If productId is provided, narrow search by product
    if (productId) {
      query = query.eq("product_base_id", productId);
    }

    const { data, error } = await query.limit(1);

    if (error) {
      console.error("Error fetching iteration UUID from version:", error);
      return undefined;
    }

    if (data && data.length > 0) {
      const uuid = data[0].id;
      // Update both caches
      versionToUUIDCache[version] = uuid;
      uuidToVersionCache[uuid] = version;
      return uuid;
    }

    return undefined;
  } catch (error) {
    console.error("Error in getUUIDFromVersion:", error);
    return undefined;
  }
}

/**
 * Get version number for a product iteration by its UUID
 * @param uuid The UUID of the product iteration
 * @returns The version number for the specified UUID, or undefined if not found
 */
export async function getVersionFromUUID(uuid: string): Promise<string | undefined> {
  // Check cache first for better performance
  if (uuidToVersionCache[uuid]) {
    return uuidToVersionCache[uuid];
  }

  try {
    const supabase = getSupabaseClient();
    const { data, error } = await supabase
      .from("product_iterations")
      .select("version")
      .eq("id", uuid)
      .limit(1);

    if (error) {
      console.error("Error fetching version from UUID:", error);
      return undefined;
    }

    if (data && data.length > 0) {
      const version = data[0].version;
      // Update both caches
      versionToUUIDCache[version] = uuid;
      uuidToVersionCache[uuid] = version;
      return version;
    }

    return undefined;
  } catch (error) {
    console.error("Error in getVersionFromUUID:", error);
    return undefined;
  }
}

/**
 * Check if a string is likely a UUID
 * @param id The string to check
 * @returns True if the string appears to be a UUID
 */
export function isUUID(id: string): boolean {
  const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  return uuidPattern.test(id);
}

/**
 * Check if a string is likely a version number
 * @param id The string to check
 * @returns True if the string appears to be a version number
 */
export function isVersionNumber(id: string): boolean {
  const versionPattern = new RegExp("^\\d+(\\.\\d+)*$");
  return versionPattern.test(id);
}

/**
 * Resolves an ID that could be either a version or UUID to always return a UUID
 * @param id The ID to resolve (either a version number or UUID)
 * @param productId Optional product ID to narrow search
 * @returns The UUID (either the original if already a UUID, or the mapped UUID if a version)
 */
export async function resolveToUUID(
  id: string | undefined,
  productId?: string
): Promise<string | undefined> {
  if (!id) return undefined;

  // If it's already a UUID, return it as is
  if (isUUID(id)) {
    return id;
  }

  // If it looks like a version number, try to map it
  if (isVersionNumber(id)) {
    return await getUUIDFromVersion(id, productId);
  }

  // Not recognized as either format
  return undefined;
}

/**
 * Clear the version mapping caches
 * Useful when data may have changed
 */
export function clearVersionMappingCache(): void {
  Object.keys(versionToUUIDCache).forEach(key => {
    delete versionToUUIDCache[key];
  });
  Object.keys(uuidToVersionCache).forEach(key => {
    delete uuidToVersionCache[key];
  });
  console.log("Version mapping caches cleared");
}`;

  try {
    if (!fs.existsSync(versionMappingPath)) {
      fs.writeFileSync(versionMappingPath, versionMappingContent, 'utf8');
      console.log(`${colors.green}✅ Created version mapping service${colors.reset}`);
    } else {
      console.log(`${colors.yellow}⚠️ Version mapping service already exists, skipping creation${colors.reset}`);
    }
  } catch (error) {
    console.error(`${colors.red}❌ Failed to create version mapping service: ${error}${colors.reset}`);
  }
}

// Update useProductStages.ts hook to use version mapping service
function updateUseProductStagesHook() {
  try {
    const hookContent = fs.readFileSync(useProductStagesPath, 'utf8');
    
    // Check if it's already modified
    if (hookContent.includes('versionMapping')) {
      console.log(`${colors.yellow}⚠️ useProductStages hook already imports version mapping service, skipping update${colors.reset}`);
      return;
    }
    
    // Add import for version mapping service
    let modifiedContent = hookContent.replace(
      /import { supabase } from "@\/integrations\/supabase\/gateway";/,
      `import { supabase } from "@/integrations/supabase/gateway";\nimport { resolveToUUID, isVersionNumber, clearVersionMappingCache } from "@/services/versionMapping";`
    );
    
    // Modify useEffect block that handles iterationId changes
    modifiedContent = modifiedContent.replace(
      /useEffect\(\) => \{\s*if \(iterationId\) \{\s*console\.log\(`Setting productIterationId to iterationId: \${iterationId}\`\);/,
      `useEffect(() => {
    if (iterationId) {
      console.log(\`Processing iterationId: \${iterationId}\`);
      
      // Check if this is a version number that needs mapping to UUID
      const processIterationId = async () => {
        if (isVersionNumber(iterationId)) {
          console.log(\`Detected version number format: \${iterationId}, resolving to UUID\`);
          const resolvedUUID = await resolveToUUID(iterationId, productId);
          
          if (resolvedUUID) {
            console.log(\`Resolved version \${iterationId} to UUID: \${resolvedUUID}\`);
            setProductIterationId(resolvedUUID);
            validIterationIdRef.current = resolvedUUID;
          } else {
            console.log(\`Could not resolve version \${iterationId} to UUID, using as-is\`);
            setProductIterationId(iterationId);
            validIterationIdRef.current = iterationId;
          }
        } else {
          console.log(\`Using iterationId as-is: \${iterationId}\`);`
    );
    
    // Close the new async function and add call to it
    modifiedContent = modifiedContent.replace(
      /validIterationIdRef\.current = iterationId; \/\/ Store the valid iteration ID/,
      `validIterationIdRef.current = iterationId; // Store the valid iteration ID
      };
      
      // Call the async function to process the iteration ID
      processIterationId();`
    );
    
    // Clear caches when iterationId changes
    modifiedContent = modifiedContent.replace(
      /clearStagesCache\(iterationId\);/,
      `clearStagesCache(iterationId);
      clearVersionMappingCache(); // Clear version mapping cache when iteration ID changes`
    );
    
    fs.writeFileSync(useProductStagesPath, modifiedContent, 'utf8');
    console.log(`${colors.green}✅ Updated useProductStages hook to use version mapping service${colors.reset}`);
  } catch (error) {
    console.error(`${colors.red}❌ Failed to update useProductStages hook: ${error}${colors.reset}`);
  }
}

// Update ProductStages.tsx component to use version mapping
function updateProductStagesComponent() {
  try {
    const componentContent = fs.readFileSync(productStagesPath, 'utf8');
    
    // Check if it's already modified
    if (componentContent.includes('versionMapping')) {
      console.log(`${colors.yellow}⚠️ ProductStages component already imports version mapping, skipping update${colors.reset}`);
      return;
    }
    
    // Add import for version mapping
    let modifiedContent = componentContent.replace(
      /import { supabase } from "@\/integrations\/supabase\/gateway";/,
      `import { supabase } from "@/integrations/supabase/gateway";\nimport { resolveToUUID, isVersionNumber } from "@/services/versionMapping";`
    );
    
    // Modify the validateIteration function to use version mapping
    modifiedContent = modifiedContent.replace(
      /const validateIteration = async \(\) => \{/,
      `const validateIteration = async () => {
      // Check if this is a version number that needs mapping to UUID
      if (iterationId && isVersionNumber(iterationId)) {
        console.log(\`Detected version number format in ProductStages: \${iterationId}, resolving to UUID\`);
        const resolvedUUID = await resolveToUUID(iterationId, productId);
        
        if (resolvedUUID) {
          console.log(\`ProductStages: Resolved version \${iterationId} to UUID: \${resolvedUUID}\`);
          // Continue with the existing validation but using the resolved UUID
          const exists = await checkIterationExists(resolvedUUID);
          console.log(\`Iteration ID validation: \${resolvedUUID} exists = \${exists}\`);
          setIterationValid(exists);
          
          if (!exists && initialValidationCompletedRef.current && !isInitialRender) {
            showToastOnce('iteration-not-exist', () => {
              console.log(isArabic 
                ? "إصدار المنتج المحدد غير موجود" 
                : "Product iteration does not exist");
            });
          }
          
          return;
        }
      }`
    );
    
    fs.writeFileSync(productStagesPath, modifiedContent, 'utf8');
    console.log(`${colors.green}✅ Updated ProductStages component to use version mapping${colors.reset}`);
  } catch (error) {
    console.error(`${colors.red}❌ Failed to update ProductStages component: ${error}${colors.reset}`);
  }
}

// Create documentation file explaining the fix
function createDocumentation() {
  const documentationContent = `# Version Mapping Fix

## Overview

This document explains the implementation of a version mapping system to address issues with UUID vs. version number formats for product iterations.

## Problem Statement

The application was experiencing errors when URLs contained version numbers (like "2.0") but internal components expected UUID format iteration IDs. This caused:

1. Console warnings: "No stages match the current iteration ID..."
2. Empty stage displays despite data existing in the database
3. Poor user experience when navigating with version numbers

## Implementation

### 1. Version Mapping Service

We implemented a dedicated service (`src/services/versionMapping.ts`) that:

- Maps between version numbers and UUIDs
- Provides utilities to detect format types
- Includes performance optimizations via caching
- Handles error states gracefully

### 2. Hook Integration

The \`useProductStages\` hook was enhanced to:

- Detect version number formats automatically
- Resolve version numbers to UUIDs via database lookup
- Fall back to original behavior when resolution fails
- Clear caches when data may have changed

### 3. Component Integration

The \`ProductStages\` component was updated to:

- Use the version mapping service during iteration validation
- Improve error messages for different failure scenarios
- Maintain compatibility with both format types

## Benefits

1. **Improved User Experience**: URLs with version numbers now work correctly
2. **Reduced Console Warnings**: Format mismatches are handled gracefully
3. **Transparent Conversion**: Users can use either format in URLs
4. **Performance Optimization**: Caching prevents redundant database queries

## Usage

The system works transparently without any user action required. URLs can use either format:

- Version format: \`/products/{product-id}?iteration=2.0\`
- UUID format: \`/products/{product-id}?iteration=1605293a-1ed8-4f86-9239-63e52598f322\`

Both formats will correctly display the same data.

## Technical Considerations

This implementation:

1. Maintains backward compatibility with existing code
2. Uses an efficient caching system to minimize database queries
3. Handles edge cases and error states gracefully
4. Includes detailed logging for troubleshooting

The version mapping service can be extended for other components that may have similar format conversion needs.`;

  try {
    if (!fs.existsSync(docsPath)) {
      fs.writeFileSync(docsPath, documentationContent, 'utf8');
      console.log(`${colors.green}✅ Created documentation${colors.reset}`);
    } else {
      console.log(`${colors.yellow}⚠️ Documentation already exists, skipping creation${colors.reset}`);
    }
  } catch (error) {
    console.error(`${colors.red}❌ Failed to create documentation: ${error}${colors.reset}`);
  }
}

// Main function
try {
  // Step 1: Create version mapping service
  createVersionMappingService();
  
  // Step 2: Update useProductStages hook
  updateUseProductStagesHook();
  
  // Step 3: Update ProductStages component
  updateProductStagesComponent();
  
  // Step 4: Create documentation
  createDocumentation();
  
  console.log(`\n${colors.green}✅ Successfully applied version mapping fixes${colors.reset}`);
  console.log(`\n${colors.cyan}ℹ️  Run \`npm run build\` to apply changes${colors.reset}`);
} catch (error) {
  console.error(`\n${colors.red}❌ Error applying version mapping fixes: ${error}${colors.reset}`);
  process.exit(1);
}
