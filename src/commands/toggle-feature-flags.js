/**
 * Simple Feature Flag Toggle Utility
 * 
 * This script provides a direct way to toggle feature flags without
 * going through the full admin UI - useful for quick testing.
 * 
 * Copy this entire script to the browser console and execute it.
 */

// Check current feature flags in localStorage
function checkFlags() {
  try {
    const flags = JSON.parse(localStorage.getItem('feature_flags_cache') || '{}');
    console.log('Current feature flags:');
    console.table(flags);
    return flags;
  } catch (err) {
    console.error('Error reading flags:', err);
    return {};
  }
}

// Toggle a specific flag
function toggleFlag(flagName) {
  try {
    const flags = checkFlags();
    if (flags[flagName] !== undefined) {
      // Toggle the flag
      flags[flagName] = !flags[flagName];
      
      // Save to both localStorage keys for consistency
      localStorage.setItem('feature_flags_cache', JSON.stringify(flags));
      localStorage.setItem('feature_flags_override', JSON.stringify(flags));
      
      console.log(`✅ Toggled ${flagName} to ${flags[flagName]}`);
      console.table(flags);
      
      return `Flag ${flagName} toggled to ${flags[flagName]}. Refresh the page to see changes.`;
    } else {
      return `Flag ${flagName} not found!`;
    }
  } catch (err) {
    console.error('Error toggling flag:', err);
    return 'Error toggling flag. See console for details.';
  }
}

// Enable all flags
function enableAll() {
  try {
    const flags = checkFlags();
    
    // Set all flags to true
    Object.keys(flags).forEach(key => {
      flags[key] = true;
    });
    
    // Save to both localStorage keys
    localStorage.setItem('feature_flags_cache', JSON.stringify(flags));
    localStorage.setItem('feature_flags_override', JSON.stringify(flags));
    
    console.log('✅ All flags enabled!');
    console.table(flags);
    
    return 'All flags enabled. Refresh the page to see changes.';
  } catch (err) {
    console.error('Error enabling flags:', err);
    return 'Error enabling flags. See console for details.';
  }
}

// Disable all flags
function disableAll() {
  try {
    const flags = checkFlags();
    
    // Set all flags to false
    Object.keys(flags).forEach(key => {
      flags[key] = false;
    });
    
    // Save to both localStorage keys
    localStorage.setItem('feature_flags_cache', JSON.stringify(flags));
    localStorage.setItem('feature_flags_override', JSON.stringify(flags));
    
    console.log('❌ All flags disabled!');
    console.table(flags);
    
    return 'All flags disabled. Refresh the page to see changes.';
  } catch (err) {
    console.error('Error disabling flags:', err);
    return 'Error disabling flags. See console for details.';
  }
}

// Reset to defaults
function resetToDefaults() {
  try {
    const defaults = {
      useCustomWorkflowStages: false,
      useSwimlanes: true,
      useWipLimits: false,
      useAgingIndicators: false
    };
    
    // Save to both localStorage keys
    localStorage.setItem('feature_flags_cache', JSON.stringify(defaults));
    localStorage.setItem('feature_flags_override', JSON.stringify(defaults));
    
    console.log('🔄 Flags reset to defaults!');
    console.table(defaults);
    
    return 'Flags reset to defaults. Refresh the page to see changes.';
  } catch (err) {
    console.error('Error resetting flags:', err);
    return 'Error resetting flags. See console for details.';
  }
}

// Create a simple UI for the console
console.log(`
=============================================
Feature Flags Toggle Utility
=============================================

Available commands:

checkFlags() - Show current feature flags
toggleFlag('flagName') - Toggle a specific flag
enableAll() - Enable all feature flags
disableAll() - Disable all feature flags
resetToDefaults() - Reset flags to default values

Example:
> toggleFlag('useSwimlanes')

After using any command, refresh the page to see changes.
`);

// Check current flags
checkFlags();

// Expose functions to the global scope
window.checkFlags = checkFlags;
window.toggleFlag = toggleFlag;
window.enableAll = enableAll;
window.disableAll = disableAll;
window.resetToDefaults = resetToDefaults;
