/**
 * Schema Version Management Script
 * 
 * This script provides utilities for creating and managing database schema versions.
 * Use this when making schema changes to keep track of versions and ensure compatibility.
 * 
 * Usage:
 * - To view current version: node src/commands/manage-schema-versions.js status
 * - To add a new version: node src/commands/manage-schema-versions.js add --version 1.1.0 --description "Added new columns" --component stages=1.1.0
 */

// Import dotenv to load environment variables
require('dotenv').config();

// Import the Supabase client creation function and database config
const { createClient } = require('@supabase/supabase-js');

// Get Supabase connection info from environment variables
const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_KEY = process.env.VITE_SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_KEY;

if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('Error: Missing Supabase environment variables.');
  console.error('Make sure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in your .env file.');
  process.exit(1);
}

// Create a Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

// Schema version table name
const SCHEMA_VERSION_TABLE = 'schema_versions';

// Command-line argument parsing
const args = process.argv.slice(2);
const command = args[0]?.toLowerCase() || 'help';

// Parse command line arguments
function parseArgs(args) {
  const result = { params: {} };
  
  for (let i = 1; i < args.length; i++) {
    if (args[i].startsWith('--')) {
      const paramName = args[i].substring(2);
      
      // Handle component parameters with key=value format
      if (paramName.startsWith('component') && i + 1 < args.length && args[i + 1].includes('=')) {
        if (!result.components) result.components = {};
        
        const [componentName, componentVersion] = args[i + 1].split('=');
        result.components[componentName] = componentVersion;
        i++;
      } 
      // Handle regular parameters
      else if (i + 1 < args.length && !args[i + 1].startsWith('--')) {
        result.params[paramName] = args[i + 1];
        i++;
      } else {
        result.params[paramName] = true;
      }
    }
  }
  
  return result;
}

// Function to display the current schema version
async function showStatus() {
  try {
    console.log('Fetching schema version information...');
    
    // Check if the schema_versions table exists
    const { error: tableCheckError } = await supabase
      .from(SCHEMA_VERSION_TABLE)
      .select('count')
      .limit(1);
    
    if (tableCheckError && tableCheckError.message.includes(`relation "${SCHEMA_VERSION_TABLE}" does not exist`)) {
      console.log('Schema version table does not exist yet.');
      console.log('This is normal if you haven\'t created any schema versions.');
      console.log('Run the "add" command to create your first schema version.');
      return;
    }
    
    // Get the current version
    const { data: currentVersion, error: versionError } = await supabase
      .from(SCHEMA_VERSION_TABLE)
      .select('*')
      .order('applied_at', { ascending: false })
      .limit(1)
      .single();
    
    if (versionError) {
      if (versionError.code === 'PGRST116') {
        console.log('No schema versions found in the database.');
        console.log('Run the "add" command to create your first schema version.');
      } else {
        console.error('Error fetching current schema version:', versionError);
      }
      return;
    }
    
    console.log('\nCurrent Schema Version:');
    console.log('---------------------');
    console.log(`Version:     ${currentVersion.version}`);
    console.log(`Applied At:  ${new Date(currentVersion.applied_at).toLocaleString()}`);
    console.log(`Description: ${currentVersion.description || 'No description'}`);
    console.log(`Required:    ${currentVersion.required ? 'Yes' : 'No'}`);
    
    if (currentVersion.components && Object.keys(currentVersion.components).length > 0) {
      console.log('\nComponent Versions:');
      console.log('------------------');
      Object.entries(currentVersion.components).forEach(([component, version]) => {
        console.log(`${component}: ${version}`);
      });
    }
    
    // Get version history
    const { data: versionHistory, error: historyError } = await supabase
      .from(SCHEMA_VERSION_TABLE)
      .select('version, description, applied_at')
      .order('applied_at', { ascending: false });
    
    if (historyError) {
      console.error('Error fetching version history:', historyError);
      return;
    }
    
    if (versionHistory.length > 1) {
      console.log('\nVersion History:');
      console.log('---------------');
      versionHistory.slice(1).forEach((version, index) => {
        console.log(`${version.version} - ${new Date(version.applied_at).toLocaleString()} - ${version.description || 'No description'}`);
      });
    }
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Function to add a new schema version
async function addVersion(params, components) {
  try {
    if (!params.version) {
      console.error('Error: --version parameter is required');
      console.log('Example: node src/commands/manage-schema-versions.js add --version 1.1.0 --description "Added new columns"');
      return;
    }
    
    console.log(`Adding new schema version: ${params.version}`);
    
    // Check if the schema_versions table exists
    const { error: tableCheckError } = await supabase
      .from(SCHEMA_VERSION_TABLE)
      .select('count')
      .limit(1);
    
    if (tableCheckError && tableCheckError.message.includes(`relation "${SCHEMA_VERSION_TABLE}" does not exist`)) {
      console.log('Schema version table does not exist yet. Creating it now...');
      
      // Create the table
      const createTableSQL = `
        CREATE TABLE IF NOT EXISTS public.${SCHEMA_VERSION_TABLE} (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          version TEXT NOT NULL,
          description TEXT,
          applied_at TIMESTAMPTZ DEFAULT now(),
          required BOOLEAN DEFAULT true,
          components JSONB
        );
        
        -- Create index on version for faster lookups
        CREATE INDEX IF NOT EXISTS schema_versions_version_idx ON public.${SCHEMA_VERSION_TABLE} (version);
      `;
      
      const { error: createError } = await supabase.rpc('exec_sql', { query: createTableSQL });
      
      if (createError) {
        console.error(`Error creating ${SCHEMA_VERSION_TABLE} table:`, createError);
        return;
      }
      
      console.log(`Created ${SCHEMA_VERSION_TABLE} table successfully`);
    }
    
    // Check if version already exists
    const { data: existing, error: checkError } = await supabase
      .from(SCHEMA_VERSION_TABLE)
      .select('id')
      .eq('version', params.version)
      .maybeSingle();
    
    if (checkError) {
      console.error('Error checking for existing version:', checkError);
      return;
    }
    
    if (existing) {
      console.error(`Error: Version ${params.version} already exists in the database`);
      return;
    }
    
    // Insert the new version
    const { error: insertError } = await supabase
      .from(SCHEMA_VERSION_TABLE)
      .insert({
        version: params.version,
        description: params.description || `Schema version ${params.version}`,
        required: params.required !== 'false', // Default to true unless explicitly set to false
        components: components || {}
      });
    
    if (insertError) {
      console.error('Error inserting new schema version:', insertError);
      return;
    }
    
    console.log(`Successfully added schema version ${params.version}`);
    
    // Show the updated status
    await showStatus();
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Function to provide help information
function showHelp() {
  console.log(`
Schema Version Management Script
===============================

This script helps manage database schema versions to track changes and ensure compatibility.

Commands:
  status          Show current schema version and history
  add             Add a new schema version
  help            Show this help message

Options for "add" command:
  --version       Schema version number (required, e.g., "1.1.0")
  --description   Description of the changes
  --required      Whether this version is required (true/false, default: true)
  --component     Set component version (can be used multiple times)
                  Format: --component name=version

Examples:
  # Show current status
  node src/commands/manage-schema-versions.js status

  # Add a new version
  node src/commands/manage-schema-versions.js add --version 1.1.0 --description "Added new columns to stages table"

  # Add with component versions
  node src/commands/manage-schema-versions.js add --version 1.1.0 --description "Updated stages" --component stages=1.1.0 --component indicators=1.0.2
  `);
}

// Main execution flow
async function main() {
  try {
    switch (command) {
      case 'status':
        await showStatus();
        break;
        
      case 'add':
        const { params, components } = parseArgs(args);
        await addVersion(params, components);
        break;
        
      case 'help':
      default:
        showHelp();
        break;
    }
  } catch (error) {
    console.error('Error executing command:', error);
  } finally {
    // Close the script cleanly
    process.exit(0);
  }
}

// Run the main function
main();
