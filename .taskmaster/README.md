# Task Master Integration Guide for statsFactory

Task Master has been successfully initialized for your statsFactory project. This guide will help you get started.

## 🚀 Quick Start

### 1. Configure API Keys
Open `.cursor/mcp.json` and replace the placeholder API keys with your actual keys:
- **Required**: At least one AI provider key (Anthropic, OpenAI, Google, etc.)
- **Recommended**: Perplexity API key for research capabilities

### 2. Enable Task Master in Cursor
1. Open Cursor Settings (Ctrl+Shift+J or Cmd+Shift+J)
2. Click on the MCP tab on the left
3. Find "taskmaster-ai" and enable it with the toggle

### 3. Restart Cursor
After enabling Task Master, restart Cursor to ensure the MCP server loads properly.

## 📋 Common Commands

In Cursor's AI chat, you can use these commands:

### Initialize (already done)
```
Initialize taskmaster-ai in my project
```

### Parse Your PRD
```
Can you parse my PRD at .taskmaster/docs/prd.txt?
```

### View Tasks
```
Show me all tasks
```

### Get Next Task
```
What's the next task I should work on?
```

### Implement a Task
```
Can you help me implement task 3?
```

### Update Task Status
```
Mark task 3 as done
```

### Expand a Task
```
Can you help me expand task 5 into subtasks?
```

### Add New Tasks
```
Add a new task: [description of what needs to be done]
```

## 🔄 Workflow Integration

Task Master is integrated with your memory bank workflow:

1. **Task Generation**: Tasks are generated from your PRD
2. **Context7 Scaffolding**: Each task implementation uses Context7 MCP
3. **Memory Bank Rules**: All work follows memory bank rules
4. **Progress Tracking**: Task status updates are tracked
5. **Documentation**: Progress is logged in memory bank

## 📁 Project Structure

```
.taskmaster/
├── config.json         # Task Master configuration
├── docs/
│   └── prd.txt        # Your Product Requirements Document
├── tasks/             # Generated task files will go here
├── templates/
│   └── example_prd.txt # PRD template for reference
└── README.md          # This file
```

## 🎯 Next Steps

1. **Add your API keys** to `.cursor/mcp.json`
2. **Enable Task Master** in Cursor settings
3. **Parse your PRD** to generate initial tasks
4. **Start implementing** tasks one by one

## 💡 Tips

- Always use Task Master commands in Cursor's AI chat
- Tasks will respect your memory bank rules automatically
- Each task implementation will use Context7 scaffolding
- Update task status as you complete work
- Use the research model for complex tasks

## 🔗 Integration with Memory Bank

Task Master respects your existing workflow:
- Context7 MCP for all new features
- Definition of Done checklist
- Progress tracking in `memory-bank/progress.md`
- Rule compliance from `memory-bank/rules/`

## ❓ Need Help?

If you encounter issues:
1. Check that API keys are correctly configured
2. Ensure Task Master is enabled in Cursor MCP settings
3. Restart Cursor if the MCP server isn't responding
4. Review the Task Master documentation at: https://github.com/eyaltoledano/claude-task-master

Happy coding with Task Master! 🚀
