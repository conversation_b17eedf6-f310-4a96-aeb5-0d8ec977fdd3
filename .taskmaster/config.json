{"models": {"main": {"0": "c", "1": "l", "2": "a", "3": "u", "4": "d", "5": "e", "6": "-", "7": "3", "8": "-", "9": "s", "10": "o", "11": "n", "12": "n", "13": "e", "14": "t", "15": "-", "16": "2", "17": "0", "18": "2", "19": "4", "20": "0", "21": "2", "22": "2", "23": "9", "provider": "anthropic", "modelId": "claude-3-7-sonnet-20250219", "maxTokens": 64000, "temperature": 0.2}, "research": {"0": "p", "1": "e", "2": "r", "3": "p", "4": "l", "5": "e", "6": "x", "7": "i", "8": "t", "9": "y", "10": "-", "11": "s", "12": "o", "13": "n", "14": "a", "15": "r", "16": "-", "17": "m", "18": "e", "19": "d", "20": "i", "21": "u", "22": "m", "provider": "perplexity", "modelId": "sonar-pro", "maxTokens": 8700, "temperature": 0.1}, "fallback": {"provider": "anthropic", "modelId": "claude-3-5-sonnet", "maxTokens": 64000, "temperature": 0.2}}, "global": {"logLevel": "info", "debug": false, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Task Master", "ollamaBaseURL": "http://localhost:11434/api", "bedrockBaseURL": "https://bedrock.us-east-1.amazonaws.com", "userId": "**********"}}