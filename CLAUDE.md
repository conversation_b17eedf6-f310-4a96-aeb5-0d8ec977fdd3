# CLAUDE.md - statsFactory Codebase Guide

## Overview
statsFactory is a comprehensive statistics management application for the General Authority for Statistics. It's built with React, TypeScript, and Supabase, providing tools for managing statistical products, indicators, publications, and surveys with structured workflows and approval processes.

## Key Commands

### Development
- `npm run dev` - Start development server (port 8080)
- `npm run build` - Build for production
- `npm run build:dev` - Build for development
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint for code quality checks
- `npm run verify-departments` - Verify department tables in database

## Architecture Overview

### Tech Stack
- **Frontend**: React 18 with TypeScript
- **State Management**: React Query (TanStack Query)
- **UI Framework**: Tailwind CSS + shadcn/ui components
- **Backend**: Supabase (PostgreSQL + Auth + Edge Functions)
- **Build Tool**: Vite
- **Routing**: React Router v6
- **Forms**: React Hook Form + Zod validation

### Project Structure
```
statsFactory/
├── src/
│   ├── components/         # Reusable UI components
│   ├── context/           # React context providers
│   ├── hooks/             # Custom React hooks
│   ├── integrations/      # External service integrations (Supabase)
│   ├── lib/               # Utility libraries
│   ├── pages/             # Page components
│   ├── services/          # Business logic services
│   ├── types/             # TypeScript type definitions
│   ├── utils/             # Helper utilities
│   ├── App.tsx            # Main app component
│   └── main.tsx           # Entry point
├── supabase/              # Database migrations & configs
├── docs/                  # Comprehensive documentation
├── memory-bank/           # Project context & rules
└── .cursor/rules/         # Cursor AI development rules
```

### Core Architectural Patterns

#### 1. Supabase Gateway Pattern
All Supabase interactions go through a centralized gateway (`src/integrations/supabase/gateway.ts`):
- Singleton client management
- Error telemetry and monitoring
- Consistent configuration
- Automatic token cleanup

```typescript
// Always import from gateway, not lib/supabase
import { supabase } from '@/integrations/supabase/gateway';
```

#### 2. Provider Hierarchy
The app uses a specific provider nesting order in `main.tsx`:
```
ThemeProvider
  └── QueryClientProvider
      └── AuthProvider
          └── LanguageProvider
              └── BrowserRouter
                  └── HelpProvider
                      └── App
```

#### 3. Authentication & Role-Based Access Control
- Custom auth implementation built on Supabase Auth
- Role-based permissions with hierarchical structure
- Mock auth provider for development/testing
- Protected routes using `RequireAuth` and `RequireAdmin` components

#### 4. Error Boundaries & Stability
- Comprehensive error boundaries with fallback UI
- Request retry logic with exponential backoff
- Circuit breakers for critical systems
- Browser compatibility polyfills

#### 5. Feature Flags System
- Runtime feature toggles without deployment
- Per-user and global feature flags
- Integration with Kanban board preferences

#### 6. Internationalization (i18n)
- Full support for English and Arabic
- RTL layout support
- Translation context provider
- Language-specific date/number formatting

## Development Practices

### Task-Driven Development
The project uses Task Master for task management:
- All work must be associated with a Task Master task ID
- Tasks are tracked in `.taskmaster/tasks/tasks.json`
- Use MCP tools or CLI for task management
- Every code change must trace back to a task

### Memory Bank Rules
Critical development rules are stored in `memory-bank/rules/`:
1. **Core Principles** (`01-core-principles.md`): Task-driven development, Context7 architecture
2. **Workflow Rules** (`03-workflow-rules.md`): Development workflow with Task Master

### Definition of Done (DoD)
All work must pass five-point check:
1. Code complete and tested
2. Documentation updated
3. Tests passing (80% coverage minimum)
4. Security scan passed
5. Task marked complete in Task Master

### Database Patterns
- RLS (Row Level Security) policies for all tables
- Stored procedures for complex operations
- Migration-based schema management
- Comprehensive audit logging

### Component Patterns
- Consistent use of shadcn/ui components
- Form validation with React Hook Form + Zod
- Loading states with skeletons
- Error boundaries for resilience

## Key Features & Modules

### Products Module
- Statistical product management with versioning
- Iteration tracking and team collaboration
- Kanban board with drag-and-drop
- Timeline and calendar views

### Indicators Module
- SDG (Sustainable Development Goals) integration
- Real-time data from UN Statistics API
- Interactive charts and visualizations
- Regional comparison tools

### Approvals System
- Multi-stage workflow approvals
- Delegation management
- Approval analytics and reporting
- Notification system

### Gamification System
- User achievements and badges
- Points and leveling system
- Leaderboards
- Progress tracking

## Environment Setup

### Required Environment Variables
```bash
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### Optional Performance Tuning
- Telemetry can be toggled via `/toggle-telemetry.html`
- Offline mode available for development
- Force RPC fallbacks for stability

## Performance Optimizations
- Request deduplication and caching
- Lazy loading for routes and components
- Image caching with persistent storage
- Optimized bundle splitting
- React Query for server state management

## Testing & Quality
- ESLint configuration for code quality
- TypeScript with relaxed settings for gradual adoption
- Component testing patterns
- E2E testing considerations

## Important Files to Review
1. `src/integrations/supabase/gateway.ts` - Supabase client management
2. `src/App.tsx` - Main application structure and routing
3. `src/hooks/useAuth.tsx` - Authentication logic
4. `src/components/CapabilityProvider.tsx` - Permissions system
5. `memory-bank/rules/` - Development guidelines

## Common Development Tasks

### Adding a New Feature
1. Create task in Task Master
2. Use Context7 MCP for scaffolding
3. Follow existing patterns in similar components
4. Update documentation in real-time
5. Pass Definition of Done checklist

### Working with Supabase
- Always use the gateway pattern
- Check RLS policies for new tables
- Use stored procedures for complex queries
- Handle errors with telemetry

### Debugging Tips
- Check browser console for telemetry logs
- Use `/test-*` routes for isolated testing
- Review `docs/` folder for troubleshooting guides
- Check Task Master for related task context

## Notes for Claude
- This is a production application with real users
- Security and stability are top priorities
- All changes must be traceable to tasks
- Follow established patterns unless explicitly asked to change them
- The codebase uses a mix of modern and legacy patterns - prefer modern patterns for new code
- When in doubt, check the memory-bank rules and existing implementations