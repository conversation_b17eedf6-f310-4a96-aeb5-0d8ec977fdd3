# 01. Core Principles

## Memory Bank First
1. **Read Order**: ALWAYS read memory-bank/rules/ first, then memory-bank/ files
2. **No Exceptions**: This applies to EVERY new session, no matter how recent
3. **Complete Read**: Read ALL files, not just some

## Task-Driven Development
1. **Task Master Integration**: All work MUST be associated with a Task Master task
2. **No Orphan Code**: Every line of code must trace back to a task ID
3. **Progress Tracking**: Use Task Master to track all development progress

## Context7 Architecture
1. **All New Work**: Use Context7 MCP for scaffolding ALL new features
2. **Task Reference**: Include task ID in Context7 scaffold metadata
3. **Best Practices**: Context7 ensures compliance with project patterns

## Definition of Done
1. **Five-Point Check**: ALL work must pass the DoD before promotion
2. **No Shortcuts**: DoD is mandatory, not optional
3. **Task Completion**: Task can only be marked done after DoD passes

## Documentation Discipline
1. **Real-Time Updates**: Update docs as you code, not after
2. **Task Context**: Reference task IDs in all documentation
3. **Memory Bank Sync**: Keep memory bank synchronized with Task Master

## Security & Quality
1. **Security First**: Block on any high-severity issue
2. **Test Coverage**: Minimum 80% coverage required
3. **License Compliance**: Only OSI-approved dependencies

## MCP Usage
1. **Task Master MCP**: Primary tool for task management
2. **Context7 MCP**: Primary tool for code scaffolding
3. **Auto-Selection**: Let the system choose appropriate MCPs

## Communication Protocol
1. **Task References**: Always mention current task ID in discussions
2. **Status Updates**: Provide task status in every progress report
3. **Blocker Reporting**: Immediately report any task blockers

## Error Handling
1. **Stop on Failure**: If DoD fails, STOP and report
2. **Task Documentation**: Document all errors in task details
3. **User Direction**: Always ask for direction on failures

## Continuous Improvement
1. **Pattern Recognition**: Identify repeated patterns for new rules
2. **Task Insights**: Use task completion data to improve estimates
3. **Workflow Optimization**: Regularly review and optimize workflow
