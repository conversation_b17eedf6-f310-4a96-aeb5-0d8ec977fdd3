# 02. Task Master Protocol

## MANDATORY Task Master Usage

### Session Initialization
Every development session MUST start with:
```
1. "What's the next task I should work on?"
2. Review the task details
3. Check task dependencies
4. Plan implementation approach
```

### Task Assignment Rules
1. **No Unassigned Work**: Every piece of development MUST have a task ID
2. **Task Creation**: If no task exists, create one BEFORE starting work
3. **Task Scope**: One task = one atomic unit of work
4. **Task Ownership**: Claim task before starting implementation

### During Development
Execute these Task Master commands as needed:
- `Show me task [ID]` - Before starting any implementation
- `Can you help me expand task [ID]?` - For complex tasks
- `Update task [ID] with: [progress notes]` - Document decisions
- `Add a new task: [discovered work]` - For newly identified work

### Task Status Management
1. **pending** → Task is ready to start (dependencies met)
2. **in-progress** → Actively working on task
3. **blocked** → Waiting on external dependency
4. **review** → Implementation complete, awaiting review
5. **done** → Task fully complete and verified

### Completion Protocol
Before marking any task as done:
1. Verify all acceptance criteria are met
2. Ensure Definition of Done passes
3. Update task with final implementation notes
4. Check for any follow-up tasks needed
5. Execute: `Mark task [ID] as done`

### Task Dependencies
1. **Check Before Start**: Never start a task with unmet dependencies
2. **Update on Changes**: If implementation affects other tasks, update them
3. **Chain Management**: Respect the dependency chain at all times
4. **Blocker Protocol**: Immediately report and document blockers

### Emergency Protocols

#### If Task Master is Down
1. Document all work in `memory-bank/activeContext.md`
2. Create temporary task tracking in memory bank
3. Include task descriptions and dependencies
4. Sync with Task Master when available

#### If Task Scope Changes
1. Stop implementation
2. Document the scope change
3. Either:
   - Update current task description, OR
   - Split into multiple tasks
4. Get user approval before continuing

### Integration Checkpoints

#### Before Writing Code
- [ ] Task ID identified
- [ ] Dependencies verified
- [ ] Task details reviewed
- [ ] Implementation approach planned

#### After Writing Code
- [ ] Task notes updated
- [ ] Dependencies still valid
- [ ] Follow-up tasks created if needed
- [ ] Status updated appropriately

#### End of Session
- [ ] All active tasks have status updates
- [ ] Blockers are documented
- [ ] Next steps are clear
- [ ] Memory bank is synchronized

### Task Master Command Quick Reference
```bash
# Discovery
"What's the next task I should work on?"
"Show me all tasks"
"Show me tasks with status: pending"

# Task Details
"Show me task [ID]"
"What are the dependencies for task [ID]?"
"Show me subtasks for task [ID]"

# Task Management
"Can you help me implement task [ID]?"
"Can you help me expand task [ID]?"
"Mark task [ID] as [status]"
"Add a new task: [description]"

# Updates
"Update task [ID] with: [information]"
"Add subtask to task [ID]: [description]"
"Link task [ID] as dependency of task [ID2]"
```

### Golden Rules
1. **Task First, Code Second**: Always have a task before coding
2. **Document Everything**: Task details are project memory
3. **Respect Dependencies**: They exist for a reason
4. **Small Tasks Win**: Better to have many small tasks than few large ones
5. **Continuous Updates**: Task status should reflect reality

Remember: Task Master is not bureaucracy, it's our project nervous system. It ensures everyone knows what's happening, what's next, and what's blocked.
