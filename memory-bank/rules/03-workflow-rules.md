# 03. Workflow Rules

## Task Master Integration (MANDATORY)

### Starting a Work Session
1. **Check Current Task**: Always run `What's the next task I should work on?` to identify the current task
2. **Review Task Details**: Use `Show me task [ID]` to understand full requirements
3. **Expand if Needed**: For complex tasks, use `Can you help me expand task [ID]?`

### During Implementation
1. **Context7 Scaffolding**: All new features MUST use Context7 MCP as defined by Task Master
2. **Task Context**: Reference the current task ID in all Context7 scaffolds
3. **Progress Updates**: Log implementation notes using Task Master
4. **Dependency Checks**: Verify all task dependencies are met before starting

### Completing Work
1. **Update Status**: Mark tasks as done using `Mark task [ID] as done`
2. **Document Changes**: Update subtask details if implementation differs from plan
3. **Check Next**: Always check for the next task before ending a session

### Task Master Commands Reference
```
# Essential Commands (USE THESE REGULARLY)
- What's the next task I should work on?
- Show me all tasks
- Show me task [ID]
- Mark task [ID] as done
- Can you help me implement task [ID]?
- Can you help me expand task [ID]?
- Add a new task: [description]
- Update task [ID] with new information: [details]
```

## Development Flow

### PLAN Mode Flow
```
1. Read Memory Bank Rules
2. Check Task Master for current/next task
3. Review task requirements and dependencies
4. Plan implementation approach
5. Create Context7 scaffold plan
6. Present plan to user for approval
```

### ACT Mode Flow
```
1. Read Memory Bank Rules
2. Get current task from Task Master
3. Create Context7 scaffold for the task
4. Implement following task requirements
5. Run Definition of Done checks
6. Update Task Master status
7. Document in memory bank
8. Check for next task
```

## Task-Driven Development Rules

1. **No Orphan Work**: All development MUST be tied to a Task Master task
2. **Task First**: Before any implementation, identify or create the relevant task
3. **Status Tracking**: Update task status at each major milestone
4. **Dependency Respect**: Never start a task with unmet dependencies
5. **Documentation**: Task implementation details must be logged

## Integration Points

### When Creating New Features
1. Check if a task exists for the feature
2. If not, create a task: `Add a new task: [feature description]`
3. Get the task ID and use it in all related work
4. Reference task ID in Context7 scaffold

### When Fixing Bugs
1. Create a bug fix task if one doesn't exist
2. Link the fix to the task ID
3. Document the fix in task details
4. Update task status when complete

### When Refactoring
1. Create refactoring task with clear scope
2. Expand task to identify all affected areas
3. Track progress through subtasks
4. Ensure all tests pass before marking done

## Task Master State Management

### Session Start Checklist
- [ ] Check Task Master connection
- [ ] Review current task status
- [ ] Identify blocked tasks
- [ ] Plan session goals

### Session End Checklist
- [ ] Update all modified task statuses
- [ ] Document any blockers
- [ ] Note next steps in task details
- [ ] Verify memory bank is updated

## Error Handling

### If Task Master is Unavailable
1. Document work in memory bank activeContext.md
2. Create manual task list in memory bank
3. Sync with Task Master when available
4. Never proceed without task tracking

### If Task Dependencies Conflict
1. Stop implementation immediately
2. Document the conflict
3. Ask user for resolution
4. Update task dependencies accordingly

## Best Practices

1. **Atomic Tasks**: Keep tasks small and focused
2. **Clear Descriptions**: Task descriptions should be actionable
3. **Regular Updates**: Update task status after each work session
4. **Dependency Clarity**: Explicitly state all task dependencies
5. **Progress Visibility**: Use Task Master to show progress to stakeholders

## Task Master MCP Usage

The Task Master MCP MUST be used for:
- Getting next task to work on
- Checking task details and requirements
- Updating task status
- Adding new tasks discovered during development
- Expanding complex tasks into subtasks
- Tracking dependencies between tasks
- Documenting implementation decisions

Remember: Task Master is not just a tool, it's the backbone of our development workflow. Every piece of work must flow through Task Master to maintain project coherence and progress visibility.
