{"mcpServers": {"fetch-mcp": {"type": "stdio", "command": "npx", "args": ["fetch-mcp@latest"], "env": {}}, "supabase": {"type": "stdio", "command": "npx", "args": ["@supabase/mcp-server-supabase@latest", "--access-token", "********************************************"], "env": {}}, "filesystem": {"type": "stdio", "command": "npx", "args": ["@modelcontextprotocol/server-filesystem@latest", "."], "env": {}}, "github": {"type": "stdio", "command": "npx", "args": ["@modelcontextprotocol/server-github@latest"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************"}}, "taskmaster-ai": {"type": "stdio", "command": "npx", "args": ["task-master-ai@latest"], "env": {"ANTHROPIC_API_KEY": "************************************************************************************************************", "PERPLEXITY_API_KEY": "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", "OPENAI_API_KEY": "********************************************************************************************************************************************************************"}}, "figma-mcp-pro": {"type": "stdio", "command": "figma-mcp-pro", "args": ["--st<PERSON>"], "env": {"FIGMA_API_KEY": "YOUR_FIGMA_API_KEY_HERE"}}}}