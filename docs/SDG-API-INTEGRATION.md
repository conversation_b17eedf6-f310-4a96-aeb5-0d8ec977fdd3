# SDG API Integration

This document provides an overview of the Sustainable Development Goals (SDG) API integration with the Stat Linker Factory application.

## Overview

The SDG API integration uses Supabase Edge Functions to securely fetch, process, and store SDG data from the UN SDG API. The implementation includes:

1. **Data Collection** - Fetching SDG goals, targets, indicators, and observations from the UN SDG API
2. **Data Storage** - Storing the collected data in structured Supabase tables
3. **Analytics** - Calculating rankings and benchmarks for various indicators
4. **User Interface** - Tools and interfaces for interacting with the SDG data

## Architecture

```mermaid
graph TB
    Client[Client Application] --> API[Supabase Edge Function]
    API --> UN[UN SDG API]
    API --> DB[Supabase Database]
    API --> Storage[Supabase Storage]
    Client --> DB
    
    subgraph "SDG ETL Pipeline"
    API
    end
    
    subgraph "Data Sources"
    UN
    end
    
    subgraph "Data Storage"
    DB
    Storage
    end
```

## Components

### 1. Edge Functions

- **sdg-etl**: Main Edge Function for SDG data collection and processing
- **debug**: Utility Edge Function for diagnosing connection and environment issues

### 2. Database Tables

- **sdg_goals**: Basic information about the 17 SDGs
- **sdg_targets**: Targets for each SDG goal
- **sdg_indicators**: Indicators for measuring progress on targets
- **sdg_observations**: Actual data points for indicators by country and year
- **sdg_rankings**: Calculated rankings for Saudi Arabia across indicators
- **sdg_benchmarks**: Regional and global benchmarks for comparison

### 3. Utilities

- **fix-supabase-connection.js**: Script to fix connection and CORS issues
- **deploy-sdg-edge-function.js**: Script to deploy the SDG ETL Edge Function
- **check-auth-secrets.js**: Script to diagnose authentication issues
- **fetch-sdg-data.js**: CLI tool for fetching SDG data

### 4. User Interfaces

- **sdg-api-tester.html**: Web interface for testing the SDG API
- **check-supabase-connection.html**: Tool to verify Supabase connectivity
- **fix-env-variables.html**: Tool to update environment variables

## Available Actions

The SDG ETL Edge Function supports the following actions:

| Action | Description | Parameters |
|--------|-------------|------------|
| `test` | Test the Edge Function connection | None |
| `goals` | Fetch and store SDG goals | `lang` (en/ar) |
| `targets` | Fetch and store targets for a goal | `goalCode`, `lang` |
| `indicators` | Fetch and store indicators for a target | `targetCode`, `lang` |
| `observations` | Fetch and store observations | `seriesCode`, `indicatorCode`, `geoCode` |
| `rankings` | Calculate rankings | `indicatorCode` (optional), `year`, `calculateBenchmarks` |
| `all` | Process all goals, targets, and indicators | `lang`, `includeMetadata` |

## Environment Setup

The SDG API integration requires the following environment variables:

```
VITE_SUPABASE_URL=your-supabase-url
VITE_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
VITE_EDGE_FUNCTION_SECRET=your-edge-function-secret
```

## Security

The SDG ETL Edge Function is secured using a secret key. All requests to the Edge Function must include an Authorization header with the secret key:

```
Authorization: Bearer your-edge-function-secret
```

## Workflow Examples

### Fetch and Store SDG Goals

```javascript
const response = await fetch(`${SUPABASE_URL}/functions/v1/sdg-etl`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${EDGE_FUNCTION_SECRET}`
  },
  body: JSON.stringify({ 
    action: 'goals',
    lang: 'en'
  })
});

const data = await response.json();
```

### Calculate Rankings for an Indicator

```javascript
const response = await fetch(`${SUPABASE_URL}/functions/v1/sdg-etl`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${EDGE_FUNCTION_SECRET}`
  },
  body: JSON.stringify({ 
    action: 'rankings',
    indicatorCode: '1.1.1',
    year: 2023,
    calculateBenchmarks: true
  })
});

const data = await response.json();
```

## Troubleshooting

If you encounter issues with the SDG API integration, try the following:

1. Use the **sdg-api-tester.html** to test the API directly
2. Check connections with **check-supabase-connection.html**
3. Update environment variables with **fix-env-variables.html**
4. Run `node scripts/fix-supabase-connection.js` to update CORS configuration
5. Run `node scripts/deploy-sdg-edge-function.js` to redeploy the Edge Function
6. Check logs in the Supabase Dashboard

## Data References

The SDG data is sourced from the official UN SDG API:
- [UN SDG API Documentation](https://unstats.un.org/sdgapi/swagger/)
- [UN SDG Website](https://sdgs.un.org/)

## Further Development

Potential enhancements for the SDG API integration:

1. Add support for trend analysis of SDG indicators over time
2. Implement predictive analytics for future SDG performance
3. Create visualization components for SDG data
4. Add comparative analysis features for regional benchmarking
5. Implement automated data refresh schedules
