# Fixing "Operating in offline mode" Issues

This document explains how to fix the offline mode-related issues affecting connectivity to Supabase and causing RPC function failures.

## Problem

Some users may encounter these issues:
- "Operating in offline mode" errors in the browser console
- 404 (Not Found) errors for RPC functions
- Authentication failures with messages about offline mode
- The application working in the test environment but not in production

## Root Cause

The application has a setting called `supabase_offline_mode` in localStorage that controls whether the app uses a real Supabase client or a mock offline client. When this setting is `true`, all Supabase operations return mock responses instead of connecting to the actual Supabase backend.

### Problem Timeline

1. **Multiple Offline Mode Flags**: The system has several ways to enable offline mode:
   - `localStorage.getItem('supabase_offline_mode') === 'true'` (primary flag)
   - `appConfig.offlineMode = true`
   - `connectionConfig.offlineMode = true`
   - `localStorage.getItem('offlineMode') === 'true'`

2. **Testing/Cache Issues**: 
   - Testing tools may report all functions working correctly while the actual app still fails
   - This happens because the test tools might not be affected by offline mode
   - Browser caching can also cause outdated error responses to persist

## Solution

We've created two utilities to solve this issue:

1. **Fix Offline Mode Tool**: Use this to diagnose and fix offline mode issues
   - Located at: `/fix-offline-mode.html`
   - Shows the status of all offline mode flags
   - Provides one-click fix to disable all offline mode settings
   - Tests RPC functions to verify they're working

2. **Cache Clearing Tool**: Use this to clear cached responses that might be causing continued errors
   - Located at: `/clear-cache-fix.html`
   - Clears browser cache and service workers
   - Provides clean testing environment for RPC functions

## Step-by-Step Fix

1. Open `/fix-offline-mode.html` in your browser
2. Look for the "Primary Offline Mode Flag" - if it shows "Enabled", click "Disable Offline Mode"
3. After disabling, click "Test RPC Functions" to verify they're working
4. If issues persist, try the "Clear ALL Storage & Reload" button
5. Return to the application and check if the issue is resolved

## For Developers

The offline mode system is implemented in `src/integrations/supabase/gateway.ts`:

```javascript
// Key code section from gateway.ts
const isOfflineMode = window.localStorage.getItem('supabase_offline_mode') === 'true';
  
if (isOfflineMode) {
  console.warn("Operating in offline mode - using mock Supabase client");
  return createOfflineClient();
}
```

The primary fix is to ensure this localStorage flag is not set to `true`. For additional reliability, we also clear other legacy offline mode settings.

## Additional Notes

1. **Telemetry**: The application uses a telemetry system that can be configured or disabled. This is separate from offline mode but may also affect performance.

2. **Multiple Parameters**: RPC functions sometimes have multiple parameter formats (with or without parameters). Our testing tools try both formats to ensure compatibility.

3. **Client-Side Fallbacks**: The application has fallback mechanisms in `src/services/rpc-fallbacks.ts` that will handle cases where the server functions aren't available. However, these fallbacks provide limited functionality compared to the real backend.

## Verification

After applying the fix, verify that:

1. Console shows no "Operating in offline mode" errors
2. RPC functions return 200 OK responses
3. Authentication works correctly
4. The application functions normally

If problems persist, contact support for further assistance.
