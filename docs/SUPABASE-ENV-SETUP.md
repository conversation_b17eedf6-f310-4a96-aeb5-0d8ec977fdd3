# Supabase Environment Variables Setup

This document explains how to properly configure Supabase environment variables for both backend scripts and frontend Vite application.

## Environment Variables Overview

The application requires specific environment variables to connect to Supabase services. These variables need to be set in the `.env` file in the project root.

## Required Variables

### Backend Scripts (Node.js)

Backend scripts and server-side code use these variables:

- `SUPABASE_URL` - The URL of your Supabase project
- `SUPABASE_ANON_KEY` - The public/anonymous API key for your Supabase project
- `SUPABASE_SERVICE_ROLE_KEY` - The service role key for administrative operations

### Frontend (Vite Application)

For frontend Vite application, variables must be prefixed with `VITE_`:

- `VITE_SUPABASE_URL` - The URL of your Supabase project
- `VITE_SUPABASE_ANON_KEY` - The public/anonymous API key for your Supabase project

> **Important**: Variables without the `VITE_` prefix are not accessible in browser code!

### Database Migration (Optional)

For direct database access (migrations, etc.):

- `S<PERSON><PERSON>ASE_CONNECTION_STRING` - PostgreSQL connection string to your Supabase database

## Sample .env File

```env
# Supabase Connection Details

# Supabase Project URL
SUPABASE_URL=https://your-project-ref.supabase.co
# Vite version for frontend access
VITE_SUPABASE_URL=https://your-project-ref.supabase.co

# Supabase API Keys
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# For PostgreSQL direct connection (optional)
# SUPABASE_CONNECTION_STRING=postgresql://postgres:<EMAIL>:5432/postgres

# Vite environment variables for frontend access
VITE_SUPABASE_ANON_KEY=your-anon-key
```

## Troubleshooting

### Blank Screen / Environment Variables Error

If you see a blank screen with errors about missing Supabase environment variables:

```
Error: Missing Supabase environment variables.
Make sure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in your environment.
```

**Solution**:
1. Check your `.env` file and ensure both `VITE_SUPABASE_URL` and `VITE_SUPABASE_ANON_KEY` are set
2. Restart your development server after modifying environment variables
3. Clear browser cache if the problem persists

### Database Access Issues

If your database migrations or direct SQL commands fail:

```
Cannot connect to database: connection refused
```

**Solution**:
1. Verify your `SUPABASE_CONNECTION_STRING` is correctly formatted in the `.env` file
2. Ensure you have the correct database password
3. Check if IP restrictions are enabled in your Supabase project settings

## Why Two Sets of Variables?

We need two sets of variables because:

1. Backend code can access all environment variables
2. Frontend code (browser) can only access variables prefixed with `VITE_` due to security measures implemented by Vite

This separation ensures sensitive variables aren't exposed in client-side code.
