# SDG API Authentication Fix Guide

## Problem Description

The SDG API integration was encountering a **401 Unauthorized** error when attempting to call the Edge Function from the frontend application. This was preventing the SDG data from being retrieved and displayed in the application's SDG Dashboard.

The issue stemmed from a problem with environment variables not being properly synchronized between:
1. The frontend application's `.env.local` file
2. The Edge Function's environment variables (in `supabase/functions/sdg-etl/.env`)

Even though the token values appeared to be identical in both places, the Edge Function was rejecting the authentication token provided by the frontend.

## Root Cause Analysis

After extensive debugging, we identified two potential issues:

1. **Whitespace handling**: Possible invisible whitespace characters or line ending differences in the token value
2. **Environment variable loading**: Inconsistencies in how the Edge Function's Deno runtime loads environment variables compared to the frontend's Vite environment

We observed inconsistent environment variable handling when deploying the Edge Functions, resulting in:

```
AUTH_TOKEN:    "TEST_KEY_2025-04-26T16-27-04-282Z_epcmo5ly"
SECRET_VALUE:  "SDG_EDGE_FUNCTION_AUTHORIZATION"
AUTH_MATCHES:  "no"
TOKEN_LENGTH:  42
SECRET_LENGTH: 38
LENGTH_MATCH:  "no" 
```

The debug output showed that the token being sent and the token expected by the server were different, despite appearing identical in the environment files.

## Solution Implemented

We solved this by implementing a simplified authentication approach with the following steps:

1. Created a **hardcoded simple token** that would be used consistently by both client and server:
   ```
   SIMPLE_SDG_SECRET_KEY_2025
   ```

2. Created a synchronization script (`src/commands/sync-tokens.js`) that:
   - Updates the frontend's `.env.local` file with the simple token
   - Updates the Edge Function's environment files with the same token
   - Forces a redeployment of both Edge Functions with the new token
   - Updates the test scripts to use the hardcoded token value directly

3. Modified the authentication test script (`src/commands/test-sdg-auth.js`) to use the simplified token directly instead of reading from environment variables, ensuring consistent testing.

4. Created additional scripts for troubleshooting:
   - `verify-edge-function-env.js`: Checks and displays environment variables across all files
   - `force-env-update.js`: Forces Edge Functions to receive updated environment values using a two-step deployment process

## How to Fix Authentication Issues

If you encounter authentication issues with the SDG API integration, follow these steps:

1. Run the synchronization script:
   ```
   node src/commands/sync-tokens.js
   ```

2. If the issue persists, verify the environment variables:
   ```
   node src/commands/verify-edge-function-env.js
   ```

3. Test the Edge Function authentication directly:
   ```
   node src/commands/test-sdg-auth.js
   ```

## Notes for Future Development

- Consider moving away from environment variables for authentication between frontend and Edge Functions, and instead use API keys or a more robust authentication system.
- The current solution uses a simplified hardcoded token approach, which is sufficient for development but should be replaced with more secure authentication for production.
- Edge Function deployments may require a "force update" approach when environment variables change, as simple redeployment may not always refresh the environment variables correctly.

## Related Files

- `src/commands/sync-tokens.js`: Main script to synchronize authentication tokens
- `src/commands/test-sdg-auth.js`: Test script for Edge Function authentication
- `scripts/fix-environment-vars.js`: Alternative approach using ES modules
- `src/commands/verify-edge-function-env.js`: Tool to check environment variables
- `src/commands/force-env-update.js`: Tool to force environment variable updates
- `.env.local`: Frontend environment variables file
- `supabase/functions/sdg-etl/.env`: Edge Function environment variables file
