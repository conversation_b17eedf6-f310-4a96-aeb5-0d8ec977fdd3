# Environment Variables Guide

This document explains the necessary environment variables for the application, including the SDG API integration.

## Overview

The application uses environment variables to store configuration settings and sensitive information like API keys and secrets. These variables are stored in an `.env.local` file in the root directory of the project, which is not committed to version control for security reasons.

## Required Environment Variables

### Core Variables

```properties
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Application Configuration
VITE_APP_ENV=development
VITE_APP_URL=http://localhost:8080
```

### SDG API Integration Variables

```properties
# SDG Edge Function Secret
VITE_EDGE_FUNCTION_SECRET=your-secure-secret-for-edge-function-auth
```

## Setting Up Environment Variables

1. Create a file named `.env.local` in the root of the project.
2. Copy the template variables above and replace placeholder values with your actual configuration.
3. Make sure the file is listed in your `.gitignore` to prevent committing secrets to version control.

## Getting Supabase Credentials

1. Sign in to your [Supabase Dashboard](https://app.supabase.com/).
2. Select your project.
3. Go to Project Settings > API.
4. Under "Project API keys", you'll find:
   - `URL`: Your Supabase project URL
   - `anon/public`: The anonymous key (use for VITE_SUPABASE_ANON_KEY)
   - `service_role`: The service role key (use for SUPABASE_SERVICE_ROLE_KEY)

## SDG Edge Function Secret

This is a secure random string used to authenticate requests to the SDG Edge Function. You can generate a secure random string using:

```bash
# On macOS/Linux:
openssl rand -base64 32

# On Windows (PowerShell):
$randomBytes = New-Object byte[] 32
[Security.Cryptography.RandomNumberGenerator]::Create().GetBytes($randomBytes)
[Convert]::ToBase64String($randomBytes)
```

## Environment Variables during Deployment

When deploying to production, ensure these environment variables are properly set in your hosting environment. For local development, the `.env.local` file is sufficient.

## Troubleshooting

If you encounter issues related to environment variables:

1. Verify that your `.env.local` file exists and contains all required variables.
2. Check that the values are correct (no leading/trailing spaces, proper formatting).
3. For Vite applications, remember that only variables prefixed with `VITE_` are accessible in frontend code.
4. After changing environment variables, restart your development server for the changes to take effect.
5. For Edge Function issues, make sure the `VITE_EDGE_FUNCTION_SECRET` matches the secret used in the Edge Function deployment.

## Security Notes

- Never commit your `.env.local` file or any file containing sensitive credentials to version control.
- The service role key (`SUPABASE_SERVICE_ROLE_KEY`) has admin privileges - only use it in secure server-side code.
- Regularly rotate your secrets and update the environment variables accordingly.
