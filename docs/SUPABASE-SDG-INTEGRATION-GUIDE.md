# Supabase SDG Integration Guide

This guide explains how to work with the Sustainable Development Goals (SDG) API integration in the Stat Linker Factory application, including how to troubleshoot common connection issues.

## Overview

The SDG API integration connects to the UN Sustainable Development Goals API, retrieves data, and stores it in your Supabase database. This data includes:

- SDG goals, targets, and indicators
- Observations for specific indicators by country and year
- Rankings and benchmarks for Saudi Arabia among global and regional countries

## Configuration

The integration requires the following environment variables:

```
VITE_SUPABASE_URL=https://<your-project-id>.supabase.co
VITE_SUPABASE_ANON_KEY=<your-anon-key>
SUPABASE_SERVICE_ROLE_KEY=<your-service-role-key>
VITE_EDGE_FUNCTION_SECRET=<your-edge-function-secret>
```

These are stored in your `.env.local` file at the project root.

## Troubleshooting Connection Issues

If you encounter connection issues with the SDG API integration, follow these steps:

### 1. Run the Supabase Connection Fix Script

We've created a diagnostic script that checks and fixes common Supabase connection issues:

```bash
node scripts/fix-supabase-connection.js
```

This script:
- Verifies your environment variables
- Tests connectivity to your Supabase project
- Validates the SDG ETL Edge Function
- Checks for required database tables
- Rebuilds environment configuration files 

### 2. Reset Browser Storage

If you're still seeing errors in the browser console, use one of the provided utility pages:

- `/reset-supabase.html` - Resets Supabase connection state in localStorage
- `/check-supabase-connection.html` - Checks connectivity and shows detailed diagnostics

### 3. Common Error Solutions

#### "Invalid API Key" Errors

These typically occur when:
- Environment variables are not correctly loaded
- Browser's localStorage has outdated or corrupted tokens
- The development server needs to be restarted 

Solution steps:
1. Run `node scripts/fix-supabase-connection.js`
2. Restart the development server with `npm run dev`
3. Open `/reset-supabase.html` in your browser and click "Reset Supabase Connection"
4. Return to the application and test functionality

#### Missing Tables or Data

If you're missing SDG data or tables:

```bash
# Fetch SDG goals
node src/commands/fetch-sdg-data.js goals

# Fetch all SDG data (goals, targets, indicators)
node src/commands/fetch-sdg-data.js all

# Calculate rankings
node src/commands/fetch-sdg-data.js rankings
```

## Working with SDG Data

### Fetching Data

Run the fetch command with different parameters depending on what data you need:

```bash
# Fetch all data (goals, targets, indicators)
node src/commands/fetch-sdg-data.js all

# Fetch only goals
node src/commands/fetch-sdg-data.js goals

# Fetch goals in Arabic 
node src/commands/fetch-sdg-data.js goals lang=ar

# Fetch observations for Saudi Arabia for a specific indicator
node src/commands/fetch-sdg-data.js observations seriesCode=SI_POV_DAY1 geoCode=SAU

# Calculate rankings for 2023
node src/commands/fetch-sdg-data.js rankings year=2023
```

### Using the SDG API in Components

Import the `useSDGData` hook in your components:

```jsx
import { useSDGData } from '../hooks/useSDGData';

const SDGComponent = () => {
  const {
    goals,
    indicators,
    observations,
    rankings,
    isLoading,
    error,
    updateFilters,
  } = useSDGData();
  
  // Your component logic here
};
```

### Redeploying the Edge Function

If you need to update or redeploy the SDG ETL Edge Function:

```bash
node scripts/deploy-sdg-edge-function.js
```

## Database Schema

The SDG data is stored in the following tables:

- `sdg_goals` - The 17 Sustainable Development Goals
- `sdg_targets` - Targets for each goal
- `sdg_indicators` - Measurement indicators for targets
- `sdg_observations` - Actual data points by country and year
- `sdg_rankings` - Saudi Arabia's rankings among different regions
- `sdg_benchmarks` - Regional benchmarks for comparison

## Verifying Health

To verify that everything is working correctly:

1. Check that the Edge Function is deployed:
   ```bash
   node scripts/fix-supabase-connection.js
   ```

2. Verify data exists in the database:
   ```bash
   node src/commands/fetch-sdg-data.js goals
   ```

3. Open the application in a browser and navigate to the SDG dashboard page to see if data is loading correctly.
