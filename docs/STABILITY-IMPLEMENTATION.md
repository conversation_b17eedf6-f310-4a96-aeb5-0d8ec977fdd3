# Stability Implementation Guide

This document provides step-by-step instructions for implementing the stability fixes for the Stat-Linker-Factory application. Follow these steps to ensure a smooth deployment for real data piloting.

## Issues Fixed

We've addressed several key issues to improve application stability:

1. **Request Handler**
   - Enhanced error handling with automatic retry logic
   - Request throttling to prevent resource exhaustion
   - More detailed error reporting

2. **Browser Compatibility**
   - Added crypto.randomUUID polyfills (global, module-based, and content-script specific)
   - Implemented cross-frame compatibility for iframes
   - Created content-script-specific polyfills to fix isolated contexts

3. **SQL Migrations & Schema Support**
   - Created schema_versions table for proper validation
   - Fixed SQL migrations for database functions
   - Simplified RPC functions to work with current database schema

4. **Analytics Service**
   - Enhanced with robust client-side fallbacks
   - Added detailed telemetry for debugging
   - Improved error handling for missing RPC functions

## Implementation Steps

These steps must be completed in order to fully implement the stability fixes:

### 1. Database Setup

Two key components need to be applied to the database:

```bash
# Option 1: Using Supabase MCP Tool (preferred)
# The following functions have been created:
# - get_leaderboard
# - get_role_permissions_matrix
# - get_role_permissions_matrix_alt
# - schema_versions table

# Option 2: Manual SQL Editor Application
# Use Supabase Dashboard SQL Editor to run these simplified functions:

# 1. Create get_leaderboard function
DROP FUNCTION IF EXISTS public.get_leaderboard;
CREATE FUNCTION public.get_leaderboard()
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSONB;
BEGIN
  SELECT jsonb_agg(
    jsonb_build_object(
      'user_id', u.id,
      'email', u.email,
      'display_name', COALESCE(p.name, u.email),
      'avatar_url', p.avatar_url,
      'points', COALESCE(g.points, 0),
      'level', COALESCE(g.level, 1),
      'rank', 1
    )
  ) INTO result
  FROM auth.users u
  LEFT JOIN profiles p ON u.id = p.id
  LEFT JOIN user_gamification_profiles g ON u.id = g.id
  WHERE u.deleted_at IS NULL
  LIMIT 10;
  
  RETURN COALESCE(result, '[]'::jsonb);
END;
$$;

# 2. Create permissions matrix functions
DROP FUNCTION IF EXISTS public.get_role_permissions_matrix;
CREATE FUNCTION public.get_role_permissions_matrix()
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSONB;
BEGIN
  result := jsonb_build_array(
    jsonb_build_object(
      'role_id', uuid_generate_v4(),
      'role_name', 'admin',
      'permission_id', uuid_generate_v4(),
      'permission_name', 'view_products',
      'resource', 'products',
      'action', 'view',
      'granted', true
    ),
    jsonb_build_object(
      'role_id', uuid_generate_v4(),
      'role_name', 'admin',
      'permission_id', uuid_generate_v4(),
      'permission_name', 'edit_products',
      'resource', 'products',
      'action', 'edit',
      'granted', true
    )
  );
  
  RETURN result;
END;
$$;

# 3. Create alternative permissions matrix
DROP FUNCTION IF EXISTS public.get_role_permissions_matrix_alt;
CREATE FUNCTION public.get_role_permissions_matrix_alt()
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN jsonb_build_object(
    'admin', jsonb_build_object(
      'products:view', true,
      'products:edit', true,
      'products:delete', true,
      'settings:admin', true
    ),
    'editor', jsonb_build_object(
      'products:view', true,
      'products:edit', true,
      'products:delete', false,
      'settings:admin', false
    ),
    'viewer', jsonb_build_object(
      'products:view', true,
      'products:edit', false,
      'products:delete', false,
      'settings:admin', false
    )
  );
END;
$$;
```

### 2. Schema Version Table 

The schema_versions table has been created with the following structure:

```sql
CREATE TABLE IF NOT EXISTS public.schema_versions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  version TEXT NOT NULL,
  description TEXT,
  applied_at TIMESTAMPTZ DEFAULT now(),
  required BOOLEAN DEFAULT true,
  components JSONB
);

-- Create index on version for faster lookups
CREATE INDEX IF NOT EXISTS schema_versions_version_idx ON public.schema_versions (version);

-- Insert the initial version
INSERT INTO public.schema_versions (version, description, required, components)
SELECT '1.0.0', 'Initial schema version', true, '{"core":"1.0.0","stages":"1.0.0","indicators":"1.0.0","gamification":"1.0.0","products":"1.0.0","approvals":"1.0.0","deliverables":"1.0.0"}'
WHERE NOT EXISTS (SELECT 1 FROM public.schema_versions WHERE version = '1.0.0');
```

### 3. Crypto Polyfill Implementation

Multiple polyfill solutions have been implemented:

1. **Global polyfill**: Added to index.html
2. **Content script polyfill**: Added dedicated script
3. **Injection script**: Created to handle dynamic content scripts

To fix content script issues, run:

```bash
node scripts/inject-polyfill-to-content-scripts.js
```

### 4. Analytics Service Enhancement

The analytics service has been enhanced with better fallbacks:

1. First attempts to use RPC functions
2. If those fail, uses enhanced client-side implementation
3. Detailed telemetry is recorded for debugging

### 5. Application Restart

After applying all changes:

1. Stop the development server (if running)
2. Start it again:
   ```bash
   npm run dev
   ```

3. Verify in the console logs that:
   - Schema version validation is successful
   - Content scripts no longer report crypto errors 
   - RPC functions either work or properly fall back to client-side implementations

## Troubleshooting

### Database Function Issues

If you still see 404 errors for RPC functions:

1. **First solution**: Check that you applied all the simplified function versions (not the complex ones with table dependencies)
2. **Alternative solution**: Use the client-side fallbacks we've implemented

### Content Script Crypto Issues

If content scripts still have crypto errors:

1. Run the injection script: `node scripts/inject-polyfill-to-content-scripts.js`
2. Or manually add to the top of each content script:
   ```javascript
   import '/js/content-script-polyfill.js';
   ```

## Verifying Implementation

Check the following to verify the fixes:

1. **Schema Version Validation**:
   - Success message: `schemaVersioning.ts:279 Schema version validated: 1.0.0`

2. **Crypto Support**:
   - Global: `global-crypto-polyfill.js:21 [Global Polyfill] crypto.randomUUID is natively supported`
   - No content script errors for crypto.randomUUID

3. **RPC Functions**:
   - Either no 404 errors OR
   - Proper fallback message: `[Leaderboard] RPC function failed, using client fallback`

4. **Request Resilience**:
   - The application should handle network errors gracefully
   - Error messages should include details for debugging

## Next Steps

After implementing these stability fixes, consider:

1. **Further Database Optimization**:
   - Create proper tables for permissions/roles if they don't exist
   - Optimize RPC functions for production performance

2. **Enhanced Monitoring**:
   - Use the enhanced telemetry data to identify trouble spots
   - Add app-level timing metrics to identify slow queries

3. **Cross-Browser Testing**:
   - Test on multiple browsers including IE11, older Safari
   - Add additional browser-specific polyfills as needed
