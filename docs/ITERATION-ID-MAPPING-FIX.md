# Iteration ID Mapping Fix

## Overview

This fix addresses issues related to iteration ID mapping in the product stages system, specifically:

1. Repeated console warnings about iteration ID mismatches
2. Inability to handle numeric version formats (like "2.0") when UUID format is expected
3. Lack of error rate limiting causing console spam

## Problem Description

When viewing product stages with a version-style iteration ID parameter in the URL (e.g., `?iteration=2.0`), the system was attempting to find stages with that specific ID, but stages in the database use UUID format IDs (e.g., `1605293a-1ed8-4f86-9239-63e52598f322`).

This mismatch resulted in:
- Repeated console warnings
- No stages being displayed despite them existing in the database
- Poor user experience

## Implemented Fixes

### 1. Warning Rate Limiting

Added a warning limiter to prevent the same warning from appearing multiple times in the console. A unique key is generated for each warning based on the specific combination of values involved, ensuring that each unique issue is only reported once.

```typescript
const warningShownRef = useRef<Set<string>>(new Set());
// Only show the warning once per unique combination
if (!warningShownRef.current.has(warningKey)) {
  console.warn("No stages match the current iteration ID...");
  warningShownRef.current.add(warningKey);
}
```

### 2. Version-to-UUID Mapping

Added a mechanism to detect when a version-style ID is provided but a UUID exists in the database:

```typescript
// Try to map numeric version to UUID if we have data
if (availableIterationIds.length === 1 && 
    typeof productIterationId === 'string' && 
    /^\d+(\.\d+)?$/.test(productIterationId)) {
  console.log(`Detected potential version number (${productIterationId}) instead of UUID...`);
  versionToUuidMapRef.current[productIterationId] = availableIterationIds[0];
  // Update the valid iteration ID to the mapped UUID
  validIterationIdRef.current = availableIterationIds[0];
  setProductIterationId(availableIterationIds[0]);
  queryEnabledRef.current = true;
}
```

### 3. Enhanced Validation Logic

Improved the `validateIterationID` function to check for mapped versions:

```typescript
// Check if this is a version number and we have a mapping for it
if (/^\d+(\.\d+)?$/.test(id) && versionToUuidMapRef.current[id]) {
  console.log(`Using mapped UUID for version ${id}: ${versionToUuidMapRef.current[id]}`);
  return validateIterationID(versionToUuidMapRef.current[id]);
}
```

## Benefits

- **Reduced Console Noise**: Warnings now only appear once per unique scenario
- **Improved UX**: The system can now handle version-style iteration IDs in URLs
- **Automatic Recovery**: When a version ID is used, the system attempts to map it to the correct UUID
- **Better Error Handling**: More graceful failure modes when unexpected IDs are encountered

## Implementation

The fixes were applied to:
- `useProductStages.ts` - The core hook that manages product stages data and state

To apply the fix to your system, run:

```bash
node src/commands/fix-iteration-warnings.js
```

## Future Considerations

For a more comprehensive solution, consider implementing:

1. A formal mapping system between user-friendly version numbers and internal UUIDs
2. Server-side parameter validation and translation
3. A more robust URL schema that clearly distinguishes between version numbers and UUIDs
