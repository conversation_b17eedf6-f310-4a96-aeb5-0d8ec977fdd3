# Hook Migration Guide

## Overview

The codebase has been migrated from using deprecated hooks to their modern equivalents. This document outlines the changes made and provides guidance for any future development.

## Migrated Hooks

### `usePermissionsContext` → `usePermissions`

The `usePermissionsContext` hook was deprecated and has been replaced with `usePermissions`. This change improves performance and reduces console warnings.

#### Before

```tsx
import { usePermissionsContext } from '@/hooks/usePermissions';

const MyComponent = () => {
  const permissionsContext = usePermissionsContext();
  
  // Using permissionsContext
  const canEdit = permissionsContext.hasCapability('edit');
  
  return (
    // Component JSX
  );
};
```

#### After

```tsx
import { usePermissions } from '@/hooks/usePermissions';

const MyComponent = () => {
  const permissions = usePermissions();
  
  // Using permissions
  const canEdit = permissions.hasCapability('edit');
  
  return (
    // Component JSX
  );
};
```

## Migration Summary

The automatic migration process made the following changes:

- Updated 10 import statements
- Migrated 5 hook usage instances
- Renamed 7 variable references

A total of 7 files were modified during this process.

## Benefits

- Reduced console warnings
- Improved performance
- Prevention of duplicate context subscriptions
- Better code maintainability

## Future Development

When developing new components or features:

1. Always use `usePermissions` instead of `usePermissionsContext`
2. Follow the pattern of naming the returned value `permissions` instead of `permissionsContext`
3. Leverage the circuit-breaker and error handling features built into the new hook

## Components Updated

The following components were updated:

1. `src/commands/apply-product-page-fixes.js`
2. `src/commands/migrate-deprecated-hooks.js`
3. `src/components/admin/IterationTeamManagement.tsx`
4. `src/components/permissions/PermissionGate.tsx`
5. `src/components/permissions/SafePermissionGate.tsx`
6. `src/components/products/tasks/EditTaskDialog.tsx`
7. `src/components/products/tasks/EnhancedTaskDetailDialog.tsx`

## Relationship with Other Optimizations

This migration is part of a series of performance optimizations:

1. **Hook Migration**: Updating deprecated hooks to their modern equivalents
2. **Gamification Disabling**: Temporarily disabling the gamification system through stub implementations
3. **Telemetry Optimization**: Setting telemetry to be disabled by default
4. **RPC Fallbacks**: Implementing client-side fallbacks for server functions

Together, these changes significantly improve application performance and reduce console warnings.
