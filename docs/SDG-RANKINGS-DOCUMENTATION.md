# SDG Rankings Documentation

This document provides an in-depth explanation of the SDG rankings implementation in the Stat Linker Factory application.

## Overview

The SDG Rankings system calculates and displays Saudi Arabia's position relative to other countries for each Sustainable Development Goal (SDG) indicator. Rankings are calculated across three contexts:

1. **Global** - Saudi Arabia's ranking among all countries worldwide
2. **GCC** - Saudi Arabia's position within Gulf Cooperation Council countries
3. **Arab** - Saudi Arabia's standing among Arab League member states

## Ranking Calculation Logic

Rankings are calculated using the following process:

1. For each indicator and year, we retrieve Saudi Arabia's value
2. We determine if higher or lower values are better for this indicator (e.g., higher literacy rates are better, but lower mortality rates are better)
3. We sort all countries according to their values in the appropriate direction
4. We calculate Saudi Arabia's rank within this sorted list
5. We determine the percentile score (higher is better, regardless of the indicator direction)

### Direction Determination

The system automatically determines whether higher or lower values are better for each indicator using a predefined list in the `ranking-utils.ts` file. For example:

- **Lower is better** for indicators like:
  - Poverty rates (Goal 1)
  - Mortality rates (Goal 3)
  - Unemployment rates (Goal 8)
  - CO2 emissions (Goal 13)

- **Higher is better** for indicators like:
  - Education enrollment rates (Goal 4)
  - Internet access (Goal 9)
  - Forest coverage (Goal 15)

### Percentile Calculation

Percentiles are calculated as follows, ensuring that higher percentiles always indicate better performance:

```
percentile = (1 - (rank / total)) * 100
```

For example, if Saudi Arabia ranks 10th out of 100 countries, its percentile is (1 - (10/100)) * 100 = 90%, indicating it performs better than 90% of countries.

## Database Structure

Rankings are stored in the `sdg_rankings` table with the following structure:

```sql
CREATE TABLE sdg_rankings (
    indicator_code TEXT NOT NULL,
    geo_code TEXT NOT NULL, -- Country code (SAU for Saudi Arabia)
    year INTEGER NOT NULL,
    
    -- Global rankings
    global_rank INTEGER,
    global_total INTEGER,
    global_percentile NUMERIC,
    
    -- GCC rankings
    gcc_rank INTEGER,
    gcc_total INTEGER,
    gcc_percentile NUMERIC,
    
    -- Arab world rankings
    arab_rank INTEGER,
    arab_total INTEGER,
    arab_percentile NUMERIC,
    
    calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (indicator_code, geo_code, year)
);
```

## Benchmarking Logic

In addition to rankings, the system calculates regional benchmarks for comparison:

1. **GCC Benchmark** - Average and median values across GCC countries
2. **Western Asia Benchmark** - Average and median values across Western Asia
3. **World Benchmark** - Global average and median values

Benchmarks are stored in the `sdg_benchmarks` table and calculated using the `calculate_regional_benchmarks` stored function in PostgreSQL.

## Status Categorization

Based on percentile rankings, indicators are assigned a status:

| Percentile | Status | Description |
|------------|--------|-------------|
| ≥ 75% | Excellent | Saudi Arabia is among the top performers globally |
| 50-74.9% | Good | Saudi Arabia performs above the global median |
| 25-49.9% | Average | Saudi Arabia performs below the global median |
| < 25% | Poor | Saudi Arabia is among the lower performers globally |

This status is used for visual categorization in the dashboard, helping to quickly identify areas of strength and opportunity.

## Update Process

Rankings are calculated through the following methods:

1. **Manual Trigger** - Administrators can manually trigger ranking calculations via the SDG dashboard
2. **API Update** - When new data is fetched from the UN SDG API, rankings are automatically recalculated
3. **CLI Command** - Rankings can be updated using the command-line tool:
   ```bash
   node src/commands/fetch-sdg-data.js rankings year=2023
   ```

## Country Groupings

The following country groups are used for regional rankings:

### GCC Countries
- Bahrain (BHR)
- Kuwait (KWT)
- Oman (OMN)
- Qatar (QAT)
- Saudi Arabia (SAU)
- United Arab Emirates (ARE)

### Arab League Countries
- Algeria (DZA)
- Bahrain (BHR)
- Comoros (COM)
- Djibouti (DJI)
- Egypt (EGY)
- Iraq (IRQ)
- Jordan (JOR)
- Kuwait (KWT)
- Lebanon (LBN)
- Libya (LBY)
- Mauritania (MRT)
- Morocco (MAR)
- Oman (OMN)
- Palestine (PSE)
- Qatar (QAT)
- Saudi Arabia (SAU)
- Somalia (SOM)
- Sudan (SDN)
- Syria (SYR)
- Tunisia (TUN)
- United Arab Emirates (ARE)
- Yemen (YEM)

## Visualization and Reporting

The rankings are visualized in multiple ways:

1. **Indicator Cards** - Each indicator card displays the global rank and percentile
2. **Detailed View** - The indicator detail dialog shows all three ranking contexts (global, GCC, Arab)
3. **Trend Analysis** - Future versions will include historical tracking of rankings over time

## Implementation Details

The rankings are calculated using the `calculate_indicator_ranking` PostgreSQL function. This function:

1. Takes an indicator code, year, and sort direction as parameters
2. Optionally accepts a filter to limit to specific country groups
3. Returns Saudi Arabia's rank and the total number of countries with data

Example call:
```sql
SELECT * FROM calculate_indicator_ranking(
    '3.1.1',  -- Indicator code
    2023,     -- Year
    'ASC',    -- Direction (ASC for indicators where lower is better)
    'geo_code IN (''BHR'', ''KWT'', ''OMN'', ''QAT'', ''SAU'', ''ARE'')'  -- GCC filter
);
```

## Technical Considerations

1. **Missing Data** - Countries missing data for a specific indicator/year are excluded from rankings
2. **Tied Values** - Countries with identical values receive the same rank (standard SQL RANK function behavior)
3. **Consistency Check** - After calculations, a consistency check ensures percentiles match ranks
4. **API Results Storage** - Raw UN SDG API responses are stored in the `sdg-api-results` bucket for audit purposes

## Further Development

Future enhancements to the ranking system include:

1. **Weighted Composite Scores** - Creating aggregate scores across multiple indicators
2. **Dynamic Time Periods** - Allowing rankings across custom time ranges (e.g., 5-year averages)
3. **Goal-Level Rankings** - Aggregating indicators to provide overall goal-level rankings
4. **Progress Tracking** - Year-over-year change in rankings and percentiles
5. **Regional Customization** - Adding more regional groupings for specialized analysis
