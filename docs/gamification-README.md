# Gamification System README

The gamification system is fully integrated into the StatLinker Factory application. This README provides a quick guide on how to use and extend the system.

## Quick Start

### Setup

The gamification system is automatically initialized during application startup through the `databaseBootstrap.ts` process. However, if you need to manually set up or reset the system, run:

```bash
node src/commands/setup-gamification-complete.js
```

### Using Gamification in Components

1. **Import the hook**:
```tsx
import { useGamification } from '@/hooks/useGamification';
```

2. **Use gamification features**:
```tsx
function MyComponent() {
  const { 
    userProfile,       // Current user's gamification profile
    achievements,      // All available achievements
    leaderboard,       // Current leaderboard data
    trackEvent,        // Track a gamification event
    addPoints,         // Manually add points to user
    grantAchievement,  // Manually grant an achievement
    isEnabled          // Is gamification enabled?
  } = useGamification();
  
  // Example: Track an event when a user performs an action
  const handleAction = () => {
    trackEvent('action_completed', { actionId: '123' });
  };
  
  return (
    <div>
      {isEnabled && userProfile && (
        <div>
          <p>Level: {userProfile.level}</p>
          <p>Points: {userProfile.points}</p>
          <button onClick={handleAction}>Complete Action</button>
        </div>
      )}
    </div>
  );
}
```

### Ready-to-Use Components

The system includes several ready-to-use components:

```tsx
// User profile card with level and points
import { UserProfileCard } from '@/components/gamification/UserProfileCard';

// User achievements list/grid
import { UserAchievements } from '@/components/gamification/UserAchievements';

// Display user progress towards next level
import { UserProgressCard } from '@/components/gamification/UserProgressCard';

// Achievements display for a specific category
import { AchievementsDisplay } from '@/components/gamification/AchievementsDisplay';

// Leaderboard component
import { Leaderboard } from '@/components/gamification/Leaderboard';
```

## Core Concepts

### Events

The gamification system is event-driven. Instead of directly granting points or achievements, you typically track events that may trigger rules:

```tsx
// Track that a user created a product
trackEvent('product_created', { 
  productId: '123',
  productType: 'statistical' 
});

// Track that a user updated their profile
trackEvent('profile_updated', { 
  completionPercentage: 100  
});
```

### Rules

Rules automatically evaluate events and can grant points, achievements, or trigger other actions. Rules are defined in the database and can be managed through the admin interface.

Example rule (simplified):
```json
{
  "name": "First Product",
  "description": "Created your first statistical product",
  "conditions": [
    { "type": "event", "operator": "equals", "target": "product_created" },
    { "type": "custom", "operator": "equals", "target": "products_count", "value": 1 }
  ],
  "actions": [
    { "type": "add_points", "points": 150 },
    { "type": "grant_achievement", "achievement_id": "first_product" }
  ]
}
```

### Achievements

Achievements represent accomplishments users can earn. They can be granted:
1. Automatically through rules (recommended)
2. Manually via code using `grantAchievement`

## Admin Interface

The gamification admin interface is available for users with admin privileges:

```tsx
// In an admin page component:
import { GamificationAdmin } from '@/components/admin/GamificationAdmin';

function AdminPage() {
  return <GamificationAdmin />;
}
```

The admin interface provides:
- Achievement management 
- Rule configuration
- System settings
- Analytics dashboard

## Best Practices

1. **Use events over direct actions**: Track events and let rules handle the logic instead of directly adding points.
2. **Keep achievements meaningful**: Design achievements that encourage valuable user behaviors.
3. **Make sure notification events are visible**: Place the `GamificationNotifier` component at the root level.
4. **Use code-splitting**: The gamification components support code-splitting to minimize bundle size.
5. **Design for progression**: Create achievements with increasing difficulty levels.

## Troubleshooting

### Common Issues

1. **Events Not Triggering Rules**:
   - Ensure the event type exactly matches the rule condition
   - Check that all conditions are being met
   - Verify the rule is active in the database

2. **Components Not Showing**:
   - Check that `isEnabled` is true (from useGamification)
   - Verify the user is authenticated
   - Check that the necessary data is being loaded

3. **SQL Functions Missing**:
   - Run the complete setup script again

4. **Database Connection Issues**:
   - Ensure Supabase connection is correctly configured
   - Verify RLS policies allowing access

## System Architecture

The gamification system uses a layered architecture:

```
UI Components → Hooks → Services → Database
```

- **Components**: React components for displaying gamification UI elements
- **Hooks**: React hooks for providing data and functionality to components
- **Services**: Core business logic and data access
- **Database**: Supabase tables, functions, and RLS policies

## Extending the System

To extend the gamification system:

1. **Add new event types**: Track new types of user actions
2. **Create custom rules**: Define new rules in the admin interface
3. **Add custom achievement categories**: Group achievements by theme
4. **Implement new gamification mechanics**: Add features like streaks, challenges, etc.
5. **Expand analytics**: Add new reports or visualizations to the admin dashboard

## Further Documentation

For more detailed information, see:
- [Full Gamification System Documentation](./gamification-system.md)
- [Database Schema](./gamification-schema.md)
- [API Reference](./gamification-api.md)
