# Force RPC Fallbacks Guide

This document explains how to use client-side fallbacks when server-side RPC functions are returning 404 errors.

## Problem

Even after fixing offline mode issues, you might still encounter these RPC function errors:
- 404 Not Found errors for `get_leaderboard`
- 404 Not Found errors for `get_role_permissions_matrix`
- 404 Not Found errors for `get_role_permissions_matrix_alt`
- 404 Not Found errors for `get_dynamic_user_capabilities`

This can happen when:
1. The server functions don't actually exist in your Supabase instance
2. There are permission issues that prevent accessing these functions
3. Something is intercepting/caching the requests incorrectly

## Solution: Force Client-Side Fallbacks

The application includes built-in client-side implementations of all critical RPC functions. These fallbacks are designed to provide reasonable default behavior when the server-side functions are unavailable.

We've created a tool to easily enable these fallbacks:

### Using the Force RPC Fallbacks Tool

1. Open `/force-rpc-fallbacks.html` in your browser
2. Click the "Force RPC Fallbacks" button
3. Reload your application

This tool:
- Sets a `force_rpc_fallbacks` flag in localStorage
- Patches the `fetch()` function to intercept RPC calls
- Returns mock data for known RPC functions

## Technical Implementation

### How It Works

When enabled:

1. The tool overrides the browser's `fetch()` function
2. It intercepts any requests to `*/rest/v1/rpc/*` endpoints
3. For known RPC functions, it returns mock successful responses
4. For other requests, it uses the original `fetch()` function

### Client-Side Fallback Functions

The original fallback implementations are defined in `src/services/rpc-fallbacks.ts`:

```typescript
// Fallback for get_leaderboard
export async function getLeaderboardFallback(limit: number = 10, offset: number = 0) {
  // Returns simulated leaderboard data
}

// Fallback for get_role_permissions_matrix
export async function getRolePermissionsMatrixFallback() {
  // Returns simulated permission matrix
}

// Fallback for get_role_permissions_matrix_alt
export async function getRolePermissionsMatrixAltFallback() {
  // Returns alternative format of permission matrix
}

// Fallback for get_dynamic_user_capabilities
export async function getDynamicUserCapabilitiesFallback(user_id?: string) {
  // Returns simulated user capabilities
}
```

## Limitations

Using client-side fallbacks has limitations:

1. **Limited Data**: Fallbacks provide minimal mock data, not real database content
2. **Not Persistent**: Changes made through the application won't be saved
3. **Reduced Functionality**: Some advanced features may not work

## Reverting to Normal Operation

When server-side RPC functions become available again:

1. Open `/force-rpc-fallbacks.html` in your browser
2. Click the "Reset to Normal Operation" button
3. Reload your application

## Recommended Approach

This solution is intended as a temporary workaround while the server-side issues are being addressed. The recommended long-term solution is to:

1. Apply all the RPC function fixes:
   - `src/commands/apply-rpc-functions.js`
   - `src/commands/apply-anon-access-rpc-functions.js`
   - `src/commands/fix-remaining-rpc-functions.js`
   - `src/commands/fix-gamification-profile-tables.js`

2. Verify that offline mode is disabled using the `/fix-offline-mode.html` tool

3. Clear browser cache using the `/clear-cache-fix.html` tool

If the server-side functions remain unavailable after these steps, use this fallback solution as a temporary measure.
