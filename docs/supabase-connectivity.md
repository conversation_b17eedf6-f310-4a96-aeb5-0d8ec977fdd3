# Supabase Connectivity Guide

This document provides information about the Supabase connectivity features and troubleshooting utilities available in this application.

## Overview

The application has been designed to work with Supabase as its backend database and authentication service. It includes features to:

1. Detect and handle connectivity issues gracefully
2. Provide offline mode for development and testing
3. Offer utilities for troubleshooting connection problems

## Environment Configuration

Supabase connection details are stored in the `.env` file at the project root:

```
# Supabase credentials
VITE_SUPABASE_URL=https://wgsnpiskyczxhojlrwtr.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Connectivity Management Tools

Two browser-based tools are provided for managing Supabase connectivity:

### 1. Toggle Offline Mode

**URL:** http://localhost:8081/toggle-offline-mode.html

This tool allows you to:
- Enable/disable offline mode
- Test connectivity to the Supabase server
- View current connection status
- Clear localStorage items

When offline mode is enabled, the application will use a mock Supabase client that returns predefined responses without making actual network requests.

### 2. Reset Supabase Connection

**URL:** http://localhost:8081/reset-supabase.html

This tool helps reset the Supabase connection state by:
- Clearing all Supabase-related localStorage items
- Resetting authentication state
- Providing a clean slate for testing

## How Connectivity Works

1. **Initial Load Check**: 
   - On application startup, a connectivity check is performed to the Supabase server
   - The check uses a health endpoint to verify server availability
   - Even a 401 (Unauthorized) response is considered a successful connection, as it indicates the server is responding

2. **Graceful Degradation**:
   - If connectivity issues are detected, the application automatically enters offline mode
   - Database operations are handled by the mock client
   - UI components adapt to show offline state

3. **Database Bootstrapping**:
   - In offline mode, database structure verification is skipped
   - This prevents errors when tables can't be accessed

## Troubleshooting

If you experience connection issues:

1. Check that the `.env` file contains the correct Supabase URL and anon key
2. Visit the Toggle Offline Mode tool and run the connectivity test
3. Try clearing localStorage using the Reset Supabase Connection tool
4. Check browser console for specific error messages

Common issues:

- **401 Unauthorized**: The server exists but the anon key might be invalid
- **Network errors**: Check your internet connection or VPN settings
- **CORS errors**: These can occur during local development

## Browser Compatibility

The application includes polyfills for:
- `crypto.randomUUID`: For older browsers that don't support this function

These polyfills are loaded automatically when the application starts.

## Implementation Details

Key files involved in Supabase connectivity:

- `src/integrations/supabase/gateway.ts`: Central gateway for all Supabase interactions
- `src/lib/databaseBootstrap.ts`: Database structure verification and initialization
- `public/js/polyfills.js`: Browser compatibility polyfills
- `public/toggle-offline-mode.html`: Connectivity management tool
- `public/reset-supabase.html`: Connection reset tool

The mock Supabase client implementation in offline mode provides stubs for all commonly used Supabase methods, including `from()`, `rpc()`, and various authentication functions.
