# SDG API Integration Complete

This document provides an overview of the completed UN Sustainable Development Goals (SDG) API integration in the Stat Linker Factory application.

## Overview

The integration enables the application to:

1. Fetch and store SDG goals, targets, indicators, and observations from the UN SDG API
2. Calculate country rankings and regional benchmarks for indicators
3. Compare Saudi Arabia's performance against global and regional averages
4. Support data retrieval via React hooks for UI components

## Architecture

The integration follows a clean architecture pattern:

```mermaid
graph TD
    A[UN SDG API] --> B[Edge Function]
    B --> C[PostgreSQL Database]
    C --> D[Frontend Components]
    
    B -- "ETL Operations" --- E[Command Line Tools]
    C -- "Centralized Supabase Gateway" --- F[Service Layer]
    F --- G[React Hooks]
    G --- H[UI Components]
```

## Components

### Database

- **Tables:** `sdg_goals`, `sdg_targets`, `sdg_indicators`, `sdg_observations`, `sdg_rankings`, `sdg_benchmarks`
- **Storage:** Raw API responses stored in `sdg-api-results` bucket

### Edge Function

- **Path:** `supabase/functions/sdg-etl`
- **Actions:** 
  - `test` - Simple connectivity test
  - `goals` - Fetch and store SDG goals
  - `targets` - Fetch and store targets for a goal
  - `indicators` - Fetch and store indicators for a target
  - `observations` - Fetch and store data points
  - `rankings` - Calculate rankings and benchmarks
  - `all` - Process all data types

### Service Layer

- **Path:** `src/services/sdg`
- **Gateway Integration:** Uses the centralized Supabase gateway pattern
- **Methods:**
  - Data retrieval (goals, targets, indicators, observations, rankings)
  - Data processing operations (refresh, calculate rankings, etc.)

### React Hooks

- **Path:** `src/hooks/useSDGData.ts`
- **Features:**
  - Provides UI components with access to SDG data
  - Includes filtering, sorting, and data manipulation
  - React Query integration for efficient data handling

### Command Line Tools

- **Path:** `src/commands/fetch-sdg-data.js`
- **Features:**
  - CLI for data operations
  - Testing and verification
  - Can be scheduled for automated updates

### Deployment Script

- **Path:** `scripts/deploy-sdg-edge-function.js`
- **Features:**
  - Environment variable validation
  - Supabase setup and verification
  - Edge function deployment
  - Automated testing

## Security

- **Authorization:** Uses `VITE_EDGE_FUNCTION_SECRET` for Edge Function access
- **Environment Variables:** Protected and not exposed to client
- **Database Access:** Tables have appropriate permissions setup

## Usage

### Command Line

```bash
# Fetch all SDG data
node src/commands/fetch-sdg-data.js all

# Calculate rankings with benchmarks for previous year
node src/commands/fetch-sdg-data.js rankings calculateBenchmarks=true

# Deploy Edge Function
node scripts/deploy-sdg-edge-function.js
```

### React Components

```tsx
import { useSDGData } from '@/hooks/useSDGData';

function SDGDataComponent() {
  const { 
    goals,
    indicators, 
    loadIndicators,
    updateFilters,
    isLoading,
    error 
  } = useSDGData();
  
  // Component logic
}
```

## Key Implementation Details

1. **Centralized Gateway Pattern:** Updated from direct Supabase client usage to the centralized gateway pattern for consistency with the rest of the application.

2. **TypeScript Integration:** Strong typing across the entire feature set.

3. **Robust Error Handling:**
   - Logging in Edge Function
   - Client-side error boundaries
   - Graceful fallbacks

4. **Testing Support:** Includes test endpoints and framework.

## Future Enhancements

1. **Scheduled Updates:** Configure automatic weekly data refreshes
2. **Visual Reporting:** Enhanced visualizations and comparative analysis
3. **Data Export:** Support for CSV and Excel export formats
4. **Custom Benchmarks:** Allow defining custom country groupings for comparison

## References

- [SDG API Documentation](docs/SDG-API-INTEGRATION.md)
- [SDG Rankings Documentation](docs/SDG-RANKINGS-DOCUMENTATION.md)
- [Environment Variables Guide](docs/ENVIRONMENT-VARIABLES-GUIDE.md)
- [UN SDG API Reference](https://unstats.un.org/sdgapi/swagger/)
