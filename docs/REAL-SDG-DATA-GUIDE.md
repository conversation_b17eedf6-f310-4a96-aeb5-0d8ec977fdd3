# Guide to Fetching Real SDG Data

This document explains how to populate the SDG dashboard with real data from the United Nations Sustainable Development Goals API.

## Overview

The SDG dashboard currently uses sample data for demonstration purposes. To fetch real data from the official UN SDG API, we've implemented a comprehensive data pipeline that:

1. Fetches data from the UN SDG API
2. Stores it in the Supabase database
3. Calculates benchmarks and rankings
4. Applies consistency rules to ensure logical ranking hierarchies

## Prerequisites

Before running the data fetch process, ensure you have:

1. Supabase project set up with the required tables and functions
2. Edge Function deployed (`sdg-etl`)
3. Environment variables properly configured:
   - `NEXT_PUBLIC_SUPABASE_URL`
   - `NEXT_PUBLIC_SUPABASE_ANON_KEY`
   - `EDGE_FUNCTION_SECRET`

## Fetching Real Data

### Option 1: Use the Command Line Script

We've created a Node.js script to fetch real SDG data:

```bash
# Make the script executable
chmod +x src/commands/fetch-sdg-data.js

# Run the script
node src/commands/fetch-sdg-data.js
```

This script will:
- Fetch SDG goals, targets, and indicators metadata
- Fetch observations for key Goal 3 indicators (health)
- Calculate benchmarks and rankings
- Apply consistency rules

### Option 2: Use the Dashboard UI

1. Go to the SDG Dashboard page
2. Click the "Sync Data" button in the top right corner
3. Wait for the data to be fetched and processed
4. The dashboard will automatically refresh with real data

## Data Flow

```mermaid
flowchart TD
    A[UN SDG API] -->|Fetch| B[Edge Function]
    B -->|Store| C[Supabase Database]
    C -->|Calculate| D[Benchmarks]
    C -->|Calculate| E[Rankings]
    E -->|Apply| F[Consistency Rules]
    C -->|Read| G[SDG Dashboard UI]
```

## Understanding the Data Structure

The UN SDG API provides data for 193 countries across all SDG indicators. Our system:

1. Focuses on Goal 3 (Health and Well-being) indicators
2. Calculates Saudi Arabia's position relative to:
   - Global community (193 countries)
   - GCC countries (6 countries)
   - Arab League countries (22 countries)

## Ranking Calculation Methodology

Rankings are calculated based on indicator direction:
- Some indicators are "ascending" (lower values are better, e.g., mortality rates)
- Some indicators are "descending" (higher values are better, e.g., vaccination rates)

The system automatically determines the correct direction for each indicator and ranks accordingly.

## Consistency Rules

The ranking system maintains logical consistency:
1. If Saudi Arabia ranks #1 in the Arab world, it must rank #1 in the GCC (subset)
2. Saudi Arabia cannot have a better rank in a larger group than in a smaller group

## Troubleshooting

If you encounter issues fetching data:

1. **API Availability**: Check if the UN SDG API is available (https://unstats.un.org/sdgapi/)
2. **Missing Data**: Some indicators may not have data for all countries or years
3. **Edge Function Logs**: Check the Supabase Edge Function logs for errors
4. **Database Constraints**: Ensure all database migrations have been applied

## Scheduled Updates

For production environments, configure scheduled updates:

```bash
# Create a cron job to run monthly
0 0 1 * * /usr/bin/node /path/to/fetch-sdg-data.js >> /var/log/sdg-updates.log 2>&1
```

## Additional Resources

- [UN SDG API Documentation](https://unstats.un.org/sdgs/metadata/)
- [Supabase Edge Functions Guide](https://supabase.io/docs/guides/functions)
- [SDG Rankings Documentation](./SDG-RANKINGS-DOCUMENTATION.md)
