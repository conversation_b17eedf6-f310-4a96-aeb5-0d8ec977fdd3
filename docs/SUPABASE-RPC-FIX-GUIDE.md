# Supabase RPC Functions Fix Guide

This guide provides step-by-step instructions for manually applying the SQL fixes to resolve performance issues and errors related to missing RPC functions.

## Problem Overview

You're seeing the following errors in your application:

1. 404 Not Found errors for RPC functions:
   ```
   POST .../rest/v1/rpc/get_leaderboard 404 (Not Found)
   POST .../rest/v1/rpc/get_role_permissions_matrix 404 (Not Found)
   POST .../rest/v1/rpc/get_role_permissions_matrix_alt 404 (Not Found)
   ```

2. Ambiguous column error:
   ```
   Error getting user capabilities: column reference "role_id" is ambiguous
   POST .../rest/v1/rpc/get_dynamic_user_capabilities 400 (Bad Request)
   ```

These errors occur because the database functions are missing or have issues.

## Manual Fix Instructions

Since the automated script couldn't execute without `psql`, follow these manual steps:

### Step 1: Access the Supabase Dashboard

1. Go to [app.supabase.io](https://app.supabase.io/)
2. Sign in with your Supabase account
3. Select your project: `wgsnpiskyczxhojlrwtr`

### Step 2: Use the SQL Editor

1. From the left navigation menu, select "SQL Editor"
2. Click "New query" to create a new SQL query

### Step 3: Apply the Missing RPC Functions

1. Copy this entire SQL block:

```sql
-- Function: get_leaderboard(integer, integer)
CREATE OR REPLACE FUNCTION get_leaderboard(
  p_limit integer DEFAULT 10,
  p_offset integer DEFAULT 0
) RETURNS TABLE (
  rank integer,
  user_id uuid,
  display_name text,
  points integer,
  level integer
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  RETURN QUERY
  SELECT
    ROW_NUMBER() OVER (ORDER BY ugp.points DESC) AS rank,
    u.id AS user_id,
    COALESCE(u.raw_user_meta_data->>'full_name', u.email) AS display_name,
    ugp.points,
    ugp.level
  FROM
    user_gamification_profiles ugp
  JOIN
    auth.users u ON ugp.user_id = u.id
  ORDER BY
    ugp.points DESC
  LIMIT p_limit
  OFFSET p_offset;
END;
$$;

-- Function: get_role_permissions_matrix()
CREATE OR REPLACE FUNCTION get_role_permissions_matrix()
RETURNS TABLE (
  role_id uuid,
  role_name text,
  capability_id uuid,
  capability_code text,
  capability_name text,
  is_granted boolean
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  RETURN QUERY
  SELECT
    r.id AS role_id,
    r.name AS role_name,
    c.id AS capability_id,
    c.code AS capability_code,
    c.name AS capability_name,
    COALESCE(rc.is_granted, false) AS is_granted
  FROM
    dynamic_roles r
  CROSS JOIN
    capabilities c
  LEFT JOIN
    role_capabilities rc ON r.id = rc.role_id AND c.id = rc.capability_id
  ORDER BY
    r.name, c.category, c.name;
END;
$$;

-- Function: get_role_permissions_matrix_alt()
CREATE OR REPLACE FUNCTION get_role_permissions_matrix_alt()
RETURNS TABLE (
  role_id uuid,
  role_name text,
  capabilities jsonb
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  RETURN QUERY
  SELECT
    r.id AS role_id,
    r.name AS role_name,
    jsonb_object_agg(
      c.code,
      jsonb_build_object(
        'id', c.id,
        'name', c.name,
        'category', c.category,
        'is_granted', COALESCE(rc.is_granted, false)
      )
    ) AS capabilities
  FROM
    dynamic_roles r
  CROSS JOIN
    capabilities c
  LEFT JOIN
    role_capabilities rc ON r.id = rc.role_id AND c.id = rc.capability_id
  GROUP BY
    r.id, r.name
  ORDER BY
    r.name;
END;
$$;

-- Ensure security for the functions
ALTER FUNCTION get_leaderboard(integer, integer) SECURITY DEFINER SET search_path = public;
ALTER FUNCTION get_role_permissions_matrix() SECURITY DEFINER SET search_path = public;
ALTER FUNCTION get_role_permissions_matrix_alt() SECURITY DEFINER SET search_path = public;

-- Grant execution permission to authenticated users
GRANT EXECUTE ON FUNCTION get_leaderboard(integer, integer) TO authenticated;
GRANT EXECUTE ON FUNCTION get_role_permissions_matrix() TO authenticated;
GRANT EXECUTE ON FUNCTION get_role_permissions_matrix_alt() TO authenticated;
```

2. Paste it into the SQL editor
3. Click "Run" to execute the SQL

### Step 4: Fix the Ambiguous Column Issue

1. Create a new SQL query
2. Copy this entire SQL block:

```sql
-- Function: get_dynamic_user_capabilities(uuid)
CREATE OR REPLACE FUNCTION get_dynamic_user_capabilities(
  p_user_id uuid
) RETURNS TABLE (
  role_id uuid,
  role_name text,
  capability_id uuid,
  capability_code text,
  capability_name text,
  capability_category text,
  is_granted boolean
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  RETURN QUERY
  SELECT
    r.id AS role_id,
    r.name AS role_name,
    c.id AS capability_id,
    c.code AS capability_code,
    c.name AS capability_name,
    c.category AS capability_category,
    COALESCE(rc.is_granted, false) AS is_granted
  FROM
    user_roles ur
  JOIN
    dynamic_roles r ON ur.role_id = r.id
  CROSS JOIN
    capabilities c
  LEFT JOIN
    role_capabilities rc ON r.id = rc.role_id AND c.id = rc.capability_id
  WHERE
    ur.user_id = p_user_id
  ORDER BY
    r.name, c.category, c.name;
END;
$$;

-- Ensure security for the functions
ALTER FUNCTION get_dynamic_user_capabilities(uuid) SECURITY DEFINER SET search_path = public;

-- Grant execution permission to authenticated users
GRANT EXECUTE ON FUNCTION get_dynamic_user_capabilities(uuid) TO authenticated;
```

3. Paste it into the SQL editor
4. Click "Run" to execute the SQL

### Step 5: Verify the Functions Were Created

1. Create a new SQL query
2. Copy and paste the following SQL:

```sql
SELECT 
  proname as function_name, 
  pg_get_function_arguments(pg_proc.oid) as arguments
FROM 
  pg_proc 
JOIN 
  pg_namespace ON pg_namespace.oid = pg_proc.pronamespace
WHERE 
  proname IN ('get_leaderboard', 'get_role_permissions_matrix', 'get_role_permissions_matrix_alt', 'get_dynamic_user_capabilities')
  AND pg_namespace.nspname = 'public';
```

3. Run this query to verify all the functions were created
4. You should see all four functions listed in the results

### Step 6: Test the Application

1. Return to your application
2. Refresh the page
3. Open the browser console (F12)
4. Verify that the errors have been resolved
5. Check if the application performance has improved

## What If Something Goes Wrong?

If you encounter errors when running the SQL:

1. Check the error message for any clues about the issue
2. Common errors include:
   - The function already exists (this is fine)
   - Missing dependent tables (like `user_gamification_profiles`)
   - Permissions issues

If the tables don't exist in your database, don't worry - the application has fallbacks. However, you might see different errors in that case.

## Alternative: Use Client-Side Fallbacks

Even without applying these SQL fixes, the application should continue to function because it has client-side fallbacks for all operations. However, for optimal performance, having the actual database functions in place is recommended.

You can confirm that the client-side fallbacks are working by checking that:
- The application loads without errors (even if some are in the console)
- Navigation between pages works
- Products can be viewed and managed

## Need Help?

If you still have issues after following this guide, refer to:
- `docs/PERFORMANCE-FIXES-SUMMARY.md` for more details on performance issues
- `docs/MANUAL-RPC-DEPLOYMENT.md` for additional deployment information
