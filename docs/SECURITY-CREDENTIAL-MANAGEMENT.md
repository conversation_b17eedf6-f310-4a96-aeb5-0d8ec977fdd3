# Security Credential Management Guide

## Overview

This document provides guidelines for secure credential management in the Stat-Linker-Factory application. Following these best practices helps prevent accidental exposure of sensitive information like API keys, tokens, and secrets.

## Secure Environment Variable Handling

### File Structure

1. **`.env.example`**: Template file with placeholder values, committed to version control
2. **`.env`**: Contains actual credentials, **never committed** to version control
3. **`.env.local`**: Local-only credentials, **never committed** to version control (used for development)

### Setting Up Environment Variables

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Fill in the actual values for your environment in the `.env` file

3. Ensure `.env` and other secret-containing files are in `.gitignore`

## Credentials Used in the Project

| Environment Variable | Purpose | Usage Location |
|---------------------|---------|----------------|
| `VITE_SUPABASE_URL` | Supabase project URL | Frontend API access |
| `VITE_SUPABASE_ANON_KEY` | Anonymous API key | Public frontend API access |
| `SUPABASE_SERVICE_ROLE_KEY` | Admin API key | Server-side operations, edge functions |
| `VITE_EDGE_FUNCTION_SECRET` | Auth for SDG Edge Function | Edge function authentication |

## Security Best Practices

1. **Role-Appropriate Keys**:
   - Only use the `SUPABASE_SERVICE_ROLE_KEY` for server-side operations
   - Client-side code should only use `VITE_SUPABASE_ANON_KEY`

2. **Environment Variable Naming**:
   - Prefix with `VITE_` for variables that should be available to frontend code
   - Variables without `VITE_` will not be exposed to browser code

3. **Secret Masking**:
   - When logging credentials, always mask them (e.g., `abc123xyz` → `abc***xyz`)
   - Avoid printing credentials to console logs

4. **File Permissions**:
   - Apply restrictive file permissions to files containing secrets:
     ```javascript
     fs.writeFileSync(path.join(dir, '.env'), content, { mode: 0o600 });
     ```

5. **Secret Rotation**:
   - Rotate secrets regularly
   - Update application secrets immediately if a breach is suspected

## Edge Function Security

For edge functions that require credentials:

1. Create `.env` files with restricted permissions (0o600)
2. Use masking when logging credential information
3. Don't commit edge function `.env` files to version control

## Credential Rotation Procedure

When rotating credentials:

1. Generate new credentials in the Supabase dashboard
2. Update `.env` files with new values
3. Deploy updated edge functions with new credentials
4. Monitor the system to ensure everything works correctly

## In Case of Credential Exposure

If credentials are accidentally exposed:

1. Immediately rotate all exposed credentials
2. Check commit history and remove any commits containing secrets
3. Force push clean history if necessary
4. Review access logs for unusual activity
