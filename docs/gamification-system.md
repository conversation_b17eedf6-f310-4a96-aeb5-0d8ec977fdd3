# Gamification System

The gamification system provides a flexible framework for adding game mechanics to the StatLinker Factory application. It includes user achievements, points, levels, leaderboards, and an admin interface for managing the system.

## Architecture

The gamification system is built with the following components:

1. **Database Layer**: Supabase tables and views for storing gamification data
2. **Service Layer**: TypeScript services for interacting with the database
3. **React Hooks**: Custom hooks for using gamification in components
4. **UI Components**: Ready-to-use React components for displaying gamification elements
5. **Admin Panel**: Interface for managing achievements, rules, and viewing analytics

```mermaid
graph TD
    DB[(Supabase Database)] --> GS[Gamification Services]
    GS --> UH[useGamification Hook]
    UH --> UC[User Components]
    GS --> AC[Admin Components]
    
    UC --> UA[UserAchievements]
    UC --> UPC[UserProfileCard]
    UC --> LB[Leaderboard]
    UC --> GN[GamificationNotifier]
    
    AC --> CF[ConfigurationPanel]
    AC --> AM[AchievementManager]
    AC --> RE[RuleEditor]
    AC --> AD[AnalyticsDashboard]
```

## Setup and Installation

### 1. Database Setup

Run the following command to set up the entire gamification system:

```bash
node src/commands/setup-gamification-complete.js
```

This single command consolidates all necessary setup operations:
- Creates all required database tables
- Sets up SQL functions for analytics
- Initializes default configuration
- Creates default achievements and rules

This creates the following tables:

- `user_gamification_profiles` - User points and levels
- `achievements` - Available achievements
- `user_achievements` - Junction table for earned achievements
- `gamification_events` - Event tracking
- `gamification_config` - System configuration
- `gamification_rules` - Rules for automatic achievement granting
- Admin tables (stats, logs, etc.)

### 2. Component Integration

#### Basic User Profile

Add the user profile card to display the user's level and achievements:

```tsx
import { UserProfileCard } from '@/components/gamification/UserProfileCard';

function UserDashboard() {
  return (
    <div className="dashboard-layout">
      <UserProfileCard />
      {/* Other dashboard content */}
    </div>
  );
}
```

#### Leaderboard

Add a leaderboard to show top users:

```tsx
import { Leaderboard } from '@/components/gamification/Leaderboard';

function CommunityPage() {
  return (
    <div className="community-layout">
      <Leaderboard limit={10} />
      {/* Other community content */}
    </div>
  );
}
```

#### Achievement Display

Display user achievements or all available achievements:

```tsx
import { UserAchievements } from '@/components/gamification/UserAchievements';

function UserProfilePage() {
  return (
    <div className="profile-layout">
      <UserAchievements showAll={false} limit={5} />
      {/* Other profile content */}
    </div>
  );
}
```

#### Notifications

Add gamification notifications to your app:

```tsx
// In your root App component
import { GamificationNotifier, GamificationToaster } from '@/components/gamification/GamificationNotifier';

function App() {
  return (
    <>
      <GamificationNotifier enableNotifications={true} />
      <GamificationToaster />
      {/* Your app content */}
    </>
  );
}
```

#### Admin Panel

Add the admin panel to a secure admin section of your app:

```tsx
import { GamificationAdmin } from '@/components/admin/GamificationAdmin';

function AdminPage() {
  return (
    <div className="admin-layout">
      <GamificationAdmin />
    </div>
  );
}
```

## Tracking Events

You can track user events to trigger gamification rules:

```tsx
import { useGamification } from '@/hooks/useGamification';

function ProductComponent({ product }) {
  const { trackEvent } = useGamification();
  
  const handleViewProduct = () => {
    // Track that user viewed a product
    trackEvent('product_viewed', { 
      product_id: product.id,
      product_name: product.name
    });
    
    // Your other logic...
  };
  
  return (
    <div onClick={handleViewProduct}>
      {/* Product content */}
    </div>
  );
}
```

## Manually Awarding Points/Achievements

You can manually award points or achievements:

```tsx
import { useGamification } from '@/hooks/useGamification';

function SurveyComponent() {
  const { addPoints, grantAchievement } = useGamification();
  
  const handleSurveyComplete = () => {
    // Award points for completing a survey
    addPoints(20, 'Completed survey');
    
    // Grant an achievement if this is their first survey
    if (isFirstSurvey) {
      grantAchievement('survey_master');
    }
  };
  
  return (
    <form onSubmit={handleSurveyComplete}>
      {/* Survey form */}
    </form>
  );
}
```

## Configuration

The gamification system is highly configurable through the admin interface. Key configuration options include:

- **Master Switch**: Enable/disable the entire gamification system
- **Level Formula**: Customize how points translate to levels
- **Notification Settings**: Control which notifications appear
- **Rules Engine**: Create dynamic rules for automatically granting achievements and points

## Best Practices

1. **Use Events**: Track meaningful user actions as events rather than directly granting achievements
2. **Meaningful Achievements**: Create achievements that encourage desired behaviors
3. **Regular Updates**: Add new achievements and adjust rules periodically to keep users engaged
4. **Balance Points**: Ensure point values are balanced relative to the effort required
5. **Multiple Categories**: Create achievements across different categories to engage different user types
6. **Clear Visibility**: Make gamification elements visible but not intrusive
7. **Performance**: Use the built-in React Query caching to minimize database calls

## Extending the System

The gamification system is designed to be extended. Common extensions include:

1. **Badges/Tiers**: Extend achievements with badge levels (bronze, silver, gold)
2. **Rewards**: Add a rewards marketplace for spending points
3. **Teams/Groups**: Implement team-based gamification metrics
4. **Challenges**: Add time-limited challenges
5. **Streaks**: Track consecutive days of activity

## Troubleshooting

### Common Issues

1. **Events Not Triggering Rules**: Ensure the event type exactly matches what's in the rule condition
2. **Notifications Not Appearing**: Check that notifications are enabled in the configuration
3. **Leaderboard Missing Users**: Users need at least one point to appear on the leaderboard
4. **Achievement Not Granted**: Check that the achievement is active and all conditions are met
5. **Performance Issues**: Use the React Query dev tools to check if unnecessary queries are being made

### Debugging

For deeper debugging:

1. Check the browser console for errors
2. Examine the `gamification_logs` table in the database
3. Use the admin analytics dashboard to track event frequencies
4. Verify that user permissions are set correctly for the gamification tables

## Security Considerations

The gamification system uses Row Level Security (RLS) to ensure users can only access their own data:

- Users can only view their own profiles and achievements
- Only admins can create and modify achievements, rules, and configuration
- Leaderboard data is partially anonymized for privacy

## Performance Optimization

The system is optimized for performance through:

1. **View Materialization**: Database views for complex queries
2. **React Query Caching**: Minimizes redundant API calls
3. **Efficient UI Components**: Only re-renders when necessary
4. **Batch Processing**: Rules are processed in batches to improve performance

## Future Roadmap

Planned enhancements to the gamification system:

1. **Daily/Weekly Challenges**: Time-limited special achievements
2. **Social Features**: Achievement sharing and social media integration
3. **Advanced Analytics**: More detailed insights into user engagement
4. **Personalization**: Tailored achievement recommendations based on user behavior
5. **Mobile Notifications**: Push notifications for mobile apps
