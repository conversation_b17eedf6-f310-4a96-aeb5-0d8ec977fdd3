# Supabase Connection Troubleshooting

This guide helps you troubleshoot common Supabase connection issues, especially related to SDG API integration.

## Common Issues

### 1. CORS Errors

CORS (Cross-Origin Resource Sharing) errors prevent your browser from making requests to the Supabase Edge Functions. 

**Symptoms:**
- Console errors mentioning "Access to fetch at 'https://your-project.supabase.co/functions/...' has been blocked by CORS policy"
- Errors about headers not being allowed

**Solutions:**
- Run `node scripts/fix-supabase-connection.js` to update the CORS configuration
- Redeploy the Edge Function with `node scripts/deploy-sdg-edge-function.js`

### 2. Authentication Errors

Auth errors prevent your app from connecting to Supabase services.

**Symptoms:**
- 401 Unauthorized errors
- "Invalid API key" errors

**Solutions:**
- Visit http://localhost:8080/fix-env-variables.html to update your environment variables
- Make sure you're using the correct Supabase URL and API keys
- Check if your service is properly initialized

### 3. Environment Variables Issues

Your app might be missing the necessary environment variables.

**Solutions:**
- Create or update .env.local with the required variables:
  - VITE_SUPABASE_URL
  - VITE_SUPABASE_ANON_KEY
  - SUPABASE_SERVICE_ROLE_KEY
  - VITE_EDGE_FUNCTION_SECRET
- Run the app with `npm run dev` to ensure variables are loaded

## Troubleshooting Tools

We've provided several tools to help diagnose and fix Supabase issues:

- **check-supabase-connection.html**: Tests your Supabase connection in the browser
- **fix-env-variables.html**: Updates your Supabase environment variables
- **fix-supabase-connection.js**: Command-line tool to fix CORS and other issues
- **deploy-sdg-edge-function.js**: Redeploys the SDG ETL Edge Function

## SDG API Integration

The SDG API integration uses Supabase Edge Functions to:

1. Fetch data from the UN SDG API
2. Process and store the data in Supabase database
3. Calculate rankings and benchmarks
4. Provide an API for the frontend application

If you're experiencing issues with the SDG integration specifically, try:

1. Updating the Edge Function's CORS configuration
2. Checking the Edge Function logs in the Supabase dashboard
3. Verifying that your EDGE_FUNCTION_SECRET matches between the client and the Edge Function

## Need More Help?

If you're still experiencing issues, you can:

1. Check the application logs in the browser console
2. Look at the Supabase dashboard for more detailed logs
3. Run `node scripts/check-env-config.js` to validate your environment setup
