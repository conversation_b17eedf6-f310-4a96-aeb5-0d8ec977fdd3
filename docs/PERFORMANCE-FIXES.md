# Products Page Performance Fixes

This document outlines the performance issues identified in the products page and the solutions implemented to resolve them.

## Issues Identified

1. **Infinite Update Loop in React Components**
   - The `ProductEditDialog` component had a dependency loop causing excessive re-renders
   - Error: `Maximum update depth exceeded. This can happen when a component calls setState inside useEffect...`

2. **Missing RPC Functions**
   - Several Supabase RPC function calls were resulting in 404 errors
   - Missing functions: `get_leaderboard`, `get_role_permissions_matrix`, `get_role_permissions_matrix_alt`

3. **Database Schema Mismatches**
   - The application was querying columns that don't exist in the database
   - Error: `column product_bases.survey_links does not exist`

4. **Function Reference Errors**
   - The `ProductCardContributors` component was trying to use a non-existent `checkAchievements` function
   - Error: `Error in delayed achievement check: TypeError: checkAchievements is not a function`

## Solutions Implemented

### 1. Fixed Infinite Update Loop in ProductEditDialog

- Removed `selectedDept` from the useEffect dependency array to break the circular dependency
- Added conditionals to prevent unnecessary state updates
- Improved code comments and readability

### 2. Added Missing RPC Functions Support

- Created a script to apply the SQL migrations for the missing RPC functions
- Added fallback implementations in the application code when RPC functions fail
- Created the `src/commands/apply-rpc-functions.js` script for easy deployment

### 3. Fixed Database Schema Mismatches

- Updated `useIterationRelatedItems.ts` to use correct database queries
- Changed the pattern from querying non-existent columns to querying relationship tables
- Added error handling for each query to prevent cascading failures

### 4. Fixed Function Reference Errors

- Added compatibility layer in `useAchievements.ts` hook
- Created `checkAchievements` as an alias for the `trackEvent` function
- Ensured backward compatibility without changing existing component code

## How to Apply the Fixes

### Run the Database Migration Script

To apply the RPC functions to your Supabase database, you have two options:

#### Option 1: Run the migration script (if you have direct database access)

```bash
# First ensure your Supabase connection details are in the .env file
# SUPABASE_URL, SUPABASE_ANON_KEY, and SUPABASE_SERVICE_ROLE_KEY are already configured

# If you have direct database access, add your database connection string to .env:
# SUPABASE_CONNECTION_STRING=postgresql://postgres:<EMAIL>:5432/postgres

# Run the migration script
node src/commands/apply-rpc-functions.js
```

> Note: To get your PostgreSQL connection string, go to your Supabase project dashboard, 
> navigate to Project Settings > Database > Connection string > URI

#### Option 2: Apply migrations manually in the Supabase dashboard

If you don't have direct database access, you can manually run the SQL migrations:

1. Go to the [Supabase dashboard](https://app.supabase.io/)
2. Select your project
3. Go to the "SQL Editor" section
4. Open the file `supabase/migrations/20250423_add_missing_rpc_functions.sql` from this project
5. Copy the entire SQL content
6. Paste it into the SQL Editor in the Supabase dashboard
7. Run the query

### Verify the Fixes

After applying the fixes and running the migration script:

1. Refresh your application
2. Open the Products page
3. Check the console for errors - the previous errors should be gone
4. Verify that the page loads faster and operates smoothly
5. Test the product edit dialog functionality

## Technical Details

### Code Changes

- **Fixed useEffect in ProductEditDialog.tsx**
  ```javascript
  // Changed from:
  useEffect(() => {
    // Effect code...
  }, [selectedGeneralDept, departments, generalDepartments, selectedDept]); // selectedDept causes loop

  // To:
  useEffect(() => {
    // Effect code with conditional checks...
  }, [selectedGeneralDept, departments, generalDepartments]); // Removed selectedDept
  ```

- **Fixed Database Queries in useIterationRelatedItems.ts**
  ```javascript
  // Changed from querying non-existent columns:
  const { data: product } = await supabase
    .from('product_bases')
    .select('survey_links, indicator_links, publication_links')
    .eq('id', productId)
    .single();

  // To separate queries on relationship tables:
  const { data: surveys } = await supabase
    .from('product_surveys')
    .select('id')
    .eq('product_base_id', productId);
  
  // And similar queries for indicators and publications
  ```

- **Added Backward Compatibility in useAchievements.ts**
  ```javascript
  // Added compatibility alias:
  const checkAchievements = trackEvent;
  
  return {
    // Other properties...
    trackEvent,
    checkAchievements // Added alias
  };
  ```

### SQL Migrations

The RPC functions added via the migration:

- `get_leaderboard(p_limit integer, p_offset integer)`: Retrieves leaderboard data for gamification
- `get_role_permissions_matrix()`: Retrieves flattened permission matrix for role-based access control
- `get_role_permissions_matrix_alt()`: Retrieves nested permission matrix as an alternative format

## Remaining Considerations

- The performance can be further improved by optimizing the number of database queries
- Consider implementing data caching for frequently accessed data
- Review other components for similar React dependency issues
- Consider implementing server-side pagination for large data sets

If you encounter any issues with these fixes, please report them in the project issue tracker.
