# Manual RPC Functions Deployment Guide

Since direct database access is not available, you'll need to manually apply the RPC functions through the Supabase dashboard SQL editor. This document provides step-by-step instructions.

## Updates (2025-04-24)

⚠️ **New SQL Migration Added**: A new SQL migration has been added to fix the "column reference role_id is ambiguous" error in the get_dynamic_user_capabilities function. This migration should be applied along with the original RPC functions.

## Prerequisites

- Access to your Supabase project dashboard
- Admin privileges to execute SQL commands

## Steps to Apply RPC Functions

1. **Log in to Supabase Dashboard**
   - Go to [app.supabase.io](https://app.supabase.io/)
   - Sign in with your credentials
   - Select your project: `wgsnpiskyczxhojlrwtr`

2. **Open the SQL Editor**
   - From the left menu, click on "SQL Editor"
   - Create a new query or open an existing one

3. **Copy the SQL Migration**
   - Open the file `supabase/migrations/20250423_add_missing_rpc_functions.sql` from your project
   - Copy the entire contents

4. **Execute the SQL**
   - Paste the SQL into the Supabase SQL Editor
   - Click "Run" to execute the SQL

5. **Verify RPC Functions**
   - After running the SQL, you can verify the functions were created by trying:
     ```sql
     SELECT * FROM pg_proc WHERE proname IN ('get_leaderboard', 'get_role_permissions_matrix', 'get_role_permissions_matrix_alt');
     ```
   - This query should return information about the three functions if they were created successfully

## RPC Functions SQL

For convenience, here's the SQL that needs to be executed:

```sql
-- Function: get_leaderboard(integer, integer)
CREATE OR REPLACE FUNCTION get_leaderboard(
  p_limit integer DEFAULT 10,
  p_offset integer DEFAULT 0
) RETURNS TABLE (
  rank integer,
  user_id uuid,
  display_name text,
  points integer,
  level integer
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  RETURN QUERY
  SELECT
    ROW_NUMBER() OVER (ORDER BY ugp.points DESC) AS rank,
    u.id AS user_id,
    COALESCE(u.raw_user_meta_data->>'full_name', u.email) AS display_name,
    ugp.points,
    ugp.level
  FROM
    user_gamification_profiles ugp
  JOIN
    auth.users u ON ugp.user_id = u.id
  ORDER BY
    ugp.points DESC
  LIMIT p_limit
  OFFSET p_offset;
END;
$$;

-- Function: get_role_permissions_matrix()
CREATE OR REPLACE FUNCTION get_role_permissions_matrix()
RETURNS TABLE (
  role_id uuid,
  role_name text,
  capability_id uuid,
  capability_code text,
  capability_name text,
  is_granted boolean
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  RETURN QUERY
  SELECT
    r.id AS role_id,
    r.name AS role_name,
    c.id AS capability_id,
    c.code AS capability_code,
    c.name AS capability_name,
    COALESCE(rc.is_granted, false) AS is_granted
  FROM
    dynamic_roles r
  CROSS JOIN
    capabilities c
  LEFT JOIN
    role_capabilities rc ON r.id = rc.role_id AND c.id = rc.capability_id
  ORDER BY
    r.name, c.category, c.name;
END;
$$;

-- Function: get_role_permissions_matrix_alt()
CREATE OR REPLACE FUNCTION get_role_permissions_matrix_alt()
RETURNS TABLE (
  role_id uuid,
  role_name text,
  capabilities jsonb
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  RETURN QUERY
  SELECT
    r.id AS role_id,
    r.name AS role_name,
    jsonb_object_agg(
      c.code,
      jsonb_build_object(
        'id', c.id,
        'name', c.name,
        'category', c.category,
        'is_granted', COALESCE(rc.is_granted, false)
      )
    ) AS capabilities
  FROM
    dynamic_roles r
  CROSS JOIN
    capabilities c
  LEFT JOIN
    role_capabilities rc ON r.id = rc.role_id AND c.id = rc.capability_id
  GROUP BY
    r.id, r.name
  ORDER BY
    r.name;
END;
$$;

-- Ensure security for the functions
ALTER FUNCTION get_leaderboard(integer, integer) SECURITY DEFINER SET search_path = public;
ALTER FUNCTION get_role_permissions_matrix() SECURITY DEFINER SET search_path = public;
ALTER FUNCTION get_role_permissions_matrix_alt() SECURITY DEFINER SET search_path = public;

-- Grant execution permission to authenticated users
GRANT EXECUTE ON FUNCTION get_leaderboard(integer, integer) TO authenticated;
GRANT EXECUTE ON FUNCTION get_role_permissions_matrix() TO authenticated;
GRANT EXECUTE ON FUNCTION get_role_permissions_matrix_alt() TO authenticated;
```

## Additional SQL for Fixing Ambiguous Column Error

If you're seeing the error "column reference role_id is ambiguous" in your console, you'll also need to apply this SQL fix:

```sql
-- Function: get_dynamic_user_capabilities(uuid)
CREATE OR REPLACE FUNCTION get_dynamic_user_capabilities(
  p_user_id uuid
) RETURNS TABLE (
  role_id uuid,
  role_name text,
  capability_id uuid,
  capability_code text,
  capability_name text,
  capability_category text,
  is_granted boolean
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  RETURN QUERY
  SELECT
    r.id AS role_id,
    r.name AS role_name,
    c.id AS capability_id,
    c.code AS capability_code,
    c.name AS capability_name,
    c.category AS capability_category,
    COALESCE(rc.is_granted, false) AS is_granted
  FROM
    user_roles ur
  JOIN
    dynamic_roles r ON ur.role_id = r.id
  CROSS JOIN
    capabilities c
  LEFT JOIN
    role_capabilities rc ON r.id = rc.role_id AND c.id = rc.capability_id
  WHERE
    ur.user_id = p_user_id
  ORDER BY
    r.name, c.category, c.name;
END;
$$;

-- Ensure security for the functions
ALTER FUNCTION get_dynamic_user_capabilities(uuid) SECURITY DEFINER SET search_path = public;

-- Grant execution permission to authenticated users
GRANT EXECUTE ON FUNCTION get_dynamic_user_capabilities(uuid) TO authenticated;
```

## After Applying the Functions

After successfully applying the RPC functions:

1. Refresh your application and navigate to the Products page
2. Verify that the 404 errors for RPC functions are no longer appearing in the console
3. Check if the page loads faster and operates more smoothly

If you see errors related to missing database tables (like `product_surveys`), don't worry - we've implemented fallbacks in the application code to handle those gracefully.

## Alternative: Client-side Fallbacks

Even without applying these functions, the application should continue to work because we've implemented client-side fallbacks for all these functions. However, for optimal performance, it's better to have the actual database functions in place.
