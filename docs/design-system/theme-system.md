# Theme System

This document describes the theme system for the Stat-Linker-Factory application, including how to use and extend it.

## Overview

The theme system consists of two primary components:

1. **Mode**: Light mode vs Dark mode (including system preference detection)
2. **Theme Colors**: Multiple color palette options that can be applied to either mode

The theme system is built upon CSS variables and Tailwind's theme configuration, making it highly customizable while maintaining a consistent look and feel.

## Theme Architecture

```mermaid
graph TD
    ThemeProvider[ThemeProvider Component]
    ThemeProvider --> ModeState[Mode State]
    ThemeProvider --> ColorState[Theme Color State]
    
    ModeState --> ApplyMode[Apply Mode Classes]
    ColorState --> ApplyColor[Apply Color Classes]
    
    ApplyMode --> RootElement[HTML Root Element]
    ApplyColor --> RootElement
    
    RootElement --> CSSVariables[CSS Variables]
    CSSVariables --> ComponentStyles[Component Styles]
```

## Available Themes

### Mode Options

- **Light**: Default light mode
- **Dark**: Dark mode with adjusted colors for reduced eye strain
- **System**: Automatically switches between light and dark based on system preferences

### Color Themes

The application supports the following color themes:

1. **Slate** (Default): Professional, neutral color scheme
   - Primary: Slate tones
   - Secondary: Muted slate
   - Accent: Cool gray

2. **Blue**: Vibrant, trust-inspiring color scheme
   - Primary: Strong blue
   - Secondary: Lighter blue
   - Accent: Cyan

3. **Green**: Fresh, growth-oriented color scheme
   - Primary: Emerald
   - Secondary: Teal
   - Accent: Light green

4. **Purple**: Creative, innovative color scheme
   - Primary: Deep purple
   - Secondary: Lavender
   - Accent: Indigo

5. **Orange**: Energetic, attention-grabbing color scheme
   - Primary: Vibrant orange  
   - Secondary: Amber
   - Accent: Light orange

Each color theme maintains proper contrast ratios for accessibility in both light and dark modes.

## Implementation

### CSS Variables

The theme system uses CSS variables to define colors and other design tokens. These are defined in the `:root` selector and various theme classes:

```css
:root {
  /* Base variables */
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  /* More variables... */
}

.theme-blue {
  --primary: 221 83% 53%;
  --primary-foreground: 210 40% 98%;
  /* Other theme-specific variables... */
}

.dark {
  /* Dark mode variables */
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  /* More dark mode variables... */
}

.dark.theme-blue {
  /* Dark-specific overrides for the blue theme */
  --primary: 213 94% 68%;
  /* Other dark mode theme-specific variables... */
}
```

### ThemeProvider Component

The `ThemeProvider` component in `src/components/theme-provider.tsx` manages the theme state and applies the appropriate classes to the HTML root element.

```tsx
// Usage example
<ThemeProvider defaultTheme="system" defaultThemeColor="slate">
  <App />
</ThemeProvider>
```

### Theme Hooks

The `useTheme` hook provides access to the current theme state and methods to change it:

```tsx
const { theme, themeColor, setTheme, setThemeColor } = useTheme();

// Change the theme mode
setTheme("dark");

// Change the theme color
setThemeColor("blue");
```

## Theme Switcher Component

The `ThemeToggle` component provides a user interface for changing the theme:

```tsx
<ThemeToggle />
```

This component displays a single dropdown menu that combines:
1. Theme mode selection (light/dark/system)
2. Theme color selection (slate/blue/green/purple/orange)

This consolidated approach reduces UI clutter while providing all theme customization options in one place.

## Custom Themes (Future Enhancement)

In the future, the theme system will support custom user-defined themes with:

1. Color picker for primary, secondary, and accent colors
2. Theme saving and naming
3. Theme sharing

## Best Practices

### Using Theme Colors

Always use semantic color variables rather than specific colors:

```tsx
// Good
<div className="bg-primary text-primary-foreground">
  Content
</div>

// Avoid
<div className="bg-blue-500 text-white">
  Content
</div>
```

### Theme-Aware Components

Components should automatically adapt to theme changes without specific code:

```tsx
// This will automatically use the correct colors based on the theme
const Button = ({ children, ...props }) => {
  return (
    <button 
      className="bg-primary text-primary-foreground px-4 py-2 rounded"
      {...props}
    >
      {children}
    </button>
  );
};
```

### Testing Themes

When developing components, test them in:
1. Both light and dark modes
2. All available color themes
3. At various screen sizes to ensure responsive design
