# Iconography Guidelines

This document outlines the standards for using icons within the application to ensure visual consistency and clarity.

## 1. Icon Library

- **Source:** Icons should be sourced exclusively from the [`lucide-react`](https://lucide.dev/) library. This provides a consistent set of high-quality, outline-style icons.

## 2. Color Palette

Use Tailwind CSS utility classes for icon colors based on the following contexts:

- **Primary Actions/States:** Use `text-primary`.
  - Examples: Completed checklist items, selected state indicators, icons in primary buttons.
- **Secondary/Decorative Icons:** Use `text-muted-foreground`.
  - Examples: Icons accompanying section headers, non-interactive indicators, icons next to muted text.
- **Destructive Actions:** Use `text-destructive` (or theme-appropriate red like `text-red-500`/`text-red-600`).
  - Examples: Delete icons, critical error indicators.
- **Default/Inherited:** For icons within standard text or buttons without a specific semantic state, allow the icon to inherit the parent element's text color (no specific `text-*` class needed unless overriding).

## 3. Sizing

Use Tailwind CSS utility classes for sizing:

- **Standard (Default):** `h-4 w-4` (1rem / 16px). This should be the most common size.
- **Emphasis:** `h-5 w-5` (1.25rem / 20px) or `h-6 w-6` (1.5rem / 24px). Use sparingly for larger buttons or elements requiring more visual prominence.
- **Small Contexts:** `h-3.5 w-3.5` (14px). Use only when necessary within very tight spaces (e.g., small badges).

## 4. Style

- **Variant:** Use the default outline style provided by `lucide-react`. Avoid filled variants unless specifically required by a unique design context.

## 5. Usage Context

- **Clarity:** Icons should primarily support text labels. Avoid using icons alone unless the action is universally understood (e.g., close `X`, `Edit`, `Trash2`) and space is constrained.
- **Accessibility:** For icon-only buttons, **always** provide a descriptive tooltip using the `<Tooltip>` component from `shadcn/ui`.
- **Consistency:** Use the *same icon* for the *same action or meaning* throughout the application. Refer to this guide or established patterns within the app.
- **Placement:** When pairing with text (e.g., in buttons or headers), ensure consistent spacing, typically using a small gap (e.g., `gap-1` or `gap-2`).