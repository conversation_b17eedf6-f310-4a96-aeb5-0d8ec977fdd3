# Design Tokens

This document provides a comprehensive overview of the design tokens used throughout the Stat-Linker-Factory application, ensuring consistent styling across components.

## Color System

### Base Colors

Our application uses a HSL-based color system with CSS variables, allowing for theme customization while maintaining consistency.

```css
/* Base colors defined as HSL values */
--primary: 210 40% 98%;
--primary-foreground: 222.2 47.4% 11.2%;
--secondary: 215 25% 27%;
--secondary-foreground: 210 40% 98%;
--accent: 210 40% 96.1%;
--accent-foreground: 222.2 47.4% 11.2%;
--destructive: 0 100% 50%;
--destructive-foreground: 210 40% 98%;
--muted: 210 40% 96.1%;
--muted-foreground: 215.4 16.3% 46.9%;
--card: 0 0% 100%;
--card-foreground: 222.2 47.4% 11.2%;
--popover: 0 0% 100%;
--popover-foreground: 222.2 47.4% 11.2%;
--border: 214.3 31.8% 91.4%;
--input: 214.3 31.8% 91.4%;
--ring: 221.2 83.2% 53.3%;
--background: 0 0% 100%;
--foreground: 222.2 47.4% 11.2%;
```

### Theme Variants

The application supports multiple theme colors, each with its own color palette:

- **Slate** (Default): Professional, neutral color scheme
- **Blue**: Vibrant, trust-inspiring color scheme
- **Green**: Fresh, growth-oriented color scheme  
- **Purple**: Creative, innovative color scheme
- **Orange**: Energetic, attention-grabbing color scheme

### Semantic Colors

These tokens represent meaning, not specific color values:

| Token | Purpose | Usage |
|-------|---------|-------|
| `primary` | Main brand color | Primary buttons, focus states, highlighted elements |
| `secondary` | Supporting color | Secondary buttons, alternative elements |
| `accent` | Emphasis color | Accents, highlights, callouts |
| `muted` | Subdued elements | Backgrounds, disabled states, secondary text |
| `destructive` | Warning/error states | Delete buttons, error messages |

## Typography

The application uses a carefully selected set of font families with appropriate sizing scales:

### Font Families

```css
--font-sans: 'Tajawal', sans-serif; /* Primary UI font */
--font-syncopate: 'Syncopate', sans-serif; /* Display/heading font */
--font-big-shoulders: '"Big Shoulders Display"', cursive; /* Alternative heading font */
```

### Font Sizes

| Token | Size | Usage |
|-------|------|-------|
| `text-xs` | 0.75rem | Fine print, footnotes |
| `text-sm` | 0.875rem | Secondary text, metadata |
| `text-base` | 1rem | Body text, default size |
| `text-lg` | 1.125rem | Enhanced body text |
| `text-xl` | 1.25rem | Small headings |
| `text-2xl` | 1.5rem | Medium headings |
| `text-3xl` | 1.875rem | Large headings |
| `text-4xl` | 2.25rem | Extra large headings |

### Font Weights

| Token | Weight | Usage |
|-------|--------|-------|
| `font-light` | 300 | Decorative text |
| `font-normal` | 400 | Body text, default weight |
| `font-medium` | 500 | Slightly emphasized text |
| `font-semibold` | 600 | Subheadings, emphasized text |
| `font-bold` | 700 | Headings, important text |

## Spacing

Our spacing system is based on a 0.25rem (4px) base unit:

| Token | Size | Description |
|-------|------|-------------|
| `space-0` | 0 | No spacing |
| `space-1` | 0.25rem (4px) | Extra small spacing |
| `space-2` | 0.5rem (8px) | Small spacing |
| `space-3` | 0.75rem (12px) | Medium-small spacing |
| `space-4` | 1rem (16px) | Medium spacing, default for most elements |
| `space-5` | 1.25rem (20px) | Medium-large spacing |
| `space-6` | 1.5rem (24px) | Large spacing |
| `space-8` | 2rem (32px) | Extra large spacing |
| `space-10` | 2.5rem (40px) | 2x large spacing |
| `space-12` | 3rem (48px) | 3x large spacing |
| `space-16` | 4rem (64px) | 4x large spacing |

## Borders & Shadows

### Border Radius

Consistent rounding of elements based on a 0.5rem base value:

```css
--radius: 0.5rem; /* Base radius */
--radius-sm: calc(var(--radius) - 0.25rem); /* Small radius */
--radius-lg: calc(var(--radius) + 0.25rem); /* Large radius */
--radius-full: 9999px; /* Full circular radius for pills, avatars */
```

### Shadows

Elevation system with consistent shadows:

```css
--shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
--shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
--shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
--shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
--shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
```

## Animation

Standard animations for consistent motion:

| Token | Timing | Usage |
|-------|--------|-------|
| `animate-fade-in` | 0.5s ease-out | Elements appearing on page load |
| `animate-slide-in` | 0.3s ease-out | Side panel entrances |
| `animate-slide-down` | 0.3s ease-out | Dropdown menus, notifications |
| `animate-slide-up` | 0.3s ease-out | Toasts, bottom-up notifications |
