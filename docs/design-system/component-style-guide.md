# Component Style Guide

This document outlines the standardized patterns for component styling and usage across the Stat-Linker-Factory application. Following these guidelines ensures visual consistency and optimal user experience.

## Button Components

Buttons should follow these consistent patterns:

### Button Hierarchy

1. **Primary Button**: Used for the main action on a page or in a form.
   - `variant="default"` 
   - Background: `primary`
   - Text: `primary-foreground`
   - Usage: Submit forms, confirm actions, main CTA

2. **Secondary Button**: Used for alternative actions.
   - `variant="secondary"`
   - Background: `secondary`
   - Text: `secondary-foreground`
   - Usage: Cancel, alternative options, secondary CTAs

3. **Outline Button**: Used for less emphasized actions.
   - `variant="outline"`
   - Border: `border`
   - Text: `foreground`
   - Usage: Secondary options, toggles, filters

4. **Ghost Button**: Minimal visual presence for low-emphasis actions.
   - `variant="ghost"`
   - Background: Transparent (hover: `muted`)
   - Text: `foreground`
   - Usage: Toolbar actions, compact UIs, icon buttons

5. **Destructive Button**: Indicates actions that might have destructive consequences.
   - `variant="destructive"`
   - Background: `destructive`
   - Text: `destructive-foreground`
   - Usage: Delete, remove, actions with permanent consequences

### Button Sizes

- **Large** (`size="lg"`): Use for main page CTAs and prominent actions.
- **Default** (`size="default"`): Standard size for most button use cases.
- **Small** (`size="sm"`): For compact UIs or secondary actions.
- **Icon** (`size="icon"`): Square-shaped for icon-only buttons.

### Button Best Practices

- Maintain consistent button ordering (e.g., Cancel always on left, Submit on right)
- Use icon + text for most buttons, icon-only for well-recognized actions
- For icon-only buttons, always include tooltip and aria-label
- Use `disabled` state appropriately rather than hiding buttons
- Follow reading direction for button ordering (primary action on trailing edge)

## Form Components

### Text Inputs

- Use appropriate input types (`text`, `email`, `password`, etc.)
- Always include proper labeling
- Show validation states clearly with error messages below the input
- Include helper text when input requirements need clarification
- Consistent sizing and spacing between form elements

### Select Menus

- Use `Select` component for single selection from defined options
- Use `Combobox` for searchable select with many options
- Use `MultiSelect` for selecting multiple options

### Checkboxes and Radios

- Use Checkbox for boolean selections (on/off, true/false)
- Use Radio groups for mutually exclusive options
- Group related options with clear labeling

### Field Groups

- Group related form fields with clear section headers
- Maintain consistent spacing between field groups (`space-8`)
- Use fieldset and legend for accessible grouping

## Card Components

Cards should be used consistently to display discrete content units:

### Card Types

1. **Standard Card**: For general content display.
   - Consistent padding: `p-6`
   - Border radius following design tokens
   - Optional hover state for interactive cards

2. **Dashboard Card**: For metrics and data visualization.
   - Consistent height and width
   - Clear header with action buttons when needed
   - Support for various content types (numbers, charts, lists)

3. **Product Card**: Specialized for product display.
   - Consistent image size/aspect ratio
   - Structured information hierarchy
   - Clear action buttons

### Card Best Practices

- Maintain consistent spacing between cards
- Ensure cards in the same view have consistent heights when possible
- Use skeleton loading states for asynchronous content
- Include appropriate hover/focus states for interactive cards

## Table Components

Tables should follow these patterns for consistency:

- Clear column headers with proper alignment (left for text, right for numbers)
- Consistent row height and padding
- Zebra striping or hover states for improved readability
- Responsive strategies for narrow viewports
- Consistent action pattern for row operations

## Dialog and Modal Components

- Use modals sparingly for user focus and important interactions
- Consistent header, body, and footer structure
- Standard button positioning in footer (cancel/secondary left, confirm/primary right)
- Overlay background to indicate modal state
- Properly manage focus trapping and keyboard navigation

## Navigation Components

- Consistent styling for active/inactive states
- Clear visual hierarchy for primary/secondary navigation
- Mobile navigation adaptations with appropriate touch targets
- Breadcrumbs for hierarchical page structures

## Layout Patterns

- Use `Container` for consistent maximum width
- Maintain consistent spacing between major layout sections
- Use standard grid patterns for responsive layouts
- Ensure consistent page header patterns

## Data Visualization

- Consistent color usage for data types
- Maintain sizing and spacing around charts and graphs
- Provide proper legends and tooltips for data interpretation
- Ensure accessibility with text alternatives and keyboard navigation

## Empty States

- Provide helpful empty states rather than blank areas
- Include guidance for user actions when appropriate
- Maintain consistent styling for empty state illustrations

## Loading States

- Use Skeleton loaders for content loading
- Spinner component for process indications
- Maintain consistent animation timing
- Provide feedback for long-running operations

## Error States

- Clear error messaging with recovery options
- Consistent styling for error indicators
- Form validation errors with specific guidance
- System errors with appropriate actions

## Responsive Design

- Follow mobile-first approach
- Use standard breakpoints from theme
- Component behavior should adapt appropriately to viewport size
- Touch-friendly sizing on mobile devices

## Accessibility Guidelines

- Maintain color contrast ratios per WCAG standards
- Ensure keyboard navigability
- Include appropriate ARIA attributes
- Support screen readers with semantic markup
- Focus management for interactive elements
