# Design System

This directory contains documentation and implementation for the Stat-Linker-Factory design system.

## Overview

The design system provides a comprehensive set of standards, components, and patterns to ensure consistent styling and user experience throughout the application. It includes:

1. **Design tokens**: Foundational styling elements like colors, typography, spacing, etc.
2. **Component guidelines**: Standards for how components should look and behave
3. **Theme system**: Support for light/dark modes and multiple color themes

## Files

- [`design-tokens.md`](./design-tokens.md): Comprehensive documentation of all design tokens
- [`component-style-guide.md`](./component-style-guide.md): Guidelines for component styling and usage
- [`theme-system.md`](./theme-system.md): Documentation of the theme system architecture and implementation

## Theme System

The theme system allows users to customize their experience with:

1. **Theme Mode**: Light or Dark mode
2. **Theme Color**: Choice of color palettes (Slate, Blue, Green, Purple, Orange)

### Implementation

The theme system is implemented with:

- CSS variables for all theme variations
- React context for theme state management
- localStorage persistence for user preferences

### Core Components

- `ThemeProvider`: Located in `src/components/theme-provider.tsx`
- `ThemeToggle`: Located in `src/components/ui/theme-toggle.tsx`

### Usage

To use theme-aware styling in components:

```tsx
// Use semantic color variables instead of hard-coded colors
<div className="bg-primary text-primary-foreground">
  This text will adapt to the current theme
</div>
```

To programmatically access or change the theme:

```tsx
import { useTheme } from "@/components/theme-provider";

function MyComponent() {
  const { theme, themeColor, setTheme, setThemeColor } = useTheme();
  
  // Change the theme mode
  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };
  
  // Change the theme color
  const setBlueTheme = () => {
    setThemeColor("blue");
  };
  
  return (
    // Your component JSX
  );
}
```

## Future Enhancements

- Custom theme creation with color picker
- Theme export/import functionality
- User-created theme library

## Best Practices

1. Always use semantic color tokens (`primary`, `secondary`, etc.) rather than specific colors
2. Test components in all theme modes and colors
3. Follow accessibility guidelines for color contrast
4. Refer to the component style guide for consistent implementation
