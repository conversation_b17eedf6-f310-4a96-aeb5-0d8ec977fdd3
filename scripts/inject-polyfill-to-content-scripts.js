/**
 * <PERSON><PERSON><PERSON> to inject crypto polyfills into content scripts
 * 
 * Usage: node scripts/inject-polyfill-to-content-scripts.js
 * 
 * This script identifies content scripts and adds the crypto.randomUUID polyfill
 * to ensure cross-browser compatibility. It directly modifies the files to
 * include the polyfill at the top of the file.
 */

import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// List of content script files to update
const TARGET_FILES = [
  'content.js', 
  'read.js',
  // Add any other content scripts that need the polyfill
];

// Paths to search for content scripts
const SEARCH_PATHS = [
  path.join(__dirname, '..', 'public'),
  path.join(__dirname, '..', 'src', 'content-scripts'),
  path.join(__dirname, '..', 'dist'),
  // Add other directories where content scripts might be located
];

// The polyfill code to inject
const POLYFILL_CODE = `
// Crypto polyfill for content scripts - injected by stability script
(function() {
  if (window.crypto && window.crypto.randomUUID) return;
  
  if (!window.crypto) {
    window.crypto = {
      getRandomValues: function(array) {
        for (let i = 0; i < array.length; i++) {
          array[i] = Math.floor(Math.random() * 256);
        }
        return array;
      }
    };
  }
  
  window.crypto.randomUUID = function() {
    return '10000000-1000-4000-8000-100000000000'.replace(/[018]/g, function(c) {
      const num = Number(c);
      return (num ^ (window.crypto.getRandomValues(new Uint8Array(1))[0] & (15 >> (num / 4)))).toString(16);
    });
  };
})();
`;

// Function to recursively find files
async function findFiles(dir, targetFiles, foundFiles = {}) {
  try {
    // Check if directory exists
    try {
      await fs.access(dir);
    } catch {
      return foundFiles;
    }
    
    const files = await fs.readdir(dir);
    
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = await fs.stat(filePath);
      
      if (stat.isDirectory()) {
        await findFiles(filePath, targetFiles, foundFiles);
      } else if (targetFiles.includes(file)) {
        foundFiles[file] = filePath;
      }
    }
    
    return foundFiles;
  } catch (error) {
    console.error(`Error searching in directory ${dir}:`, error);
    return foundFiles;
  }
}

// Function to inject polyfill to a file
async function injectPolyfill(filePath) {
  try {
    // Check if file exists
    try {
      await fs.access(filePath);
    } catch {
      console.error(`File not found: ${filePath}`);
      return false;
    }
    
    let content = await fs.readFile(filePath, 'utf8');
    
    // Check if polyfill is already present to avoid duplicates
    if (content.includes('window.crypto.randomUUID') || 
        content.includes('crypto.randomUUID')) {
      console.log(`Polyfill already exists in ${filePath}`);
      return false;
    }
    
    // Inject polyfill at the top of the file
    content = POLYFILL_CODE + '\n\n' + content;
    
    // Write the updated content back to the file
    await fs.writeFile(filePath, content);
    console.log(`✅ Successfully injected polyfill into ${filePath}`);
    return true;
  } catch (error) {
    console.error(`Error injecting polyfill into ${filePath}:`, error);
    return false;
  }
}

// Main function
async function main() {
  try {
    console.log('🔍 Searching for content scripts...');
    
    // Find all target files
    const filesToUpdate = {};
    
    for (const searchPath of SEARCH_PATHS) {
      const foundFiles = await findFiles(searchPath, TARGET_FILES);
      Object.assign(filesToUpdate, foundFiles);
    }
    
    if (Object.keys(filesToUpdate).length === 0) {
      console.log('No content script files found. Check your search paths.');
      return;
    }
    
    console.log(`Found ${Object.keys(filesToUpdate).length} files to update:`);
    Object.entries(filesToUpdate).forEach(([file, path]) => {
      console.log(`  - ${file}: ${path}`);
    });
    
    console.log('\n🔧 Injecting polyfills...');
    
    // Inject polyfill to each file
    let successCount = 0;
    
    for (const [file, filePath] of Object.entries(filesToUpdate)) {
      if (await injectPolyfill(filePath)) {
        successCount++;
      }
    }
    
    console.log(`\n✅ Updated ${successCount} of ${Object.keys(filesToUpdate).length} files.`);
    
    if (successCount === 0) {
      console.log('\n⚠️ No files were updated. The polyfill might already be present, or the files might not exist.');
    } else if (successCount < Object.keys(filesToUpdate).length) {
      console.log('\n⚠️ Some files could not be updated. See errors above.');
    } else {
      console.log('\n🎉 All content scripts were successfully updated with the crypto polyfill!');
    }
  } catch (error) {
    console.error('An error occurred while running the script:', error);
  }
}

// Run the script
main();
