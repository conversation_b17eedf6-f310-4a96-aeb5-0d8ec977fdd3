// Script to deploy the debug Edge Function
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;
const EDGE_FUNCTION_SECRET = process.env.VITE_EDGE_FUNCTION_SECRET;
const PROJECT_REF = 'wgsnpiskyczxhojlrwtr'; // Your Supabase project reference ID

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY || !EDGE_FUNCTION_SECRET) {
  console.error('❌ Missing required environment variables.');
  console.error('Please ensure VITE_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, and VITE_EDGE_FUNCTION_SECRET are set in .env.local');
  process.exit(1);
}

// Function to create or update environment file for the Edge Function
function createEnvironmentFile() {
  const envFilePath = path.join('supabase', 'functions', 'debug', '.env');
  
  console.log('Creating environment file for the Debug Edge Function...');
  
  const envContent = `SUPABASE_URL=${SUPABASE_URL}
SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
EDGE_FUNCTION_SECRET=${EDGE_FUNCTION_SECRET}`;
  
  try {
    fs.writeFileSync(envFilePath, envContent);
    console.log('Environment file created for the Debug Edge Function.');
  } catch (error) {
    console.error('❌ Error creating environment file:', error);
    process.exit(1);
  }
}

// Function to deploy the Edge Function using npx
function deployEdgeFunction() {
  console.log('Deploying Debug Edge Function...');
  
  try {
    const command = `npx supabase functions deploy debug --project-ref ${PROJECT_REF} --no-verify-jwt`;
    execSync(command, { stdio: 'inherit' });
    console.log('Debug Edge Function deployed successfully.');
  } catch (error) {
    console.error('❌ Error deploying Edge Function:', error);
    process.exit(1);
  }
}

// Main deployment process
function deployDebugFunction() {
  console.log('Starting deployment process...');
  
  // Create environment file
  createEnvironmentFile();
  
  // Deploy function
  deployEdgeFunction();
  
  console.log('Deployment process completed.');
}

// Run deployment process
deployDebugFunction();
