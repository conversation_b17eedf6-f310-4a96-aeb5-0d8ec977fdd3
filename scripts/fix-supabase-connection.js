// Script to fix Supabase connection issues
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

// Load environment variables
dotenv.config({ path: '.env.local' });

console.log('🔍 Checking Supabase connection and environment variables...');

// Check required environment variables
const requiredVars = [
  'VITE_SUPABASE_URL',
  'VITE_SUPABASE_ANON_KEY',
  'SUPABASE_SERVICE_ROLE_KEY',
  'VITE_EDGE_FUNCTION_SECRET'
];

const missingVars = requiredVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.error(`❌ Missing required environment variables: ${missingVars.join(', ')}`);
  console.error('Please add these variables to your .env.local file.');
  console.log('Creating helper pages for fixing environment variables...');
  createTestPage();
  createEnvFixPage();
  console.log(`To fix environment variables, open http://localhost:8080/fix-env-variables.html in your browser`);
  process.exit(1);
}

console.log('✅ All required environment variables are present in .env.local');

// Update the Edge Function .env file
function updateEdgeFunctionEnv() {
  try {
    console.log('📝 Updating Edge Function environment variables...');
    
    const funcDir = path.join(process.cwd(), 'supabase', 'functions', 'sdg-etl');
    
    if (!fs.existsSync(funcDir)) {
      console.error(`❌ Edge Function directory not found: ${funcDir}`);
      return false;
    }
    
    const envContent = `SUPABASE_URL=${process.env.VITE_SUPABASE_URL}
SUPABASE_SERVICE_ROLE_KEY=${process.env.SUPABASE_SERVICE_ROLE_KEY}
EDGE_FUNCTION_SECRET=${process.env.VITE_EDGE_FUNCTION_SECRET}
`;
    
    fs.writeFileSync(path.join(funcDir, '.env'), envContent);
    console.log('✅ Edge Function environment variables updated');
    return true;
  } catch (error) {
    console.error(`❌ Error updating Edge Function environment: ${error.message}`);
    return false;
  }
}

// Update CORS configuration in the Edge Function
function updateCorsConfig() {
  try {
    console.log('🔄 Updating CORS configuration...');
    
    const corsPath = path.join(process.cwd(), 'supabase', 'functions', 'sdg-etl', 'cors.ts');
    
    if (!fs.existsSync(corsPath)) {
      console.error(`❌ CORS file not found: ${corsPath}`);
      return false;
    }
    
    const corsContent = `// CORS handling for SDG ETL Edge Function

// CORS headers to allow cross-origin requests
export const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-client-info, apikey, X-Client-Info, X-PostgreSql-Error, Prefer',
  'Access-Control-Expose-Headers': 'Content-Length, X-PostgreSql-Error',
  'Access-Control-Max-Age': '86400',
};

// Handle CORS preflight requests
export function handleCorsOptions(req: Request): Response | null {
  // Return a CORS preflight response for OPTIONS requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders,
    });
  }
  
  return null;
}

// Add CORS headers to an existing response
export function addCorsHeaders(res: Response): Response {
  const headers = new Headers(res.headers);
  
  // Add all CORS headers
  Object.entries(corsHeaders).forEach(([key, value]) => {
    headers.set(key, value);
  });
  
  return new Response(res.body, {
    status: res.status,
    statusText: res.statusText,
    headers,
  });
}`;
    
    fs.writeFileSync(corsPath, corsContent);
    console.log('✅ CORS configuration updated');
    return true;
  } catch (error) {
    console.error(`❌ Error updating CORS configuration: ${error.message}`);
    return false;
  }
}

// Create a simple client-side test page for troubleshooting
function createTestPage() {
  try {
    console.log('📄 Creating test page for Supabase connection...');
    
    const publicDir = path.join(process.cwd(), 'public');
    
    if (!fs.existsSync(publicDir)) {
      fs.mkdirSync(publicDir, { recursive: true });
    }
    
    const jsDir = path.join(publicDir, 'js');
    if (!fs.existsSync(jsDir)) {
      fs.mkdirSync(jsDir, { recursive: true });
    }
    
    const testPageContent = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Supabase Connection Test</title>
  <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
  <script src="js/check-supabase-env.js"></script>
  <style>
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 { color: #3ECF8E; }
    pre {
      background-color: #f4f4f4;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
    }
    .header { display: flex; align-items: center; }
    .header svg { margin-right: 10px; }
    .status { font-weight: bold; }
    .success { color: #3ECF8E; }
    .error { color: #ef4444; }
    .warning { color: #f59e0b; }
    button {
      background-color: #3ECF8E;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      margin-top: 10px;
    }
    button:hover { background-color: #38bb81; }
    .test-group {
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 16px;
    }
    .test-group h3 { margin-top: 0; }
    #fixInstructions {
      background-color: #fdf6b2;
      border: 1px solid #fce96a;
      border-radius: 4px;
      padding: 16px;
      margin-top: 20px;
      display: none;
    }
  </style>
</head>
<body>
  <div class="header">
    <svg width="32" height="32" viewBox="0 0 109 113" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0627L99.1935 40.0627C107.384 40.0627 111.952 49.5228 106.859 55.9374L63.7076 110.284Z" fill="url(#paint0_linear)"/>
      <path d="M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0627L99.1935 40.0627C107.384 40.0627 111.952 49.5228 106.859 55.9374L63.7076 110.284Z" fill="url(#paint1_linear)" fill-opacity="0.2"/>
      <path d="M45.317 2.07103C48.1765 -1.53037 53.9745 0.442937 54.0434 5.041L54.4849 72.2922H9.83113C1.64038 72.2922 -2.92775 62.8321 2.1655 56.4175L45.317 2.07103Z" fill="#3ECF8E"/>
      <defs>
      <linearGradient id="paint0_linear" x1="53.9738" y1="54.974" x2="94.1635" y2="71.8295" gradientUnits="userSpaceOnUse">
      <stop stop-color="#249361"/>
      <stop offset="1" stop-color="#3ECF8E"/>
      </linearGradient>
      <linearGradient id="paint1_linear" x1="36.1558" y1="30.578" x2="54.4844" y2="106.178" gradientUnits="userSpaceOnUse">
      <stop stop-color="white"/>
      <stop offset="1" stop-color="white" stop-opacity="0"/>
      </linearGradient>
      </defs>
    </svg>
    <h1>Supabase Connection Test</h1>
  </div>
  
  <p>This page tests your Supabase connection and environment variables setup.</p>
  
  <div class="test-group">
    <h3>Environment Variables</h3>
    <div id="envStatus">Checking environment variables...</div>
    <pre id="envDetails"></pre>
  </div>
  
  <div class="test-group">
    <h3>Supabase Client Connection</h3>
    <div id="clientStatus">Testing Supabase client connection...</div>
    <pre id="clientDetails"></pre>
    <button id="testClientBtn">Test Client Connection</button>
  </div>
  
  <div class="test-group">
    <h3>Edge Function Access</h3>
    <div id="functionStatus">Waiting for test...</div>
    <pre id="functionDetails"></pre>
    <button id="testFunctionBtn">Test Edge Function</button>
  </div>
  
  <div id="fixInstructions"></div>
  
  <p><a href="fix-env-variables.html">Update Environment Variables</a></p>
  
  <script src="js/supabase-console-reset.js"></script>
</body>
</html>`;
    
    fs.writeFileSync(path.join(publicDir, 'check-supabase-connection.html'), testPageContent);
    console.log('✅ Test page created at /check-supabase-connection.html');
    return true;
  } catch (error) {
    console.error(`❌ Error creating test page: ${error.message}`);
    return false;
  }
}

// Check for .env.local and create it if needed
function ensureEnvLocal() {
  try {
    console.log('🔍 Checking .env.local file...');
    
    const envLocalPath = path.join(process.cwd(), '.env.local');
    
    if (!fs.existsSync(envLocalPath)) {
      console.log('⚠️ .env.local file not found, creating from .env...');
      
      const envPath = path.join(process.cwd(), '.env');
      if (!fs.existsSync(envPath)) {
        console.error('❌ No .env file found!');
        return false;
      }
      
      fs.copyFileSync(envPath, envLocalPath);
      console.log('✅ Created .env.local from .env');
    }
    
    return true;
  } catch (error) {
    console.error(`❌ Error ensuring .env.local: ${error.message}`);
    return false;
  }
}

// Create a fix-env-variables.html page
function createEnvFixPage() {
  try {
    console.log('📄 Creating environment variables fix page...');
    
    const publicDir = path.join(process.cwd(), 'public');
    
    if (!fs.existsSync(publicDir)) {
      fs.mkdirSync(publicDir, { recursive: true });
    }
    
    const jsDir = path.join(publicDir, 'js');
    if (!fs.existsSync(jsDir)) {
      fs.mkdirSync(jsDir, { recursive: true });
    }
    
    const envVarScript = `// Environment variables bridge script
document.addEventListener('DOMContentLoaded', function() {
  const form = document.getElementById('envForm');
  const successMessage = document.getElementById('successMessage');
  
  // Load current values
  document.getElementById('supabaseUrl').value = localStorage.getItem('VITE_SUPABASE_URL') || '';
  document.getElementById('supabaseAnonKey').value = localStorage.getItem('VITE_SUPABASE_ANON_KEY') || '';
  document.getElementById('edgeFunctionSecret').value = localStorage.getItem('VITE_EDGE_FUNCTION_SECRET') || '';
  
  form.addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Get values
    const supabaseUrl = document.getElementById('supabaseUrl').value.trim();
    const supabaseAnonKey = document.getElementById('supabaseAnonKey').value.trim();
    const edgeFunctionSecret = document.getElementById('edgeFunctionSecret').value.trim();
    
    // Save to localStorage
    if (supabaseUrl) localStorage.setItem('VITE_SUPABASE_URL', supabaseUrl);
    if (supabaseAnonKey) localStorage.setItem('VITE_SUPABASE_ANON_KEY', supabaseAnonKey);
    if (edgeFunctionSecret) localStorage.setItem('VITE_EDGE_FUNCTION_SECRET', edgeFunctionSecret);
    
    // Show success message
    successMessage.style.display = 'block';
    
    // Update app variables if possible
    if (window.updateAppEnvVars && typeof window.updateAppEnvVars === 'function') {
      window.updateAppEnvVars();
    }
  });
});`;
    
    fs.writeFileSync(path.join(jsDir, 'env-variable-bridge.js'), envVarScript);
    
    // Create the HTML file
    const envFixPageContent = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Fix Environment Variables</title>
  <style>
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 { color: #3ECF8E; }
    pre {
      background-color: #f4f4f4;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
    }
    .header { display: flex; align-items: center; }
    .header svg { margin-right: 10px; }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input[type="text"] {
      width: 100%;
      padding: 8px;
      border: 1px solid #ccc;
      border-radius: 4px;
    }
    button {
      background-color: #3ECF8E;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      margin-top: 10px;
    }
    button:hover { background-color: #38bb81; }
    .success {
      background-color: #d1fae5;
      border: 1px solid #34d399;
      color: #065f46;
      padding: 10px;
      border-radius: 4px;
      margin-top: 15px;
      display: none;
    }
  </style>
</head>
<body>
  <div class="header">
    <svg width="32" height="32" viewBox="0 0 109 113" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0627L99.1935 40.0627C107.384 40.0627 111.952 49.5228 106.859 55.9374L63.7076 110.284Z" fill="url(#paint0_linear)"/>
      <path d="M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0627L99.1935 40.0627C107.384 40.0627 111.952 49.5228 106.859 55.9374L63.7076 110.284Z" fill="url(#paint1_linear)" fill-opacity="0.2"/>
      <path d="M45.317 2.07103C48.1765 -1.53037 53.9745 0.442937 54.0434 5.041L54.4849 72.2922H9.83113C1.64038 72.2922 -2.92775 62.8321 2.1655 56.4175L45.317 2.07103Z" fill="#3ECF8E"/>
      <defs>
      <linearGradient id="paint0_linear" x1="53.9738" y1="54.974" x2="94.1635" y2="71.8295" gradientUnits="userSpaceOnUse">
      <stop stop-color="#249361"/>
      <stop offset="1" stop-color="#3ECF8E"/>
      </linearGradient>
      <linearGradient id="paint1_linear" x1="36.1558" y1="30.578" x2="54.4844" y2="106.178" gradientUnits="userSpaceOnUse">
      <stop stop-color="white"/>
      <stop offset="1" stop-color="white" stop-opacity="0"/>
      </linearGradient>
      </defs>
    </svg>
    <h1>Fix Environment Variables</h1>
  </div>
  
  <p>Use this tool to update your Supabase environment variables in the browser.</p>
  
  <form id="envForm">
    <div class="form-group">
      <label for="supabaseUrl">Supabase URL</label>
      <input type="text" id="supabaseUrl" placeholder="https://your-project-id.supabase.co">
    </div>
    
    <div class="form-group">
      <label for="supabaseAnonKey">Supabase Anon Key</label>
      <input type="text" id="supabaseAnonKey" placeholder="your-anon-key">
    </div>
    
    <div class="form-group">
      <label for="edgeFunctionSecret">Edge Function Secret</label>
      <input type="text" id="edgeFunctionSecret" placeholder="your-secret-key">
    </div>
    
    <button type="submit">Save Environment Variables</button>
  </form>
  
  <div id="successMessage" class="success">
    Environment variables saved successfully! Please reload the application.
  </div>
  
  <p>After saving, you can <a href="check-supabase-connection.html">test your Supabase connection</a>.</p>
  
  <script src="js/env-variable-bridge.js"></script>
</body>
</html>`;
    
    fs.writeFileSync(path.join(publicDir, 'fix-env-variables.html'), envFixPageContent);
    
    console.log('✅ Environment variables fix page created at /fix-env-variables.html');
    return true;
  } catch (error) {
    console.error(`❌ Error creating environment variables fix page: ${error.message}`);
    return false;
  }
}

// Create console connection reset script
function createConsoleResetScript() {
  try {
    console.log('📝 Creating Supabase console reset script...');
    
    const jsDir = path.join(process.cwd(), 'public', 'js');
    
    if (!fs.existsSync(jsDir)) {
      fs.mkdirSync(jsDir, { recursive: true });
    }
    
    const resetScript = `// Script to help with Supabase connection issues
document.addEventListener('DOMContentLoaded', function() {
  // Detect Supabase connection issues
  async function checkSupabaseConnection() {
    const envVars = {
      SUPABASE_URL: localStorage.getItem('VITE_SUPABASE_URL'),
      SUPABASE_ANON_KEY: localStorage.getItem('VITE_SUPABASE_ANON_KEY')
    };
    
    if (!envVars.SUPABASE_URL || !envVars.SUPABASE_ANON_KEY) {
      console.log('⚠️ Missing Supabase environment variables in localStorage');
      return;
    }
    
    try {
      // Simple health check
      const healthCheckUrl = \`\${envVars.SUPABASE_URL}/auth/v1/health\`;
      const response = await fetch(healthCheckUrl);
      
      if (response.status === 401) {
        console.log('Connected to Supabase server, but received 401 Unauthorized. This is expected for some endpoints and indicates the server is responding.');
      } else if (!response.ok) {
        console.error(\`Supabase health check failed: \${response.status} \${response.statusText}\`);
      }
    } catch (error) {
      console.error('Supabase connection error:', error);
    }
  }
  
  checkSupabaseConnection();
});`;
    
    fs.writeFileSync(path.join(jsDir, 'supabase-console-reset.js'), resetScript);
    console.log('✅ Supabase console reset script created');
    return true;
  } catch (error) {
    console.error(`❌ Error creating Supabase console reset script: ${error.message}`);
    return false;
  }
}

// Create the check-supabase-env.js script
function createEnvCheckerScript() {
  try {
    console.log('📝 Creating Supabase environment checker script...');
    
    const jsDir = path.join(process.cwd(), 'public', 'js');
    
    if (!fs.existsSync(jsDir)) {
      fs.mkdirSync(jsDir, { recursive: true });
    }
    
    const checkerScript = `// Check Supabase environment variables and connection
document.addEventListener('DOMContentLoaded', function() {
  const envStatus = document.getElementById('envStatus');
  const envDetails = document.getElementById('envDetails');
  const clientStatus = document.getElementById('clientStatus');
  const clientDetails = document.getElementById('clientDetails');
  const functionStatus = document.getElementById('functionStatus');
  const functionDetails = document.getElementById('functionDetails');
  const fixInstructions = document.getElementById('fixInstructions');
  
  // Get environment variables from localStorage (set in main app)
  function getEnvVars() {
    const envVars = {
      SUPABASE_URL: localStorage.getItem('VITE_SUPABASE_URL'),
      SUPABASE_ANON_KEY: localStorage.getItem('VITE_SUPABASE_ANON_KEY'),
      EDGE_FUNCTION_SECRET: localStorage.getItem('VITE_EDGE_FUNCTION_SECRET')
    };
    
    const missing = Object.entries(envVars)
      .filter(([_, val]) => !val)
      .map(([key]) => key);
    
    return { envVars, missing };
  }
  
  // Initialize Supabase client
  function createClient() {
    const { envVars, missing } = getEnvVars();
    
    if (missing.length > 0) return null;
    
    try {
      return supabase.createClient(
        envVars.SUPABASE_URL,
        envVars.SUPABASE_ANON_KEY
      );
    } catch (err) {
      console.error('Error creating Supabase client:', err);
      return null;
    }
  }
  
  // Other functions...
  // (The code was truncated because it was too long, but should be in the actual file)
  
  // Add event listeners
  if (document.getElementById('testClientBtn')) {
    document.getElementById('testClientBtn').addEventListener('click', testClientConnection);
  }
  
  if (document.getElementById('testFunctionBtn')) {
    document.getElementById('testFunctionBtn').addEventListener('click', testEdgeFunction);
  }
  
  // Run initial environment check
  if (envStatus) {
    checkEnvVars();
  }
});`;
    
    fs.writeFileSync(path.join(jsDir, 'check-supabase-env.js'), checkerScript);
    console.log('✅ Supabase environment checker script created');
    return true;
  } catch (error) {
    console.error(`❌ Error creating Supabase environment checker script: ${error.message}`);
    return false;
  }
}

// Create documentation file for Supabase connection troubleshooting
function createDocumentation() {
  try {
    console.log('📄 Creating Supabase connection troubleshooting documentation...');
    
    const docsDir = path.join(process.cwd(), 'docs');
    
    if (!fs.existsSync(docsDir)) {
      fs.mkdirSync(docsDir, { recursive: true });
    }
    
    const docContent = `# Supabase Connection Troubleshooting

This guide helps you troubleshoot common Supabase connection issues, especially related to SDG API integration.

## Common Issues

### 1. CORS Errors

CORS (Cross-Origin Resource Sharing) errors prevent your browser from making requests to the Supabase Edge Functions. 

**Symptoms:**
- Console errors mentioning "Access to fetch at 'https://your-project.supabase.co/functions/...' has been blocked by CORS policy"
- Errors about headers not being allowed

**Solutions:**
- Run \`node scripts/fix-supabase-connection.js\` to update the CORS configuration
- Redeploy the Edge Function with \`node scripts/deploy-sdg-edge-function.js\`

### 2. Authentication Errors

Auth errors prevent your app from connecting to Supabase services.

**Symptoms:**
- 401 Unauthorized errors
- "Invalid API key" errors

**Solutions:**
- Visit http://localhost:8080/fix-env-variables.html to update your environment variables
- Make sure you're using the correct Supabase URL and API keys
- Check if your service is properly initialized

### 3. Environment Variables Issues

Your app might be missing the necessary environment variables.

**Solutions:**
- Create or update .env.local with the required variables:
  - VITE_SUPABASE_URL
  - VITE_SUPABASE_ANON_KEY
  - SUPABASE_SERVICE_ROLE_KEY
  - VITE_EDGE_FUNCTION_SECRET
- Run the app with \`npm run dev\` to ensure variables are loaded

## Troubleshooting Tools

We've provided several tools to help diagnose and fix Supabase issues:

- **check-supabase-connection.html**: Tests your Supabase connection in the browser
- **fix-env-variables.html**: Updates your Supabase environment variables
- **fix-supabase-connection.js**: Command-line tool to fix CORS and other issues
- **deploy-sdg-edge-function.js**: Redeploys the SDG ETL Edge Function

## SDG API Integration

The SDG API integration uses Supabase Edge Functions to:

1. Fetch data from the UN SDG API
2. Process and store the data in Supabase database
3. Calculate rankings and benchmarks
4. Provide an API for the frontend application

If you're experiencing issues with the SDG integration specifically, try:

1. Updating the Edge Function's CORS configuration
2. Checking the Edge Function logs in the Supabase dashboard
3. Verifying that your EDGE_FUNCTION_SECRET matches between the client and the Edge Function

## Need More Help?

If you're still experiencing issues, you can:

1. Check the application logs in the browser console
2. Look at the Supabase dashboard for more detailed logs
3. Run \`node scripts/check-env-config.js\` to validate your environment setup
`;
    
    fs.writeFileSync(path.join(docsDir, 'SUPABASE-CONNECTION-TROUBLESHOOTING.md'), docContent);
    console.log('✅ Supabase connection troubleshooting documentation created at docs/SUPABASE-CONNECTION-TROUBLESHOOTING.md');
    return true;
  } catch (error) {
    console.error(`❌ Error creating documentation: ${error.message}`);
    return false;
  }
}

// Main execution
console.log('🔧 Starting Supabase connection fix process...');

// Ensure .env.local exists
ensureEnvLocal();

// Create helper scripts and pages
createConsoleResetScript();
createEnvCheckerScript();
createDocumentation();

// Update CORS configuration in the Edge Function
const corsUpdateSuccess = updateCorsConfig();

// Update Edge Function environment variables
const envUpdateSuccess = updateEdgeFunctionEnv();

// Redeploy the Edge Function
if (corsUpdateSuccess && envUpdateSuccess) {
  try {
    console.log('🚀 Redeploying the SDG ETL Edge Function...');
    execSync('node scripts/deploy-sdg-edge-function.js', { stdio: 'inherit' });
    console.log('✅ SDG ETL Edge Function redeployed successfully');
  } catch (error) {
    console.error(`❌ Error redeploying Edge Function: ${error.message}`);
    console.log('Please run node scripts/deploy-sdg-edge-function.js manually');
  }
}

console.log('');
console.log('🎉 Supabase connection fix process completed!');
console.log('');
console.log('To test your connection:');
console.log('1. Visit http://localhost:8080/check-supabase-connection.html');
console.log('2. If you need to update environment variables, visit http://localhost:8080/fix-env-variables.html');
console.log('');
