/**
 * Fix Environment Variables script
 * 
 * This script directly creates a simple secret key that's exactly the same for both
 * the frontend and the Edge Functions, overcoming the issues with environment
 * variables not being properly synced in the Edge Function environment.
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

// Define colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m'
};

// Load environment variables
console.log(`${colors.blue}Loading environment variables...${colors.reset}`);
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

// Define a very simple secret key that will work reliably
const SIMPLE_SECRET = 'SIMPLE_SDG_SECRET_KEY_2025';

console.log(`${colors.yellow}Creating a simple secret key for Edge Functions: ${SIMPLE_SECRET}${colors.reset}`);

// Update the local .env file
const envLocalPath = '.env.local';
let envLocalContent = fs.readFileSync(envLocalPath, 'utf8');
if (envLocalContent.includes('VITE_EDGE_FUNCTION_SECRET=')) {
  envLocalContent = envLocalContent.replace(
    /VITE_EDGE_FUNCTION_SECRET=.*/,
    `VITE_EDGE_FUNCTION_SECRET=${SIMPLE_SECRET}`
  );
} else {
  envLocalContent += `\n# Simple Edge Function Secret\nVITE_EDGE_FUNCTION_SECRET=${SIMPLE_SECRET}\n`;
}
fs.writeFileSync(envLocalPath, envLocalContent);
console.log(`${colors.green}✓ Updated ${envLocalPath}${colors.reset}`);

// Update Edge Function .env files
const edgeFunctions = ['debug', 'sdg-etl'];
for (const func of edgeFunctions) {
  const envPath = path.join('supabase', 'functions', func, '.env');
  let envContent = fs.readFileSync(envPath, 'utf8');
  if (envContent.includes('EDGE_FUNCTION_SECRET=')) {
    envContent = envContent.replace(
      /EDGE_FUNCTION_SECRET=.*/,
      `EDGE_FUNCTION_SECRET=${SIMPLE_SECRET}`
    );
  } else {
    envContent += `\nEDGE_FUNCTION_SECRET=${SIMPLE_SECRET}\n`;
  }
  fs.writeFileSync(envPath, envContent);
  console.log(`${colors.green}✓ Updated ${envPath}${colors.reset}`);
}

// Deploy both functions
console.log(`${colors.blue}\nDeploying Edge Functions with the simple secret key...${colors.reset}`);
try {
  console.log(`${colors.yellow}Deploying debug function...${colors.reset}`);
  execSync('node scripts/deploy-debug-function.js', { stdio: 'inherit' });
  
  console.log(`${colors.yellow}\nDeploying sdg-etl function...${colors.reset}`);
  execSync('node scripts/deploy-sdg-edge-function.js', { stdio: 'inherit' });
  
  console.log(`${colors.green}\n✓ Successfully deployed both Edge Functions with the simple secret key${colors.reset}`);
  console.log(`${colors.blue}\nVerifying deployments...${colors.reset}`);
  
  console.log(`${colors.yellow}Testing debug function...${colors.reset}`);
  execSync('node src/commands/test-debug-verbose.js', { stdio: 'inherit' });
  
  console.log(`${colors.yellow}\nTesting SDG ETL function...${colors.reset}`);
  execSync('node src/commands/test-sdg-auth.js', { stdio: 'inherit' });
  
  console.log(`${colors.green}\n✓ Environment fix complete!${colors.reset}`);
} catch (error) {
  console.error(`${colors.red}\n✗ Error during deployment or testing: ${error.message}${colors.reset}`);
  process.exit(1);
}
