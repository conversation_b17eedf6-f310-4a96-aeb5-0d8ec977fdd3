// <PERSON>ript to deploy the SDG ETL Edge Function to Supabase
import dotenv from 'dotenv';
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Required environment variables
const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;
const EDGE_FUNCTION_SECRET = process.env.VITE_EDGE_FUNCTION_SECRET;

// Validate environment variables
if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY || !EDGE_FUNCTION_SECRET) {
  console.error('❌ Missing required environment variables.');
  console.error('Please ensure .env.local contains VITE_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, and VITE_EDGE_FUNCTION_SECRET');
  process.exit(1);
}

// Get project ID from URL
const projectId = SUPABASE_URL.match(/https:\/\/([^.]+)/)?.[1];
if (!projectId) {
  console.error('❌ Invalid Supabase URL');
  process.exit(1);
}

// Main deployment function
async function deployEdgeFunction() {
  try {
    console.log('Deploying SDG ETL Edge Function...');
    
    // Ensure storage bucket exists for SDG results
    console.log('Ensuring storage bucket exists...');
    try {
      execSync(`npx supabase storage create sdg-api-results`, { stdio: 'inherit' });
    } catch (error) {
      // Bucket may already exist, which would cause an error
      console.log('Storage bucket verified.');
    }
    
// Create environment file with obfuscated secrets logging
console.log('Creating environment file for the Edge Function...');
const envDir = path.join(process.cwd(), 'supabase', 'functions', 'sdg-etl');

// Log masked versions of secrets for debugging
const maskedServiceKey = SUPABASE_SERVICE_ROLE_KEY ? 
  `${SUPABASE_SERVICE_ROLE_KEY.substr(0, 5)}...${SUPABASE_SERVICE_ROLE_KEY.substr(-5)}` : 
  '<missing>';

const maskedEdgeSecret = EDGE_FUNCTION_SECRET ? 
  `${EDGE_FUNCTION_SECRET.substr(0, 5)}...${EDGE_FUNCTION_SECRET.substr(-5)}` : 
  '<missing>';

console.log(`Using:
- Supabase URL: ${SUPABASE_URL ? SUPABASE_URL.split('//')[1].split('.')[0] + '...' : '<missing>'}
- Service Key: ${maskedServiceKey}
- Edge Secret: ${maskedEdgeSecret}`);

// Create actual content
const envContent = `SUPABASE_URL=${SUPABASE_URL}
SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
EDGE_FUNCTION_SECRET=${EDGE_FUNCTION_SECRET}`;

// Write to file with restricted permissions
fs.writeFileSync(path.join(envDir, '.env'), envContent, { mode: 0o600 });
console.log('Environment file created for the Edge Function with restricted permissions.');
    
    // Deploy the Edge Function
    console.log('Deploying SDG ETL Edge Function...');
    execSync(`npx supabase functions deploy sdg-etl --project-ref ${projectId} --no-verify-jwt`, { stdio: 'inherit' });
    
    // Test the deployment
    console.log('Verifying Edge Function deployment...');
    try {
      execSync('node src/commands/fetch-sdg-data.js --test-only', { stdio: 'inherit' });
      console.log('✅ SDG ETL Edge Function deployed and verified successfully!');
    } catch (error) {
      console.warn('⚠️ Warning: Could not verify Edge Function deployment:', error.message);
      console.warn('Please check the Edge Function manually using the fetch-sdg-data.js script.');
    }
    
    console.log('Deployment process completed.');
  } catch (error) {
    console.error('❌ Error deploying Edge Function:', error.message);
    process.exit(1);
  }
}

// Run deployment
deployEdgeFunction();
