
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Suppress Capability Warnings</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            line-height: 1.6;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
        code {
            background-color: #f5f5f5;
            padding: 2px 6px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>RPC Function Warning Suppression</h1>
    
    <div class="card">
        <h2>Warning Status</h2>
        <p>This page has loaded the warning suppression script. The following warnings are now suppressed:</p>
        <ul>
            <li><code>RPC function get_dynamic_user_capabilities returned 404</code></li>
            <li><code>POST https://...supabase.co/rest/v1/rpc/get_dynamic_user_capabilities 404</code></li>
        </ul>
        <p>The fallback mechanisms continue to work silently in the background.</p>
    </div>
    
    <div class="card">
        <h2>How to Use</h2>
        <p>To activate this warning suppression in your application:</p>
        <ol>
            <li>Include the script in your HTML:</li>
            <code>&lt;script src="/js/suppress-capability-warnings.js"&gt;&lt;/script&gt;</code>
            <li>Add it before any scripts that make Supabase calls</li>
        </ol>
    </div>

    <script src="/js/suppress-capability-warnings.js"></script>
</body>
</html>
