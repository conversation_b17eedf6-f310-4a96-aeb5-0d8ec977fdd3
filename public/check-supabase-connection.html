<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Supabase Connection Checker</title>
  <style>
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
      line-height: 1.5;
      padding: 2rem;
      max-width: 800px;
      margin: 0 auto;
      color: #333;
    }
    .card {
      background-color: #f9f9f9;
      border-radius: 8px;
      padding: 1.5rem;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      margin-bottom: 1.5rem;
    }
    h1, h2 {
      color: #0055aa;
    }
    .buttons {
      display: flex;
      gap: 1rem;
      margin: 1.5rem 0;
    }
    button {
      padding: 0.75rem 1.5rem;
      background-color: #0070f3;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 1rem;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    button:hover {
      background-color: #0055aa;
    }
    pre {
      background-color: #f4f4f4;
      padding: 1rem;
      border-radius: 4px;
      overflow-x: auto;
    }
    code {
      font-family: monospace;
      background-color: #f4f4f4;
      padding: 0.2rem 0.4rem;
      border-radius: 3px;
      font-size: 0.9rem;
    }
    .links {
      display: flex;
      gap: 1rem;
      margin-top: 1rem;
    }
    .links a {
      display: inline-block;
      padding: 0.5rem 1rem;
      background-color: #f4f4f4;
      color: #0055aa;
      border-radius: 4px;
      text-decoration: none;
      font-weight: 500;
    }
    .links a:hover {
      background-color: #e0e0e0;
    }
    #console-output {
      height: 400px;
      overflow-y: auto;
      padding: 1rem;
      background-color: #2d2d2d;
      color: #f0f0f0;
      font-family: monospace;
      border-radius: 4px;
      margin-top: 1rem;
    }
    .success {
      color: #4caf50;
    }
    .warning {
      color: #ff9800;
    }
    .error {
      color: #f44336;
    }
  </style>
</head>
<body>
  <h1>Supabase Connection Checker</h1>
  
  <div class="card">
    <h2>About This Tool</h2>
    <p>This tool helps diagnose issues with Supabase connection and authentication. It checks:</p>
    <ul>
      <li>Environment variables in your application</li>
      <li>Supabase configuration in localStorage</li>
      <li>Connection to the Supabase API</li>
      <li>Authentication tokens</li>
    </ul>
    <p>Open your browser console to see detailed results (press F12 or right-click → Inspect → Console).</p>
  </div>
  
  <div class="buttons">
    <button id="checkBtn">Run Connection Check</button>
    <button id="resetBtn">Reset Supabase Connection</button>
  </div>
  
  <div class="card">
    <h2>Console Output</h2>
    <div id="console-output">Results will appear here after running the check...</div>
  </div>
  
  <div class="card">
    <h2>Troubleshooting Links</h2>
    <div class="links">
      <a href="/reset-supabase.html">Reset Supabase Connection</a>
      <a href="/">Return to Main App</a>
      <a href="https://app.supabase.com/project/_" target="_blank">Supabase Dashboard</a>
    </div>
  </div>

  <script type="module">
    // Override console methods to capture output
    const consoleOutput = document.getElementById('console-output');
    const originalConsole = {
      log: console.log,
      warn: console.warn,
      error: console.error
    };
    
    function escapeHTML(text) {
      return text
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');
    }
    
    // Append output to the console div
    function appendToOutput(text, type = '') {
      const line = document.createElement('div');
      line.className = type;
      
      // Handle styled console logs
      if (text.includes('%c') && Array.isArray(arguments[0])) {
        // Just append the plain text without styling for now
        line.innerHTML = escapeHTML(arguments[0][0].replace(/%c/g, ''));
      } else {
        line.innerHTML = escapeHTML(text);
      }
      
      consoleOutput.appendChild(line);
      consoleOutput.scrollTop = consoleOutput.scrollHeight; // Auto-scroll
    }
    
    // Override console methods
    console.log = function() {
      originalConsole.log.apply(console, arguments);
      appendToOutput(Array.from(arguments).join(' '));
    };
    
    console.warn = function() {
      originalConsole.warn.apply(console, arguments);
      appendToOutput(Array.from(arguments).join(' '), 'warning');
    };
    
    console.error = function() {
      originalConsole.error.apply(console, arguments);
      appendToOutput(Array.from(arguments).join(' '), 'error');
    };
    
    // Handle check button
    document.getElementById('checkBtn').addEventListener('click', async () => {
      consoleOutput.innerHTML = '';
      appendToOutput('Running Supabase connection check...\n');
      
      try {
        // Import and run the checker script
        const module = await import('./js/check-supabase-env.js');
        appendToOutput('\nCheck complete! See browser console (F12) for detailed results.');
      } catch (error) {
        console.error('Error running checker:', error);
        appendToOutput('\nError running check. See browser console for details.', 'error');
      }
    });
    
    // Handle reset button - redirect to the reset page
    document.getElementById('resetBtn').addEventListener('click', () => {
      window.location.href = '/reset-supabase.html';
    });
  </script>
</body>
</html>
