<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Supabase Connection Reset</title>
  <style>
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
      line-height: 1.5;
      padding: 2rem;
      max-width: 800px;
      margin: 0 auto;
      color: #333;
    }
    .card {
      background-color: #f9f9f9;
      border-radius: 8px;
      padding: 1.5rem;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      margin-bottom: 1.5rem;
    }
    h1, h2 {
      color: #0055aa;
    }
    .buttons {
      display: flex;
      gap: 1rem;
      margin: 1.5rem 0;
    }
    button {
      padding: 0.75rem 1.5rem;
      background-color: #0070f3;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 1rem;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    button:hover {
      background-color: #0055aa;
    }
    #status {
      margin-top: 1.5rem;
      padding: 1rem;
      border-radius: 4px;
    }
    .success {
      background-color: #d4edda;
      color: #155724;
    }
    .error {
      background-color: #f8d7da;
      color: #721c24;
    }
    .info {
      background-color: #e2f3fc;
      color: #0c5460;
    }
    pre {
      background-color: #f4f4f4;
      padding: 1rem;
      border-radius: 4px;
      overflow-x: auto;
    }
    code {
      font-family: monospace;
      background-color: #f4f4f4;
      padding: 0.2rem 0.4rem;
      border-radius: 3px;
      font-size: 0.9rem;
    }
    ol li, ul li {
      margin-bottom: 0.5rem;
    }
    .copy-btn {
      background-color: #0070f3;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 0.5rem 1rem;
      font-size: 0.9rem;
      cursor: pointer;
      margin-top: 0.5rem;
      transition: background-color 0.2s;
    }
    .copy-btn:hover {
      background-color: #0055aa;
    }
    .code-block {
      position: relative;
    }
    .code-block .copy-btn {
      position: absolute;
      top: 0.5rem;
      right: 0.5rem;
    }
  </style>
</head>
<body>
  <h1>Supabase Connection Reset Tool</h1>
  
  <div class="card">
    <h2>Current Supabase Configuration</h2>
    <p>Use this tool to reset your Supabase connection state when you've updated credentials or experiencing authentication issues.</p>
    <pre id="config">Loading configuration...</pre>
  </div>
  
  <div class="buttons">
    <button id="resetBtn">Reset Supabase Connection</button>
    <button id="testConnBtn">Test Connection</button>
    <button id="clearStorageBtn">Clear All LocalStorage</button>
  </div>
  
  <div id="status" class="info">
    Ready to reset Supabase connection.
  </div>
  
  <div class="card">
    <h2>Browser Console Reset Script</h2>
    <p>You can also use this script directly in your browser console (F12) to reset the connection:</p>
    <div class="code-block">
      <pre><code id="console-script">// Loading script...</code></pre>
      <button class="copy-btn" id="copy-script-btn">Copy Script</button>
    </div>
    <p>
      <strong>Instructions:</strong> 
      <ol>
        <li>Open your browser console (F12 or right-click → Inspect → Console)</li>
        <li>Paste the copied script and press Enter</li>
        <li>Follow the instructions in the console output</li>
        <li>Refresh the page to apply the changes</li>
      </ol>
    </p>
  </div>
  
  <div class="card">
    <h2>Troubleshooting Tips</h2>
    <ol>
      <li>Try clicking <strong>Reset Supabase Connection</strong> first. This clears all tokens and sets the proper API key.</li>
      <li>Then click <strong>Test Connection</strong> to verify connectivity to your Supabase project.</li>
      <li>If test succeeds, return to the application and attempt login again.</li>
      <li>If test fails, the project URL or API key may be incorrect.</li>
      <li>If you are still having issues after resetting, try the <a href="fix-env-variables.html" style="font-weight: bold; color: #0070f3;">Environment Variables Fix Tool</a> which handles problems caused by switching from .env to .env.local.</li>
    </ol>
    <p>You can also check your connection in the browser console with this script:</p>
    <pre>// Paste this in your browser console to check connectivity
const checkSupabase = async () => {
  const url = localStorage.getItem('supabase_url') || 'https://wgsnpiskyczxhojlrwtr.supabase.co';
  const key = localStorage.getItem('supabase_anon_key');
  console.log("Testing URL:", url);
  console.log("Key present:", !!key);
  
  try {
    const resp = await fetch(`${url}/auth/v1/health`, {
      headers: {
        'apikey': key,
        'Authorization': `Bearer ${key}`
      }
    });
    console.log("Response status:", resp.status);
    // 401 is expected for anonymous users with auth endpoints
    console.log("Connection appears " + (resp.status === 401 ? "good" : "problematic"));
  } catch (err) {
    console.error("Connection failed:", err);
  }
};
checkSupabase();</pre>
  </div>
  
  <div class="card">
    <h2>Common Error Causes</h2>
    <ul>
      <li><strong>Invalid API Key</strong> - The API key may be expired, incorrect, or missing. Reset to restore default key.</li>
      <li><strong>Token Corruption</strong> - Authentication tokens can sometimes become corrupted. Reset clears them.</li>
      <li><strong>Browser Cache</strong> - Sometimes the browser cache can cause issues. Try clearing browser data.</li>
      <li><strong>Multiple Client Instances</strong> - This application uses a singleton pattern for Supabase clients. Reset helps reinitialize correctly.</li>
    </ul>
  </div>
  
  <div class="card" style="margin-top: 20px;">
    <h2>Next Steps</h2>
    <p>After resetting, return to the application to try logging in with the new Supabase connection:</p>
    <a href="/" style="display: inline-block; padding: 10px 20px; background-color: #0070f3; color: white; text-decoration: none; border-radius: 4px; margin-top: 10px;">Return to Application</a>
  </div>

  <script type="module">
    // Fetch environment variables from a controller endpoint
    async function fetchSupabaseConfig() {
      try {
        // Get the URL and key from localStorage if they exist
        const supabaseUrl = localStorage.getItem('supabase_url');
        const supabaseKey = localStorage.getItem('supabase_anon_key');
        
        const configElement = document.getElementById('config');
        if (supabaseUrl) {
          const keyInfo = supabaseKey ? 
            `ANON_KEY: ${supabaseKey.substring(0, 10)}...${supabaseKey.substring(supabaseKey.length - 5)}` : 
            'ANON_KEY: [not found]';
          
          configElement.textContent = `Current Supabase URL: ${supabaseUrl}\n${keyInfo}\n\nAPI Health: Use 'Test Connection' button to check connectivity`;
        } else {
          configElement.textContent = 'No Supabase configuration found in localStorage';
        }
      } catch (error) {
        console.error('Error fetching config:', error);
      }
    }
    
    fetchSupabaseConfig();
    
    // Log storage items
    function logStorageItems() {
      console.log('Current localStorage items:');
      const items = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        items.push(key);
        console.log(`- ${key}`);
      }
      return items;
    }
    
    const storageItems = logStorageItems();
    
    // Handle reset button
    document.getElementById('resetBtn').addEventListener('click', async () => {
      const statusElement = document.getElementById('status');
      statusElement.className = 'info';
      statusElement.textContent = 'Resetting Supabase connection...';
      
      try {
        // Reset the Supabase connection by clearing localStorage
        for (let i = localStorage.length - 1; i >= 0; i--) {
          const key = localStorage.key(i);
          if (key && (
            key.includes('supabase') || 
            key.includes('sb-') ||
            key.includes('stat-linker') ||
            key === 'auth-token' ||
            key === 'stat-linker-custom-auth-token-v1'
          )) {
            console.log(`Removing localStorage item: ${key}`);
            localStorage.removeItem(key);
          }
        }
        
        // Store the URL and anon key in localStorage for reference and testing
        localStorage.setItem('supabase_url', 'https://wgsnpiskyczxhojlrwtr.supabase.co');
        localStorage.setItem('supabase_anon_key', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.qDngOlzjtwoH6fJNL6iQpRXXmFlNm9H6k5_JqXZ4ORQ');
        
        statusElement.className = 'success';
        statusElement.textContent = `Supabase connection reset successful! All Supabase-related data cleared.`;
        
        fetchSupabaseConfig();
        logStorageItems();
      } catch (error) {
        console.error('Error resetting Supabase connection:', error);
        statusElement.className = 'error';
        statusElement.textContent = `Error: ${error.message}`;
      }
    });
    
    // Handle test connection button
    document.getElementById('testConnBtn').addEventListener('click', async () => {
      const statusElement = document.getElementById('status');
      statusElement.className = 'info';
      statusElement.textContent = 'Testing Supabase connection...';
      
      try {
        // Get the URL and anon key from localStorage or environment
        const supabaseUrl = localStorage.getItem('supabase_url') || 'https://wgsnpiskyczxhojlrwtr.supabase.co';
        const supabaseKey = localStorage.getItem('supabase_anon_key') || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.qDngOlzjtwoH6fJNL6iQpRXXmFlNm9H6k5_JqXZ4ORQ';
        
        // Test the auth health endpoint which is meant for this purpose
        const healthEndpoint = `${supabaseUrl}/auth/v1/health`;
        
        console.log('Testing connection to:', healthEndpoint);
        
        const response = await fetch(healthEndpoint, { 
          method: 'GET',
          cache: 'no-cache',
          headers: {
            'Content-Type': 'application/json',
            'apikey': supabaseKey,
            'Authorization': `Bearer ${supabaseKey}`
          }
        });
        
        console.log('Health check status:', response.status);
        
        // 401 is actually expected here with anon key (auth endpoints need authenticated users)
        // But what matters is that we get a response, not a network error
        if (response.status === 401 || response.status === 200) {
          statusElement.className = 'success';
          statusElement.textContent = `Connected to Supabase server, returned status ${response.status}. This is expected for the health endpoint with anon key.`;
        } else if (response.status === 404) {
          statusElement.className = 'error';
          statusElement.textContent = `Supabase project not found. Please check your project URL: ${supabaseUrl}`;
        } else {
          statusElement.className = 'error';
          statusElement.textContent = `Error connecting to Supabase: Status ${response.status}`;
        }
      } catch (error) {
        console.error('Error testing connection:', error);
        statusElement.className = 'error';
        statusElement.textContent = `Network error: ${error.message}. Please check your internet connection.`;
      }
    });
    
    // Handle clear all storage button
    document.getElementById('clearStorageBtn').addEventListener('click', () => {
      const statusElement = document.getElementById('status');
      
      try {
        localStorage.clear();
        statusElement.className = 'success';
        statusElement.textContent = 'All localStorage items cleared successfully.';
        logStorageItems();
      } catch (error) {
        console.error('Error clearing localStorage:', error);
        statusElement.className = 'error';
        statusElement.textContent = `Error: ${error.message}`;
      }
    });

    // Fetch and display the console script
    async function fetchConsoleScript() {
      try {
        const response = await fetch('/js/supabase-console-reset.js');
        const script = await response.text();
        document.getElementById('console-script').textContent = script;
      } catch (error) {
        console.error('Error loading console script:', error);
        document.getElementById('console-script').textContent = 'Error loading script: ' + error.message;
      }
    }
    
    fetchConsoleScript();
    
    // Handle copy script button
    document.getElementById('copy-script-btn').addEventListener('click', () => {
      const scriptText = document.getElementById('console-script').textContent;
      navigator.clipboard.writeText(scriptText).then(() => {
        const btn = document.getElementById('copy-script-btn');
        btn.textContent = 'Copied!';
        setTimeout(() => {
          btn.textContent = 'Copy Script';
        }, 2000);
      }).catch(err => {
        console.error('Error copying text:', err);
        alert('Failed to copy to clipboard. Please select the text manually.');
      });
    });
  </script>
</body>
</html>
