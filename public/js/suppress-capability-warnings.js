// Check if originalWarn has already been declared in the global scope
if (typeof window.__warnPatched === 'undefined') {
  // Save the flag to prevent multiple executions
  window.__warnPatched = true;
  
  // Store the original console.warn function
  const originalWarn = console.warn;

  console.warn = function(...args) {
    // Skip warnings that match specific patterns
    if (args[0] && typeof args[0] === 'string') {
      // Get Supabase URL from environment if available
      const supabaseUrl = window.ENV_SUPABASE_URL || 
                          (typeof process !== 'undefined' && process.env && process.env.VITE_SUPABASE_URL) ||
                          '';
      
      const patterns = [
        'RPC function get_dynamic_user_capabilities returned 404',
        `POST ${supabaseUrl}/rest/v1/rpc/get_dynamic_user_capabilities 404`,
        'RPC function get_user_caps returned 404',
        `POST ${supabaseUrl}/rest/v1/rpc/get_user_caps 404`
      ];
      
      for (const pattern of patterns) {
        if (args[0].includes(pattern)) {
          // Instead of showing the warning, log at debug level
          const isDevelopment = localStorage && localStorage.getItem('env_mode') === 'development';
          const isVerbose = localStorage && localStorage.getItem('log_level') === 'verbose';
          
          if (isDevelopment && isVerbose) {
            console.debug('Suppressed warning:', args[0]);
          }
          return; // Skip this warning
        }
      }
    }
    
    // Show other warnings
    originalWarn.apply(console, args);
  };

  console.log('RPC function warnings have been suppressed for capabilities functions');
}
