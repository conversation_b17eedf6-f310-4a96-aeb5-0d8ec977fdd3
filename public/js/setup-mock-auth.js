// <PERSON>ript to set up a mock authentication session for development
(function() {
  // Create a mock user session
  const mockUser = {
    id: "mock-user-123",
    email: "<EMAIL>",
    user_metadata: {
      full_name: "Development User",
      avatar_url: null
    },
    app_metadata: {
      role: "admin"  // Give admin role for full access
    }
  };

  // Create mock session object
  const mockSession = {
    user: mockUser,
    access_token: "mock-token-" + Date.now(),
    refresh_token: "mock-refresh-token",
    expires_at: Date.now() + 3600 * 1000, // 1 hour from now
  };

  // Store in localStorage
  localStorage.setItem('stat-linker-mock-session', JSON.stringify(mockSession));
  console.log("Mock authentication session created:", mockUser.email);
  
  // Add message to page
  const mockAuthMessage = document.createElement('div');
  mockAuthMessage.style.position = 'fixed';
  mockAuthMessage.style.bottom = '10px';
  mockAuthMessage.style.right = '10px';
  mockAuthMessage.style.padding = '10px';
  mockAuthMessage.style.background = '#3ECF8E';
  mockAuthMessage.style.color = 'white';
  mockAuthMessage.style.borderRadius = '4px';
  mockAuthMessage.style.zIndex = '9999';
  mockAuthMessage.innerHTML = 'Mock auth session created: ' + mockUser.email;
  
  document.body.appendChild(mockAuthMessage);
  
  // Auto-remove the message after 5 seconds
  setTimeout(() => {
    mockAuthMessage.style.opacity = '0';
    mockAuthMessage.style.transition = 'opacity 0.5s';
    setTimeout(() => mockAuthMessage.remove(), 500);
  }, 5000);
})();
