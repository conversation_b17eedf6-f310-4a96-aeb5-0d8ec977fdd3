/**
 * Polyfills for browser compatibility
 * 
 * This file provides polyfills for functions that might not be available
 * in all browser environments
 */

// Immediately execute in global scope and set to window
(function(global) {
  // Ensure global is either window or self (for web workers)
  const globalObj = typeof window !== 'undefined' ? window : 
                   typeof self !== 'undefined' ? self : global;
  
  // Make sure crypto object exists
  if (typeof globalObj.crypto === 'undefined') {
    globalObj.crypto = {};
    console.log("Created crypto object");
  }
  
  // Polyfill for crypto.getRandomValues if needed
  if (!globalObj.crypto.getRandomValues) {
    globalObj.crypto.getRandomValues = function getRandomValues(array) {
      const bytes = new Uint8Array(array.length);
      for (let i = 0; i < bytes.length; i++) {
        bytes[i] = Math.floor(Math.random() * 256);
      }
      
      // Copy to the input array
      if (array.set) {
        array.set(bytes);
      } else {
        for (let i = 0; i < bytes.length; i++) {
          array[i] = bytes[i];
        }
      }
      
      return array;
    };
    console.log("Added polyfill for crypto.getRandomValues");
  }
  
  // Polyfill for crypto.randomUUID
  if (!globalObj.crypto.randomUUID) {
    globalObj.crypto.randomUUID = function randomUUID() {
      // Simple UUID v4 implementation
      return ([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g, c =>
        (c ^ (globalObj.crypto.getRandomValues ? 
          globalObj.crypto.getRandomValues(new Uint8Array(1))[0] : 
          Math.floor(Math.random() * 256)) & 15 >> c / 4).toString(16)
      );
    };
    console.log("Added polyfill for crypto.randomUUID");
  }
  
  // Ensure it's available globally in multiple ways
  // Direct global properties
  if (typeof globalObj.randomUUID === 'undefined') {
    globalObj.randomUUID = globalObj.crypto.randomUUID;
  }
  
  // For legacy scripts that might still use navigator.id
  if (typeof globalObj.navigator === 'undefined') {
    globalObj.navigator = {};
  }
  
  if (typeof globalObj.navigator.id === 'undefined') {
    globalObj.navigator.id = {};
  }
  
  if (typeof globalObj.navigator.id.uuid === 'undefined') {
    globalObj.navigator.id.uuid = function() {
      return globalObj.crypto.randomUUID();
    };
  }
  
  // Patch Math object for scripts that might try to create UUIDs from Math.random
  const originalRandom = Math.random;
  Math.random_uuid = function() {
    return globalObj.crypto.randomUUID();
  };
  
  // For direct content script access (important for browser extensions)
  if (typeof content !== 'undefined' && typeof content.crypto === 'undefined') {
    content.crypto = globalObj.crypto;
  }
  
  // Handle iframes and cross-frame content
  try {
    if (globalObj.frames && globalObj.frames.length > 0) {
      for (let i = 0; i < globalObj.frames.length; i++) {
        if (globalObj.frames[i] && globalObj.frames[i].crypto === undefined) {
          globalObj.frames[i].crypto = globalObj.crypto;
        }
      }
    }
  } catch (e) {
    // Ignore security errors from cross-origin frames
    console.log("Warning: Could not access all frames due to security restrictions");
  }
})(typeof globalThis !== 'undefined' ? globalThis : this);

// Add any other polyfills as needed
