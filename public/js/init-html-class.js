// Add appropriate class to the HTML element based on device detection
document.addEventListener('DOMContentLoaded', function() {
  // Detect if the device is mobile
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
                   (window.matchMedia && window.matchMedia('(max-width: 768px)').matches);
  
  // Add appropriate class to html element
  if (isMobile) {
    document.documentElement.classList.add('mobile');
  } else {
    document.documentElement.classList.add('desktop');
  }
  
  // Also attach to window resize for responsive behavior
  window.addEventListener('resize', function() {
    const isCurrentlyMobile = window.matchMedia('(max-width: 768px)').matches;
    
    if (isCurrentlyMobile && !document.documentElement.classList.contains('mobile')) {
      document.documentElement.classList.remove('desktop');
      document.documentElement.classList.add('mobile');
    } else if (!isCurrentlyMobile && !document.documentElement.classList.contains('desktop')) {
      document.documentElement.classList.remove('mobile');
      document.documentElement.classList.add('desktop');
    }
  });
});
