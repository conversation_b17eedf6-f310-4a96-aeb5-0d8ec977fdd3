// Check Supabase environment variables and connection
document.addEventListener('DOMContentLoaded', function() {
  const envStatus = document.getElementById('envStatus');
  const envDetails = document.getElementById('envDetails');
  const clientStatus = document.getElementById('clientStatus');
  const clientDetails = document.getElementById('clientDetails');
  const functionStatus = document.getElementById('functionStatus');
  const functionDetails = document.getElementById('functionDetails');
  const fixInstructions = document.getElementById('fixInstructions');
  
  // Get environment variables from localStorage (set in main app)
  function getEnvVars() {
    const envVars = {
      SUPABASE_URL: localStorage.getItem('VITE_SUPABASE_URL'),
      SUPABASE_ANON_KEY: localStorage.getItem('VITE_SUPABASE_ANON_KEY'),
      EDGE_FUNCTION_SECRET: localStorage.getItem('VITE_EDGE_FUNCTION_SECRET')
    };
    
    const missing = Object.entries(envVars)
      .filter(([_, val]) => !val)
      .map(([key]) => key);
    
    return { envVars, missing };
  }
  
  // Initialize Supabase client
  function createClient() {
    const { envVars, missing } = getEnvVars();
    
    if (missing.length > 0) return null;
    
    try {
      return supabase.createClient(
        envVars.SUPABASE_URL,
        envVars.SUPABASE_ANON_KEY
      );
    } catch (err) {
      console.error('Error creating Supabase client:', err);
      return null;
    }
  }
  
  // Other functions...
  // (The code was truncated because it was too long, but should be in the actual file)
  
  // Add event listeners
  if (document.getElementById('testClientBtn')) {
    document.getElementById('testClientBtn').addEventListener('click', testClientConnection);
  }
  
  if (document.getElementById('testFunctionBtn')) {
    document.getElementById('testFunctionBtn').addEventListener('click', testEdgeFunction);
  }
  
  // Run initial environment check
  if (envStatus) {
    checkEnvVars();
  }
});