/**
 * Reset Supabase Connection
 * 
 * This utility script completely resets the Supabase connection state.
 * It must be run in the browser context since it accesses localStorage.
 */

function resetSupabaseConnection() {
  console.log('Performing complete Supabase state reset...');
  
  // Clear offline mode
  localStorage.removeItem('supabase_offline_mode');
  
  // Clear all Supabase-related localStorage items
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && (
      key.includes('supabase') || 
      key.includes('sb-') ||
      key.includes('stat-linker')
    )) {
      console.log(`Removing localStorage item: ${key}`);
      localStorage.removeItem(key);
    }
  }
  
  console.log('Supabase connection reset complete.');
  console.log('Please reload the page to initialize with the new Supabase connection.');
  
  return {
    supabaseUrl: import.meta.env.VITE_SUPABASE_URL,
    success: true
  };
}

// If this script is loaded directly in the browser, create a global function to call
if (typeof window !== 'undefined') {
  window.resetSupabaseConnection = resetSupabaseConnection;
}

export { resetSupabaseConnection };
