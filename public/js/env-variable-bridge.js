// Environment variables bridge script
document.addEventListener('DOMContentLoaded', function() {
  const form = document.getElementById('envForm');
  const successMessage = document.getElementById('successMessage');
  
  // Load current values
  document.getElementById('supabaseUrl').value = localStorage.getItem('VITE_SUPABASE_URL') || '';
  document.getElementById('supabaseAnonKey').value = localStorage.getItem('VITE_SUPABASE_ANON_KEY') || '';
  document.getElementById('edgeFunctionSecret').value = localStorage.getItem('VITE_EDGE_FUNCTION_SECRET') || '';
  
  form.addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Get values
    const supabaseUrl = document.getElementById('supabaseUrl').value.trim();
    const supabaseAnonKey = document.getElementById('supabaseAnonKey').value.trim();
    const edgeFunctionSecret = document.getElementById('edgeFunctionSecret').value.trim();
    
    // Save to localStorage
    if (supabaseUrl) localStorage.setItem('VITE_SUPABASE_URL', supabaseUrl);
    if (supabaseAnonKey) localStorage.setItem('VITE_SUPABASE_ANON_KEY', supabaseAnonKey);
    if (edgeFunctionSecret) localStorage.setItem('VITE_EDGE_FUNCTION_SECRET', edgeFunctionSecret);
    
    // Show success message
    successMessage.style.display = 'block';
    
    // Update app variables if possible
    if (window.updateAppEnvVars && typeof window.updateAppEnvVars === 'function') {
      window.updateAppEnvVars();
    }
  });
});
