/**
 * Content Script Host Validation Fix
 * 
 * This script fixes the host validation issues in content scripts by providing
 * a more resilient validation mechanism that's compatible with our main application's
 * host validator.
 * 
 * Insert this script before other content scripts to ensure it's available.
 */

(function() {
  // Configuration for valid hosts
  const VALID_HOSTS = [
    // Development hosts
    'localhost',
    '127.0.0.1',
    // Testing hosts
    'test-stat-linker.gastat.gov.sa',
    // Production hosts
    'stat-linker.gastat.gov.sa',
    // Add any other valid hosts here
  ];

  // Configuration for analytics-enabled hosts
  const INSIGHTS_WHITELIST = [
    // Production hosts
    'stat-linker.gastat.gov.sa',
    // Add any analytics-enabled hosts here
  ];

  // Host types
  const HOST_TYPES = {
    DEVELOPMENT: 'development',
    TESTING: 'testing',
    STAGING: 'staging',
    PRODUCTION: 'production',
    UNKNOWN: 'unknown'
  };

  /**
   * Get the current hostname in a reliable way
   */
  function getHostName() {
    try {
      // For browser environments
      if (typeof window !== 'undefined' && window.location) {
        const hostname = window.location.hostname;
        return hostname || '';
      }
    } catch (e) {
      console.warn('[Content Script] Error getting hostname:', e);
    }
    
    return '';
  }

  /**
   * Determine the host type based on hostname
   */
  function getHostType(hostname = getHostName()) {
    // Development environments
    if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname.includes('.local')) {
      return HOST_TYPES.DEVELOPMENT;
    }
    
    // Testing environments
    if (hostname.includes('test-') || hostname.includes('.test.') || hostname.includes('-test')) {
      return HOST_TYPES.TESTING;
    }
    
    // Staging environments
    if (hostname.includes('staging-') || hostname.includes('.staging.') || hostname.includes('-staging')) {
      return HOST_TYPES.STAGING;
    }
    
    // Production environments
    if (hostname.includes('gastat.gov.sa')) {
      return HOST_TYPES.PRODUCTION;
    }
    
    // Unknown environment
    return HOST_TYPES.UNKNOWN;
  }

  /**
   * Check if the current host is valid
   */
  function isValidHost(hostname = getHostName()) {
    // Always consider development hosts valid
    if (getHostType(hostname) === HOST_TYPES.DEVELOPMENT) {
      return true;
    }
    
    // Handle empty hostname specially
    if (!hostname) {
      // In development, we'll consider empty hostname valid with a warning
      if (typeof process !== 'undefined' && process.env && process.env.NODE_ENV === 'development') {
        console.warn('[Content Script] Empty hostname detected, but allowing in development mode');
        return true;
      }
      return false;
    }
    
    // Check against whitelist
    return VALID_HOSTS.some(validHost => 
      hostname === validHost || hostname.endsWith('.' + validHost)
    );
  }

  /**
   * Check if the current host is in the insights whitelist
   */
  function isInsightsEnabled(hostname = getHostName()) {
    // Always enable insights for development hosts
    if (getHostType(hostname) === HOST_TYPES.DEVELOPMENT) {
      return true;
    }
    
    // Check against whitelist
    return INSIGHTS_WHITELIST.some(validHost => 
      hostname === validHost || hostname.endsWith('.' + validHost)
    );
  }

  /**
   * Get host validation result
   */
  function getHostValidation() {
    const hostName = getHostName();
    const hostType = getHostType(hostName);
    const isValid = isValidHost(hostName);
    const insightsEnabled = isInsightsEnabled(hostName);
    
    return {
      hostName,
      hostType,
      isValid,
      insightsEnabled
    };
  }

  // Apply fixes by exposing the validation functions globally
  window.__CONTENT_HOST_VALIDATION__ = {
    getHostName,
    getHostType,
    isValidHost,
    isInsightsEnabled,
    getHostValidation,
    HOST_TYPES
  };

  // Patch the existing host validation functions if they exist
  if (typeof window.isValidHost === 'function') {
    console.log('[Content Script] Patching existing host validation...');
    window.isValidHost = isValidHost;
  }

  if (typeof window.isHostInWhitelist === 'function') {
    console.log('[Content Script] Patching existing whitelist validation...');
    window.isHostInWhitelist = isInsightsEnabled;
  }

  // Initialize and log the validation status
  const validation = getHostValidation();
  if (validation.isValid) {
    console.log(`[Content Script] Host validated: ${validation.hostName} (${validation.hostType})`);
  } else {
    console.warn(`[Content Script] Host validation issue detected:`, validation);
  }
})();
