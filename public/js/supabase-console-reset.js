// Script to help with Supabase connection issues
document.addEventListener('DOMContentLoaded', function() {
  // Detect Supabase connection issues
  async function checkSupabaseConnection() {
    const envVars = {
      SUPABASE_URL: localStorage.getItem('VITE_SUPABASE_URL'),
      SUPABASE_ANON_KEY: localStorage.getItem('VITE_SUPABASE_ANON_KEY')
    };
    
    if (!envVars.SUPABASE_URL || !envVars.SUPABASE_ANON_KEY) {
      console.log('⚠️ Missing Supabase environment variables in localStorage');
      return;
    }
    
    try {
      // Simple health check
      const healthCheckUrl = `${envVars.SUPABASE_URL}/auth/v1/health`;
      const response = await fetch(healthCheckUrl);
      
      if (response.status === 401) {
        console.log('Connected to Supabase server, but received 401 Unauthorized. This is expected for some endpoints and indicates the server is responding.');
      } else if (!response.ok) {
        console.error(`Supabase health check failed: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.error('Supabase connection error:', error);
    }
  }
  
  checkSupabaseConnection();
});