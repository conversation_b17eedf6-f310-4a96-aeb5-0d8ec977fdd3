/**
 * Content Script Crypto Polyfill
 * 
 * This polyfill is specifically designed for content scripts that run in their own
 * JavaScript contexts and don't inherit polyfills from the main page.
 * 
 * To use:
 * 1. Include this script directly in content scripts via import or <script> tag
 * 2. Or bundle it with your content script build process
 */

(function() {
  // Self-executing function to install in the current context (content script)
  function installContentScriptPolyfill() {
    // Check if we already have the function
    if (window.crypto && window.crypto.randomUUID) {
      console.log('[Content Polyfill] crypto.randomUUID is already available');
      return;
    }
    
    console.log('[Content Polyfill] Installing crypto.randomUUID polyfill for content script');
    
    // Ensure the crypto object exists
    if (!window.crypto) {
      console.warn('[Content Polyfill] window.crypto not found, creating a basic implementation');
      window.crypto = {
        getRandomValues: function(array) {
          for (let i = 0; i < array.length; i++) {
            array[i] = Math.floor(Math.random() * 256);
          }
          return array;
        }
      };
    }
    
    // Add the randomUUID method
    window.crypto.randomUUID = function() {
      // RFC4122 version 4 compliant UUID implementation
      return '10000000-1000-4000-8000-100000000000'.replace(/[018]/g, function(c) {
        const num = Number(c);
        return (num ^ (window.crypto.getRandomValues(new Uint8Array(1))[0] & (15 >> (num / 4)))).toString(16);
      });
    };
    
    console.log('[Content Polyfill] crypto.randomUUID polyfill successfully installed in content script');
  }
  
  // Execute immediately
  installContentScriptPolyfill();
})();

// Also export as a module for import usage
if (typeof exports !== 'undefined') {
  exports.installContentScriptPolyfill = function() {
    // The function is auto-executed, but we provide this for explicit imports
    console.log('[Content Polyfill] Module imported');
  };
}
