/**
 * Global Crypto Polyfill
 * 
 * This script provides a global polyfill for crypto.randomUUID that works across
 * all frames and content scripts. It's designed to be loaded early in the page
 * lifecycle to ensure compatibility with older browsers.
 * 
 * Usage:
 * - Add to your HTML: <script src="/js/global-crypto-polyfill.js"></script>
 * - Or import in content scripts: import '/js/global-crypto-polyfill.js';
 */

(function() {
  // Check if we're in a browser environment
  if (typeof window === 'undefined') return;
  
  // Self-executing function to install the polyfill
  function installCryptoPolyfill() {
    // Already installed or natively supported
    if (window.crypto && window.crypto.randomUUID) {
      console.log('[Global Polyfill] crypto.randomUUID is natively supported');
      return;
    }
    
    console.log('[Global Polyfill] Installing global crypto.randomUUID polyfill');
    
    // Ensure crypto object exists
    if (!window.crypto) {
      console.warn('[Global Polyfill] window.crypto not found, creating a basic implementation');
      window.crypto = {
        getRandomValues: function(array) {
          for (let i = 0; i < array.length; i++) {
            array[i] = Math.floor(Math.random() * 256);
          }
          return array;
        }
      };
    }
    
    // Add randomUUID method to the crypto object
    window.crypto.randomUUID = function() {
      // Implementation based on RFC4122 version 4 UUID
      return '10000000-1000-4000-8000-100000000000'.replace(/[018]/g, function(c) {
        const num = Number(c);
        return (num ^ (window.crypto.getRandomValues(new Uint8Array(1))[0] & (15 >> (num / 4)))).toString(16);
      });
    };
    
    console.log('[Global Polyfill] Global crypto.randomUUID polyfill successfully installed');
  }
  
  // Install the polyfill immediately
  installCryptoPolyfill();
  
  // Also inject into any iframes that might be created later
  function injectIntoFrames() {
    try {
      // Current frames
      const frames = document.querySelectorAll('iframe');
      frames.forEach(frame => {
        try {
          if (frame.contentWindow && frame.contentWindow.crypto && !frame.contentWindow.crypto.randomUUID) {
            frame.contentWindow.crypto.randomUUID = window.crypto.randomUUID;
          }
        } catch (e) {
          // Might fail due to cross-origin restrictions, which is fine
          console.warn('[Global Polyfill] Could not inject into iframe (likely cross-origin)');
        }
      });
    } catch (e) {
      console.warn('[Global Polyfill] Error injecting into frames:', e);
    }
  }
  
  // Call once on load
  if (document.readyState === 'complete') {
    injectIntoFrames();
  } else {
    window.addEventListener('load', injectIntoFrames);
  }
  
  // Monitor for new iframes
  if (window.MutationObserver) {
    const observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        mutation.addedNodes.forEach(node => {
          if (node.tagName === 'IFRAME') {
            try {
              if (node.contentWindow && node.contentWindow.crypto && !node.contentWindow.crypto.randomUUID) {
                node.contentWindow.crypto.randomUUID = window.crypto.randomUUID;
              }
            } catch (e) {
              // Might fail due to cross-origin restrictions, which is fine
            }
          }
        });
      });
    });
    
    observer.observe(document.documentElement, {
      childList: true,
      subtree: true
    });
  }
})();
