<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Warning Suppression Controls</title>
  <style>
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background-color: #f5f5f5;
      padding: 1rem;
      max-width: 800px;
      margin: 0 auto;
      color: #333;
    }
    .card {
      background-color: white;
      border-radius: 8px;
      padding: 1.5rem;
      margin-bottom: 1rem;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    h1 {
      color: #2563eb;
      margin-top: 0;
    }
    h2 {
      color: #4b5563;
      margin-top: 0;
    }
    button {
      background-color: #2563eb;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
      margin-right: 0.5rem;
      margin-bottom: 0.5rem;
    }
    button:hover {
      background-color: #1d4ed8;
    }
    button.danger {
      background-color: #ef4444;
    }
    button.danger:hover {
      background-color: #dc2626;
    }
    button.secondary {
      background-color: #6b7280;
    }
    button.secondary:hover {
      background-color: #4b5563;
    }
    button.success {
      background-color: #10b981;
    }
    button.success:hover {
      background-color: #059669;
    }
    .status-indicator {
      display: inline-block;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      margin-right: 6px;
    }
    .status-on {
      background-color: #10b981;
    }
    .status-off {
      background-color: #6b7280;
    }
    .control-row {
      display: flex;
      align-items: center;
      margin-bottom: 0.75rem;
    }
    .status-label {
      margin-left: auto;
      font-size: 0.875rem;
      color: #6b7280;
    }
  </style>
</head>
<body>
  <div class="card">
    <h1>Warning Suppression Controls</h1>
    <p>
      This utility manages the warning suppression settings for the application.
      Use these controls to configure how console warnings are handled, particularly for Supabase RPC function errors.
    </p>
    
    <div class="card">
      <h2>Environment Settings</h2>
      <div class="control-row">
        <div>
          <span class="status-indicator" id="env-mode-indicator"></span>
          Development Mode
        </div>
        <span class="status-label" id="env-mode-status"></span>
      </div>
      <button id="toggle-env-mode">Toggle Development Mode</button>
      <button class="secondary" id="reset-env-mode">Reset to Default</button>
    </div>
    
    <div class="card">
      <h2>Logging Settings</h2>
      <div class="control-row">
        <div>
          <span class="status-indicator" id="log-level-indicator"></span>
          Verbose Logging
        </div>
        <span class="status-label" id="log-level-status"></span>
      </div>
      <button id="toggle-verbose">Toggle Verbose Logging</button>
      <button class="secondary" id="reset-log-level">Reset to Default</button>
    </div>
    
    <div class="card">
      <h2>Warning Suppression</h2>
      <div class="control-row">
        <div>
          <span class="status-indicator" id="suppression-indicator"></span>
          Active Suppression
        </div>
        <span class="status-label" id="suppression-status">Active</span>
      </div>
      <p>The application is currently configured to suppress warnings for:</p>
      <ul id="suppressed-warnings">
        <li>RPC function get_dynamic_user_capabilities returned 404</li>
        <li>POST https://wgsnpiskyczxhojlrwtr.supabase.co/rest/v1/rpc/get_dynamic_user_capabilities 404</li>
        <li>RPC function get_user_caps returned 404</li>
        <li>POST https://wgsnpiskyczxhojlrwtr.supabase.co/rest/v1/rpc/get_user_caps 404</li>
      </ul>
    </div>
    
    <div class="card">
      <h2>Actions</h2>
      <button id="reload-page">Apply Changes & Reload Page</button>
      <button class="secondary" id="back-to-app">Back to Application</button>
      <button class="danger" id="reset-all-settings">Reset All Settings</button>
    </div>
  </div>
  
  <script>
    // Initialize the UI based on current settings
    function updateUI() {
      // Get current settings
      const isDevelopment = localStorage.getItem('env_mode') === 'development';
      const isVerbose = localStorage.getItem('log_level') === 'verbose';
      
      // Update environment mode indicator
      const envModeIndicator = document.getElementById('env-mode-indicator');
      const envModeStatus = document.getElementById('env-mode-status');
      envModeIndicator.className = 'status-indicator ' + (isDevelopment ? 'status-on' : 'status-off');
      envModeStatus.textContent = isDevelopment ? 'Enabled' : 'Disabled';
      
      // Update log level indicator
      const logLevelIndicator = document.getElementById('log-level-indicator');
      const logLevelStatus = document.getElementById('log-level-status');
      logLevelIndicator.className = 'status-indicator ' + (isVerbose ? 'status-on' : 'status-off');
      logLevelStatus.textContent = isVerbose ? 'Enabled' : 'Disabled';
      
      // Update suppression indicator (always on, as it's built into the code)
      const suppressionIndicator = document.getElementById('suppression-indicator');
      suppressionIndicator.className = 'status-indicator status-on';
    }
    
    // Event handlers
    document.getElementById('toggle-env-mode').addEventListener('click', () => {
      const current = localStorage.getItem('env_mode');
      if (current === 'development') {
        localStorage.removeItem('env_mode');
      } else {
        localStorage.setItem('env_mode', 'development');
      }
      updateUI();
    });
    
    document.getElementById('reset-env-mode').addEventListener('click', () => {
      localStorage.removeItem('env_mode');
      updateUI();
    });
    
    document.getElementById('toggle-verbose').addEventListener('click', () => {
      const current = localStorage.getItem('log_level');
      if (current === 'verbose') {
        localStorage.removeItem('log_level');
      } else {
        localStorage.setItem('log_level', 'verbose');
      }
      updateUI();
    });
    
    document.getElementById('reset-log-level').addEventListener('click', () => {
      localStorage.removeItem('log_level');
      updateUI();
    });
    
    document.getElementById('reload-page').addEventListener('click', () => {
      window.location.reload();
    });
    
    document.getElementById('back-to-app').addEventListener('click', () => {
      window.location.href = '/';
    });
    
    document.getElementById('reset-all-settings').addEventListener('click', () => {
      localStorage.removeItem('env_mode');
      localStorage.removeItem('log_level');
      updateUI();
    });
    
    // Initialize UI on page load
    updateUI();
  </script>
</body>
</html>
