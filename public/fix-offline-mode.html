<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Supabase Offline Mode</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #0066cc;
        }
        h2 {
            color: #333;
            margin-top: 30px;
        }
        .alert {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 12px 24px;
            margin: 20px 0;
        }
        .success {
            background-color: #e8f5e9;
            border-left: 4px solid #4caf50;
            padding: 12px 24px;
            margin: 20px 0;
        }
        .error {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
            padding: 12px 24px;
            margin: 20px 0;
        }
        code {
            background-color: #f5f5f5;
            padding: 2px 5px;
            border-radius: 4px;
            font-family: monospace;
        }
        button {
            background-color: #0066cc;
            color: white;
            border: none;
            padding: 10px 16px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px 0;
            border-radius: 4px;
        }
        button.danger {
            background-color: #f44336;
        }
        button:hover {
            opacity: 0.9;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
            opacity: 0.7;
        }
        .steps li {
            margin-bottom: 15px;
        }
        #statusDisplay {
            font-weight: bold;
            margin: 15px 0;
            font-size: 18px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .key-value-item {
            display: flex;
            margin-bottom: 6px;
        }
        .key {
            font-weight: bold;
            min-width: 200px;
        }
    </style>
</head>
<body>
    <h1>Fix Supabase Offline Mode Issues</h1>
    
    <div class="alert">
        <p>⚠️ <strong>Note:</strong> This tool diagnoses and fixes "Operating in offline mode" errors that cause authentication and RPC function failures.</p>
    </div>
    
    <div id="statusDisplay">
        Checking offline mode status...
    </div>
    
    <div class="error" id="errorInfo" style="display: none;">
        <p>❌ <strong>Error Detected:</strong> <span id="errorMessage"></span></p>
    </div>
    
    <div class="success" id="successInfo" style="display: none;">
        <p>✅ <strong>Success:</strong> <span id="successMessage"></span></p>
    </div>
    
    <h2>Fix Offline Mode Issues</h2>
    
    <button onclick="disableOfflineMode()" id="disableButton">Disable Offline Mode</button>
    <button onclick="enableOfflineMode()" id="enableButton">Enable Offline Mode</button>
    <button onclick="clearAllStorage()" class="danger">Clear ALL Storage & Reload</button>
    
    <h2>Current System State</h2>
    
    <div class="key-value-item">
        <div class="key">Primary Offline Mode Flag:</div>
        <div class="value" id="primaryOfflineModeValue">Checking...</div>
    </div>
    
    <div class="key-value-item">
        <div class="key">AppConfig Offline Mode:</div>
        <div class="value" id="appConfigOfflineModeValue">Checking...</div>
    </div>
    
    <div class="key-value-item">
        <div class="key">ConnectionConfig Offline Mode:</div>
        <div class="value" id="connectionConfigOfflineModeValue">Checking...</div>
    </div>
    
    <div class="key-value-item">
        <div class="key">Legacy Offline Mode:</div>
        <div class="value" id="legacyOfflineModeValue">Checking...</div>
    </div>
    
    <div class="key-value-item">
        <div class="key">Supabase URL:</div>
        <div class="value" id="supabaseUrlValue">Checking...</div>
    </div>
    
    <div class="key-value-item">
        <div class="key">API Key Set:</div>
        <div class="value" id="apiKeyValue">Checking...</div>
    </div>
    
    <div class="key-value-item">
        <div class="key">Session:</div>
        <div class="value" id="sessionValue">Checking...</div>
    </div>
    
    <h2>RPC Function Test</h2>
    <button onclick="testRPCFunctions()">Test RPC Functions</button>
    <div id="rpcResult" style="margin-top: 10px;"></div>
    
    <script>
        /**
         * Function to check and display the current offline mode status
         * This examines ALL possible offline mode settings
         */
        function checkOfflineMode() {
            try {
                // Check the primary offline mode flag used in gateway.ts
                const primaryOfflineMode = localStorage.getItem('supabase_offline_mode') === 'true';
                const primaryOfflineModeValue = document.getElementById('primaryOfflineModeValue');
                
                if (primaryOfflineMode) {
                    primaryOfflineModeValue.textContent = 'Enabled';
                    primaryOfflineModeValue.style.color = '#f44336';
                } else {
                    primaryOfflineModeValue.textContent = 'Disabled';
                    primaryOfflineModeValue.style.color = '#4caf50';
                }
                
                // Check other possible storage keys
                const appConfig = JSON.parse(localStorage.getItem('app_config') || '{}');
                const appConfigOfflineMode = !!appConfig.offlineMode;
                
                const appConfigOfflineModeValue = document.getElementById('appConfigOfflineModeValue');
                if (appConfigOfflineMode) {
                    appConfigOfflineModeValue.textContent = 'Enabled';
                    appConfigOfflineModeValue.style.color = '#f44336';
                } else {
                    appConfigOfflineModeValue.textContent = 'Disabled';
                    appConfigOfflineModeValue.style.color = '#4caf50';
                }
                
                const connectionConfig = JSON.parse(localStorage.getItem('connection_config') || '{}');
                const connectionConfigOfflineMode = !!connectionConfig.offlineMode;
                
                const connectionConfigOfflineModeValue = document.getElementById('connectionConfigOfflineModeValue');
                if (connectionConfigOfflineMode) {
                    connectionConfigOfflineModeValue.textContent = 'Enabled';
                    connectionConfigOfflineModeValue.style.color = '#f44336';
                } else {
                    connectionConfigOfflineModeValue.textContent = 'Disabled';
                    connectionConfigOfflineModeValue.style.color = '#4caf50';
                }
                
                const legacyOfflineMode = localStorage.getItem('offlineMode') === 'true';
                
                const legacyOfflineModeValue = document.getElementById('legacyOfflineModeValue');
                if (legacyOfflineMode) {
                    legacyOfflineModeValue.textContent = 'Enabled';
                    legacyOfflineModeValue.style.color = '#f44336';
                } else {
                    legacyOfflineModeValue.textContent = 'Disabled';
                    legacyOfflineModeValue.style.color = '#4caf50';
                }
                
                // Check if ANY offline mode is enabled
                let anyOfflineModeEnabled = primaryOfflineMode || 
                    appConfigOfflineMode || 
                    connectionConfigOfflineMode || 
                    legacyOfflineMode;
                
                // Update the global status display
                const statusDisplay = document.getElementById('statusDisplay');
                
                if (anyOfflineModeEnabled) {
                    statusDisplay.innerHTML = '🔴 App is currently in OFFLINE MODE';
                    statusDisplay.style.color = '#f44336';
                    
                    // Show error info
                    document.getElementById('errorInfo').style.display = 'block';
                    document.getElementById('errorMessage').textContent = 
                        'Offline Mode is causing authentication and RPC function errors. This prevents the app from connecting to Supabase.';
                    
                    if (primaryOfflineMode) {
                        document.getElementById('errorMessage').textContent += 'The primary offline mode flag (supabase_offline_mode) is enabled.';
                    }
                } else {
                    statusDisplay.innerHTML = '🟢 App is currently in ONLINE MODE';
                    statusDisplay.style.color = '#4caf50';
                    
                    // Hide error info
                    document.getElementById('errorInfo').style.display = 'none';
                }
                
                // Update other config values
                document.getElementById('supabaseUrlValue').textContent = 
                    localStorage.getItem('supabase_url') || 'Not set';
                document.getElementById('apiKeyValue').textContent = 
                    localStorage.getItem('supabase_anon_key') ? 'Yes (Set)' : 'No (Missing)';
                
                // Check for session
                const session = localStorage.getItem('supabase.auth.token');
                document.getElementById('sessionValue').textContent = 
                    session ? 'Yes (Found)' : 'No (Missing)';
                
                // Update button states
                document.getElementById('disableButton').disabled = !anyOfflineModeEnabled;
                document.getElementById('enableButton').disabled = anyOfflineModeEnabled;
                
                return anyOfflineModeEnabled;
            } catch (error) {
                console.error('Error checking offline mode:', error);
                document.getElementById('statusDisplay').innerHTML = 
                    '⚠️ Could not determine offline mode status';
                document.getElementById('statusDisplay').style.color = '#ffc107';
                return false;
            }
        }
        
        /**
         * Function to disable all offline mode settings
         */
        function disableOfflineMode() {
            try {
                // Remove the primary offline mode flag (gateway.ts)
                localStorage.removeItem('supabase_offline_mode');
                
                // Update other possible storage locations
                const appConfig = JSON.parse(localStorage.getItem('app_config') || '{}');
                appConfig.offlineMode = false;
                localStorage.setItem('app_config', JSON.stringify(appConfig));
                
                const connectionConfig = JSON.parse(localStorage.getItem('connection_config') || '{}');
                connectionConfig.offlineMode = false;
                localStorage.setItem('connection_config', JSON.stringify(connectionConfig));
                
                localStorage.setItem('offlineMode', 'false');
                
                // Show success message
                document.getElementById('successInfo').style.display = 'block';
                document.getElementById('successMessage').textContent = 
                    'Offline Mode has been disabled. The app will now attempt to connect to Supabase.';
                
                // Hide error message if it was showing
                document.getElementById('errorInfo').style.display = 'none';
                
                // Update status display
                checkOfflineMode();
                
                // Offer to reload
                setTimeout(() => {
                    if (confirm('Changes applied. Would you like to reload the page to apply changes?')) {
                        window.location.reload();
                    }
                }, 500);
            } catch (error) {
                console.error('Error disabling offline mode:', error);
                alert('Failed to disable offline mode: ' + error.message);
            }
        }
        
        /**
         * Function to enable offline mode (primarily for testing)
         */
        function enableOfflineMode() {
            if (!confirm('Are you sure you want to enable offline mode? This will cause authentication and RPC errors.')) {
                return;
            }
            
            try {
                // Set the primary offline mode flag (gateway.ts)
                localStorage.setItem('supabase_offline_mode', 'true');
                
                // Update other possible storage locations
                const appConfig = JSON.parse(localStorage.getItem('app_config') || '{}');
                appConfig.offlineMode = true;
                localStorage.setItem('app_config', JSON.stringify(appConfig));
                
                const connectionConfig = JSON.parse(localStorage.getItem('connection_config') || '{}');
                connectionConfig.offlineMode = true;
                localStorage.setItem('connection_config', JSON.stringify(connectionConfig));
                
                localStorage.setItem('offlineMode', 'true');
                
                // Show warning message
                document.getElementById('errorInfo').style.display = 'block';
                document.getElementById('errorMessage').textContent = 
                    'Offline Mode has been enabled. The app will now simulate being offline and RPC functions may fail.';
                
                // Hide success message if it was showing
                document.getElementById('successInfo').style.display = 'none';
                
                // Update status display
                checkOfflineMode();
                
                // Offer to reload
                setTimeout(() => {
                    if (confirm('Changes applied. Would you like to reload the page to apply changes?')) {
                        window.location.reload();
                    }
                }, 500);
            } catch (error) {
                console.error('Error enabling offline mode:', error);
                alert('Failed to enable offline mode: ' + error.message);
            }
        }
        
        /**
         * Function to clear all localStorage and reload
         */
        function clearAllStorage() {
            if (confirm('This will clear ALL localStorage data and reload the page. This is a drastic step but can fix persistent issues. Continue?')) {
                localStorage.clear();
                
                // Clear any service workers
                if ('serviceWorker' in navigator) {
                    navigator.serviceWorker.getRegistrations().then(registrations => {
                        for (let registration of registrations) {
                            registration.unregister();
                        }
                    });
                }
                
                // Clear session cookies
                document.cookie.split(";").forEach(function(c) {
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
                });
                
                // Reload with cache bypass
                window.location.reload(true);
            }
        }
        
        /**
         * Function to test if RPC functions are accessible
         */
        async function testRPCFunctions() {
            const resultDiv = document.getElementById('rpcResult');
            resultDiv.innerHTML = 'Testing RPC functions...';
            
            try {
                const url = new URL(window.location.href);
                const baseUrl = url.origin; // Get the base URL of the current page
                const supabaseUrl = localStorage.getItem('supabase_url') || 'https://wgsnpiskyczxhojlrwtr.supabase.co';
                const supabaseKey = localStorage.getItem('supabase_anon_key') || localStorage.getItem('supabase_key') || '';
                
                // Try different endpoints
                const endpoints = [
                    'get_leaderboard',
                    'get_role_permissions_matrix',
                    'get_role_permissions_matrix_alt',
                    'get_dynamic_user_capabilities'
                ];
                
                let results = [];
                
                // Test if offline mode is enabled
                const offlineMode = checkOfflineMode();
                if (offlineMode) {
                    resultDiv.innerHTML = '<div class="error">❌ Cannot test RPC functions while in offline mode. Please disable offline mode first.</div>';
                    return;
                }
                
                // Special handling for leaderboard with multiple parameter formats
                const testLeaderboard = async () => {
                    try {
                        // First try to call the leaderboard function without parameters
                        const noParamsResponse = await fetch(`${supabaseUrl}/rest/v1/rpc/get_leaderboard`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'apikey': supabaseKey,
                                'Authorization': `Bearer ${supabaseKey}`
                            },
                            body: JSON.stringify({})
                        });
                        
                        if (noParamsResponse.ok) {
                            const data = await noParamsResponse.json();
                            results.push(`✅ get_leaderboard: OK (${noParamsResponse.status}) - Result: ${Array.isArray(data) ? data.length : 'object'} items`);
                            return;
                        }
                        
                        // If that fails, try with parameters object
                        const withParamsResponse = await fetch(`${supabaseUrl}/rest/v1/rpc/get_leaderboard`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'apikey': supabaseKey,
                                'Authorization': `Bearer ${supabaseKey}`
                            },
                            body: JSON.stringify({ p_limit: 5, p_offset: 0 })
                        });
                        
                        if (withParamsResponse.ok) {
                            const data = await withParamsResponse.json();
                            results.push(`✅ get_leaderboard: OK (${withParamsResponse.status}) - Result: ${Array.isArray(data) ? data.length : 'object'} items`);
                            return;
                        }
                        
                        // If both fail, report the error
                        results.push(`❌ get_leaderboard: Failed - ${noParamsResponse.status} - ${noParamsResponse.statusText}`);
                    } catch (error) {
                        results.push(`❌ get_leaderboard: Error (${error.message})`);
                    }
                };
                
                // Test the leaderboard function first
                await testLeaderboard();
                
                // Test the other functions
                const endpointParams = {
                    'get_role_permissions_matrix': {},
                    'get_role_permissions_matrix_alt': {},
                    'get_dynamic_user_capabilities': { p_user_id: '00000000-0000-0000-0000-000000000000' }
                };
                
                for (const endpoint of endpoints.filter(e => e !== 'get_leaderboard')) {
                    try {
                        console.log(`Testing ${endpoint} with params:`, endpointParams[endpoint]);
                        
                        const response = await fetch(`${supabaseUrl}/rest/v1/rpc/${endpoint}`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'apikey': supabaseKey,
                                'Authorization': `Bearer ${supabaseKey}`
                            },
                            body: JSON.stringify(endpointParams[endpoint])
                        });
                        
                        // Check if the response was successful
                        if (response.ok) {
                            const data = await response.json();
                            const resultCount = Array.isArray(data) ? data.length : 'object';
                            results.push(`✅ ${endpoint}: OK (${response.status}) - Result: ${resultCount} items`);
                        } else {
                            results.push(`❌ ${endpoint}: Failed (${response.status} - ${response.statusText})`);
                        }
                    } catch (error) {
                        results.push(`❌ ${endpoint}: Error (${error.message})`);
                    }
                }
                
                // Display results
                resultDiv.innerHTML = `<ul>${results.map(r => `<li>${r}</li>`).join('')}</ul>`;
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">Error testing RPC functions: ${error.message}</div>`;
                console.error('Error in testRPCFunctions:', error);
            }
        }
        
        // Check offline mode when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            checkOfflineMode();
            
            // Set the Supabase anonymous key if it's missing
            if (!localStorage.getItem('supabase_anon_key')) {
                localStorage.setItem('supabase_anon_key', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.qjE4Z2CwDpXW4C5VZ5tHrhLQV2uK6ut8Mnrgr0snhwc');
            }
            
            // Set the Supabase URL if it's missing
            if (!localStorage.getItem('supabase_url')) {
                localStorage.setItem('supabase_url', 'https://wgsnpiskyczxhojlrwtr.supabase.co');
            }
        });
    </script>

    <div class="success">
        <p>✅ <strong>Next Steps:</strong> After fixing offline mode issues, return to the main application to see if the errors are resolved.</p>
    </div>
    
    <p><a href="./">Return to Main Application</a></p>
</body>
</html>
