<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Fix Environment Variables</title>
  <style>
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 { color: #3ECF8E; }
    pre {
      background-color: #f4f4f4;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
    }
    .header { display: flex; align-items: center; }
    .header svg { margin-right: 10px; }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input[type="text"] {
      width: 100%;
      padding: 8px;
      border: 1px solid #ccc;
      border-radius: 4px;
    }
    button {
      background-color: #3ECF8E;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      margin-top: 10px;
    }
    button:hover { background-color: #38bb81; }
    .success {
      background-color: #d1fae5;
      border: 1px solid #34d399;
      color: #065f46;
      padding: 10px;
      border-radius: 4px;
      margin-top: 15px;
      display: none;
    }
  </style>
</head>
<body>
  <div class="header">
    <svg width="32" height="32" viewBox="0 0 109 113" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0627L99.1935 40.0627C107.384 40.0627 111.952 49.5228 106.859 55.9374L63.7076 110.284Z" fill="url(#paint0_linear)"/>
      <path d="M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0627L99.1935 40.0627C107.384 40.0627 111.952 49.5228 106.859 55.9374L63.7076 110.284Z" fill="url(#paint1_linear)" fill-opacity="0.2"/>
      <path d="M45.317 2.07103C48.1765 -1.53037 53.9745 0.442937 54.0434 5.041L54.4849 72.2922H9.83113C1.64038 72.2922 -2.92775 62.8321 2.1655 56.4175L45.317 2.07103Z" fill="#3ECF8E"/>
      <defs>
      <linearGradient id="paint0_linear" x1="53.9738" y1="54.974" x2="94.1635" y2="71.8295" gradientUnits="userSpaceOnUse">
      <stop stop-color="#249361"/>
      <stop offset="1" stop-color="#3ECF8E"/>
      </linearGradient>
      <linearGradient id="paint1_linear" x1="36.1558" y1="30.578" x2="54.4844" y2="106.178" gradientUnits="userSpaceOnUse">
      <stop stop-color="white"/>
      <stop offset="1" stop-color="white" stop-opacity="0"/>
      </linearGradient>
      </defs>
    </svg>
    <h1>Fix Environment Variables</h1>
  </div>
  
  <p>Use this tool to update your Supabase environment variables in the browser.</p>
  
  <form id="envForm">
    <div class="form-group">
      <label for="supabaseUrl">Supabase URL</label>
      <input type="text" id="supabaseUrl" placeholder="https://your-project-id.supabase.co">
    </div>
    
    <div class="form-group">
      <label for="supabaseAnonKey">Supabase Anon Key</label>
      <input type="text" id="supabaseAnonKey" placeholder="your-anon-key">
    </div>
    
    <div class="form-group">
      <label for="edgeFunctionSecret">Edge Function Secret</label>
      <input type="text" id="edgeFunctionSecret" placeholder="your-secret-key">
    </div>
    
    <button type="submit">Save Environment Variables</button>
  </form>
  
  <div id="successMessage" class="success">
    Environment variables saved successfully! Please reload the application.
  </div>
  
  <p>After saving, you can <a href="check-supabase-connection.html">test your Supabase connection</a>.</p>
  
  <script src="js/env-variable-bridge.js"></script>
</body>
</html>
