<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix RPC 404 Errors</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #0066cc;
        }
        h2 {
            color: #333;
            margin-top: 30px;
        }
        .alert {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 12px 24px;
            margin: 20px 0;
        }
        .success {
            background-color: #e8f5e9;
            border-left: 4px solid #4caf50;
            padding: 12px 24px;
            margin: 20px 0;
        }
        code {
            background-color: #f5f5f5;
            padding: 2px 5px;
            border-radius: 4px;
            font-family: monospace;
        }
        button {
            background-color: #0066cc;
            color: white;
            border: none;
            padding: 10px 16px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px 0;
            border-radius: 4px;
        }
        button:hover {
            background-color: #004c99;
        }
        .steps li {
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <h1>Fix RPC 404 Errors</h1>
    
    <div class="alert">
        <p>⚠️ <strong>Note:</strong> The 404 errors for RPC functions may be caused by cached requests or service worker issues.</p>
    </div>
    
    <h2>Solution Steps</h2>
    
    <ol class="steps">
        <li>
            <strong>Clear Browser Cache</strong>
            <p>Click the button below to clear your browser cache for this domain:</p>
            <button onclick="clearCacheAndReload()">Clear Cache and Reload</button>
        </li>
        
        <li>
            <strong>Perform a Hard Reload</strong>
            <p>If the button above doesn't work, try a hard reload manually:</p>
            <ul>
                <li><strong>Windows/Linux:</strong> Press <code>Ctrl + Shift + R</code> or <code>Ctrl + F5</code></li>
                <li><strong>Mac:</strong> Press <code>Cmd + Shift + R</code> or <code>Cmd + Option + R</code></li>
            </ul>
        </li>
        
        <li>
            <strong>Disable Client-Side Caching</strong>
            <p>You can also try disabling the application's internal caching:</p>
            <button onclick="disableTelemetryAndCache()">Disable Telemetry & Reset Cache</button>
        </li>
        
        <li>
            <strong>Verify RPC Functions</strong>
            <p>After clearing the cache and reloading, check if the RPC functions are accessible:</p>
            <button onclick="testRPCFunctions()">Test RPC Functions</button>
            <div id="rpcResult" style="margin-top: 10px;"></div>
        </li>
    </ol>
    
    <div class="alert">
        <p>🔍 <strong>Why This Happens:</strong> When database functions are added or updated, browser caches or service workers might still use old failed responses.</p>
    </div>
    
    <script>
        // Function to clear cache and reload
        async function clearCacheAndReload() {
            try {
                // Clear localStorage cache
                localStorage.clear();
                
                // Attempt to clear fetch cache if supported
                if ('caches' in window) {
                    const cacheNames = await caches.keys();
                    await Promise.all(
                        cacheNames.map(cacheName => {
                            return caches.delete(cacheName);
                        })
                    );
                    console.log('Cache cleared successfully');
                }
                
                // Check if there are service workers and unregister them
                if ('serviceWorker' in navigator) {
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    await Promise.all(
                        registrations.map(registration => {
                            return registration.unregister();
                        })
                    );
                    console.log('Service workers unregistered');
                }
                
                // Reload with cache bypass
                window.location.reload(true);
            } catch (error) {
                console.error('Error clearing cache:', error);
                alert('Failed to clear cache: ' + error.message);
            }
        }
        
        // Function to disable telemetry and reset application cache
        function disableTelemetryAndCache() {
            try {
                // Disable telemetry if the function exists
                if (typeof window.setTelemetryEnabled === 'function') {
                    window.setTelemetryEnabled(false);
                    console.log('Telemetry disabled');
                } else {
                    // Set directly in localStorage if function doesn't exist
                    const telemetryConfig = JSON.parse(localStorage.getItem('telemetry_config') || '{}');
                    telemetryConfig.enabled = false;
                    localStorage.setItem('telemetry_config', JSON.stringify(telemetryConfig));
                    console.log('Telemetry disabled via localStorage');
                }
                
                // Reset Supabase client if the function exists
                if (typeof window.resetSupabaseClient === 'function') {
                    window.resetSupabaseClient();
                    console.log('Supabase client reset');
                }
                
                // Reset any supabase-related items in localStorage
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && (
                        key.includes('supabase') || 
                        key.includes('sb-') ||
                        key.includes('stat-linker')
                    )) {
                        localStorage.removeItem(key);
                        console.log(`Removed localStorage item: ${key}`);
                    }
                }
                
                alert('Telemetry disabled and cache reset. Reload the application for changes to take effect.');
            } catch (error) {
                console.error('Error disabling telemetry:', error);
                alert('Failed to disable telemetry: ' + error.message);
            }
        }
        
        // Function to test if RPC functions are accessible
        async function testRPCFunctions() {
            const resultDiv = document.getElementById('rpcResult');
            resultDiv.innerHTML = 'Testing RPC functions...';
            
            try {
                const url = new URL(window.location.href);
                const baseUrl = url.origin; // Get the base URL of the current page
                const supabaseUrl = localStorage.getItem('supabase_url') || 'https://wgsnpiskyczxhojlrwtr.supabase.co';
                const supabaseKey = localStorage.getItem('supabase_anon_key') || localStorage.getItem('supabase_key') || '';
                
                // Try different endpoints
                const endpoints = [
                    'get_leaderboard',
                    'get_role_permissions_matrix',
                    'get_role_permissions_matrix_alt',
                    'get_dynamic_user_capabilities'
                ];
                
                let results = [];
                
                // Prepare test data with proper parameters for each function
                const endpointParams = {
                    'get_leaderboard': { p_limit: 5, p_offset: 0 },
                    'get_role_permissions_matrix': {},
                    'get_role_permissions_matrix_alt': {},
                    'get_dynamic_user_capabilities': { p_user_id: '00000000-0000-0000-0000-000000000000' } // Using a dummy UUID
                };
                
                // Special handling for leaderboard
                const specialTest = async () => {
                    try {
                        // First try to call the leaderboard function without parameters
                        const noParamsResponse = await fetch(`${supabaseUrl}/rest/v1/rpc/get_leaderboard`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'apikey': supabaseKey,
                                'Authorization': `Bearer ${supabaseKey}`
                            },
                            body: JSON.stringify({})
                        });
                        
                        if (noParamsResponse.ok) {
                            const data = await noParamsResponse.json();
                            results.push(`✅ get_leaderboard: OK (${noParamsResponse.status}) - Result: ${Array.isArray(data) ? data.length : 'object'} items`);
                            return;
                        }
                        
                        // If that fails, try using strange parameter format
                        const altResponse = await fetch(`${supabaseUrl}/rest/v1/rpc/get_leaderboard`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'apikey': supabaseKey,
                                'Authorization': `Bearer ${supabaseKey}`
                            },
                            body: JSON.stringify([5, 0])  // Try array format
                        });
                        
                        if (altResponse.ok) {
                            const data = await altResponse.json();
                            results.push(`✅ get_leaderboard: OK (${altResponse.status}) - Result: ${Array.isArray(data) ? data.length : 'object'} items`);
                            return;
                        }
                        
                        // If both fail, report the error
                        results.push(`❌ get_leaderboard: Failed - Tried multiple parameter formats`);
                    } catch (error) {
                        results.push(`❌ get_leaderboard: Error (${error.message})`);
                    }
                };
                
                // Test the special case first
                await specialTest();
                
                // Test the other functions
                for (const endpoint of endpoints.filter(e => e !== 'get_leaderboard')) {
                    try {
                        console.log(`Testing ${endpoint} with params:`, endpointParams[endpoint]);
                        
                        const response = await fetch(`${supabaseUrl}/rest/v1/rpc/${endpoint}`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'apikey': supabaseKey,
                                'Authorization': `Bearer ${supabaseKey}`
                            },
                            body: JSON.stringify(endpointParams[endpoint])
                        });
                        
                        // Check if the response was successful
                        if (response.ok) {
                            const data = await response.json();
                            const resultCount = Array.isArray(data) ? data.length : 'object';
                            results.push(`✅ ${endpoint}: OK (${response.status}) - Result: ${resultCount} items`);
                        } else {
                            results.push(`❌ ${endpoint}: Failed (${response.status} - ${response.statusText})`);
                        }
                    } catch (error) {
                        results.push(`❌ ${endpoint}: Error (${error.message})`);
                    }
                }
                
                // Display results
                resultDiv.innerHTML = `<ul>${results.map(r => `<li>${r}</li>`).join('')}</ul>`;
                
            } catch (error) {
                resultDiv.innerHTML = `Error testing RPC functions: ${error.message}`;
                console.error('Error in testRPCFunctions:', error);
            }
        }
        
        // Initialize on document load
        document.addEventListener('DOMContentLoaded', () => {
            // Store Supabase URL and key in localStorage if it's not already there
            if (!localStorage.getItem('supabase_url')) {
                localStorage.setItem('supabase_url', 'https://wgsnpiskyczxhojlrwtr.supabase.co');
            }
            
            // Set the Supabase anonymous key
            if (!localStorage.getItem('supabase_anon_key')) {
                // This is the anonymous key from .env
                localStorage.setItem('supabase_anon_key', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.qjE4Z2CwDpXW4C5VZ5tHrhLQV2uK6ut8Mnrgr0snhwc');
                console.log("📝 Set Supabase anonymous key in localStorage");
            }
            
            // Add the key to a meta tag for future reference
            const metaTag = document.createElement('meta');
            metaTag.name = 'supabase-key';
            metaTag.content = localStorage.getItem('supabase_anon_key');
            document.head.appendChild(metaTag);
        });
    </script>

    <div class="success">
        <p>✅ <strong>Next Steps:</strong> After clearing cache and confirming RPC functions work, return to the main application to see if the errors are resolved.</p>
    </div>
    
    <p><a href="./">Return to Main Application</a></p>
</body>
</html>
