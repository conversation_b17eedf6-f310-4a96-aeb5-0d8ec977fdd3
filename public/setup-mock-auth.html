<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Setup Mock Authentication</title>
  <style>
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      text-align: center;
    }
    h1 { color: #3ECF8E; }
    .card {
      background-color: #f8f9fa;
      border-radius: 8px;
      padding: 30px;
      margin: 30px auto;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      max-width: 500px;
    }
    button {
      background-color: #3ECF8E;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      font-size: 16px;
      cursor: pointer;
      margin: 10px;
    }
    button:hover {
      background-color: #2ebd7e;
    }
    .instructions {
      text-align: left;
      margin: 20px 0;
      line-height: 1.6;
    }
    .status {
      padding: 15px;
      margin: 15px 0;
      border-radius: 4px;
      display: none;
    }
    .success {
      background-color: #d1fae5;
      color: #065f46;
      border: 1px solid #34d399;
    }
    .links {
      margin-top: 20px;
    }
    .links a {
      display: inline-block;
      margin: 5px 10px;
      color: #3ECF8E;
      text-decoration: none;
    }
    .links a:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <h1>Setup Mock Authentication</h1>
  
  <div class="card">
    <h2>Development Authentication</h2>
    <p>This page helps you set up mock authentication to bypass the login screen for development purposes.</p>
    
    <div class="instructions">
      <p><strong>What this does:</strong></p>
      <ol>
        <li>Creates a mock user session in your browser's localStorage</li>
        <li>Sets up the user with admin privileges</li>
        <li>Allows you to access the application without login credentials</li>
      </ol>
      <p><strong>Note:</strong> This is for development only and should not be used in production.</p>
    </div>
    
    <button id="setupAuthBtn">Setup Mock Authentication</button>
    <button id="clearAuthBtn">Clear Auth Session</button>
    <button id="goToAppBtn">Go to Application</button>
    
    <div id="statusMessage" class="status"></div>
    
    <div class="links">
      <a href="/indicators/sdg">SDG Dashboard</a>
      <a href="/products">Products</a>
      <a href="/settings">Settings</a>
    </div>
  </div>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const setupAuthBtn = document.getElementById('setupAuthBtn');
      const clearAuthBtn = document.getElementById('clearAuthBtn');
      const goToAppBtn = document.getElementById('goToAppBtn');
      const statusMessage = document.getElementById('statusMessage');
      
      // Check if auth already exists
      function checkAuth() {
        const hasSession = localStorage.getItem('stat-linker-mock-session');
        if (hasSession) {
          showStatus('Mock authentication already set up. You can go to the application.', 'success');
          return true;
        }
        return false;
      }
      
      // Show status message
      function showStatus(message, type) {
        statusMessage.textContent = message;
        statusMessage.className = `status ${type}`;
        statusMessage.style.display = 'block';
      }
      
      // Setup auth button click
      setupAuthBtn.addEventListener('click', function() {
        // Load and execute the auth script
        const script = document.createElement('script');
        script.src = '/js/setup-mock-auth.js';
        document.body.appendChild(script);
        
        // Show success message
        showStatus('Mock authentication set up successfully!', 'success');
        
        // Remove script after execution
        script.onload = function() {
          setTimeout(() => {
            document.body.removeChild(script);
          }, 1000);
        };
      });
      
      // Clear auth button click
      clearAuthBtn.addEventListener('click', function() {
        localStorage.removeItem('stat-linker-mock-session');
        showStatus('Authentication session cleared.', 'success');
      });
      
      // Go to app button click
      goToAppBtn.addEventListener('click', function() {
        if (!checkAuth()) {
          if (confirm('No authentication session found. Set up mock authentication first?')) {
            // Load and execute the auth script
            const script = document.createElement('script');
            script.src = '/js/setup-mock-auth.js';
            document.body.appendChild(script);
            
            // Wait a moment before redirecting
            setTimeout(() => {
              window.location.href = '/';
            }, 500);
            return;
          }
        }
        
        // Redirect to main app
        window.location.href = '/';
      });
      
      // Initial auth check
      checkAuth();
    });
  </script>
</body>
</html>
