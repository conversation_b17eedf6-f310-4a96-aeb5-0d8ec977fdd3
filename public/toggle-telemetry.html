<!DOCTYPE html>
<html>
<head>
  <title>Telemetry Control</title>
  <style>
    body { font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto; }
    .card { border: 1px solid #ddd; border-radius: 8px; padding: 20px; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    button { padding: 10px 20px; margin: 5px; cursor: pointer; border-radius: 4px; }
    .green { background-color: #4CAF50; color: white; border: none; }
    .red { background-color: #f44336; color: white; border: none; }
    .blue { background-color: #2196F3; color: white; border: none; }
    .status { margin: 20px 0; font-weight: bold; }
    .header { display: flex; align-items: center; justify-content: space-between; }
    .version { color: #666; font-size: 0.8em; }
    .control-row { margin: 10px 0; display: flex; align-items: center; }
    .control-row label { flex: 1; }
    h3 { margin-top: 25px; margin-bottom: 10px; color: #333; }
    .performance-tip { background-color: #fff3cd; padding: 10px; border-left: 4px solid #ffc107; margin: 15px 0; }
  </style>
</head>
<body>
  <div class="header">
    <h1>Telemetry Control Panel</h1>
    <span class="version">v1.0.0</span>
  </div>
  
  <div class="performance-tip">
    <strong>Performance Tip:</strong> Disabling telemetry will significantly improve application performance. 
    Only enable it temporarily when debugging specific issues.
  </div>
  
  <div class="card">
    <h2>Telemetry Status</h2>
    <p>Current status: <span id="telemetryStatus">Checking...</span></p>
    
    <button onclick="enableTelemetry()" class="green">Enable Telemetry</button>
    <button onclick="disableTelemetry()" class="red">Disable Telemetry</button>
    
    <div class="status" id="message"></div>
  </div>
  
  <div class="card">
    <h2>Advanced Configuration</h2>
    
    <div class="control-row">
      <label>
        <input type="checkbox" id="consoleLogging"> 
        Enable Console Logging
      </label>
    </div>
    
    <div class="control-row">
      <label>
        <input type="checkbox" id="persistToStorage"> 
        Persist to Storage (impacts performance)
      </label>
    </div>
    
    <div class="control-row">
      <label>
        Sample Rate:
        <select id="sampleRate">
          <option value="0.01">1%</option>
          <option value="0.05">5%</option>
          <option value="0.1">10%</option>
          <option value="0.25">25%</option>
          <option value="0.5">50%</option>
          <option value="1.0">100%</option>
        </select>
      </label>
    </div>
    
    <div class="control-row">
      <label>
        <input type="checkbox" id="logSuccessful"> 
        Log Successful Operations (high performance impact)
      </label>
    </div>
    
    <div class="control-row">
      <label>
        Max Stored Errors:
        <select id="maxStoredErrors">
          <option value="10">10</option>
          <option value="25">25</option>
          <option value="50">50</option>
          <option value="100">100</option>
          <option value="200">200</option>
        </select>
      </label>
    </div>
    
    <button onclick="applyAdvancedSettings()" class="blue">Apply Settings</button>
  </div>

  <div class="card">
    <h2>Telemetry Statistics</h2>
    <p>These statistics are only available if telemetry is enabled and has collected data</p>
    
    <h3>Errors by Table</h3>
    <div id="errorsByTable">No data available</div>
    
    <h3>Errors by Severity</h3>
    <div id="errorsBySeverity">No data available</div>
    
    <h3>Recent Errors</h3>
    <div id="recentErrors">No data available</div>
    
    <button onclick="refreshStatistics()" class="blue">Refresh Statistics</button>
    <button onclick="clearTelemetry()" class="red">Clear All Telemetry Data</button>
  </div>
  
  <div class="card">
    <h2>Application Actions</h2>
    <p>Apply changes across the application</p>
    <button onclick="resetSupabaseClient()" class="blue">Reset Supabase Client</button>
    <button onclick="refreshApplication()" class="green">Refresh Application</button>
  </div>
  
  <script>
    // Check current status
    function checkStatus() {
      const telemetryConfig = JSON.parse(localStorage.getItem('telemetry_config') || '{}');
      const enabled = telemetryConfig.enabled !== false; // Default is enabled
      
      document.getElementById('telemetryStatus').textContent = enabled ? 'ENABLED' : 'DISABLED';
      document.getElementById('telemetryStatus').style.color = enabled ? 'green' : 'red';
      
      // Set advanced controls to match current config
      document.getElementById('consoleLogging').checked = telemetryConfig.consoleLogging || false;
      document.getElementById('persistToStorage').checked = telemetryConfig.persistToStorage || false;
      document.getElementById('logSuccessful').checked = telemetryConfig.logSuccessfulOperations || false;
      
      // Set sample rate dropdown
      const sampleRate = telemetryConfig.sampleRate || 0.05;
      const sampleRateSelect = document.getElementById('sampleRate');
      for(let i = 0; i < sampleRateSelect.options.length; i++) {
        if (parseFloat(sampleRateSelect.options[i].value) === sampleRate) {
          sampleRateSelect.selectedIndex = i;
          break;
        }
      }
      
      // Set max stored errors
      const maxStoredErrors = telemetryConfig.maxStoredErrors || 50;
      const maxStoredSelect = document.getElementById('maxStoredErrors');
      for(let i = 0; i < maxStoredSelect.options.length; i++) {
        if (parseInt(maxStoredSelect.options[i].value) === maxStoredErrors) {
          maxStoredSelect.selectedIndex = i;
          break;
        }
      }
    }
    
    function enableTelemetry() {
      const config = JSON.parse(localStorage.getItem('telemetry_config') || '{}');
      config.enabled = true;
      localStorage.setItem('telemetry_config', JSON.stringify(config));
      document.getElementById('message').textContent = 'Telemetry enabled. Refresh the application page to apply changes.';
      checkStatus();
    }
    
    function disableTelemetry() {
      const config = JSON.parse(localStorage.getItem('telemetry_config') || '{}');
      config.enabled = false;
      localStorage.setItem('telemetry_config', JSON.stringify(config));
      document.getElementById('message').textContent = 'Telemetry disabled. Refresh the application page to apply changes.';
      checkStatus();
    }
    
    function applyAdvancedSettings() {
      const config = JSON.parse(localStorage.getItem('telemetry_config') || '{}');
      
      config.consoleLogging = document.getElementById('consoleLogging').checked;
      config.persistToStorage = document.getElementById('persistToStorage').checked;
      config.logSuccessfulOperations = document.getElementById('logSuccessful').checked;
      config.sampleRate = parseFloat(document.getElementById('sampleRate').value);
      config.maxStoredErrors = parseInt(document.getElementById('maxStoredErrors').value);
      
      localStorage.setItem('telemetry_config', JSON.stringify(config));
      document.getElementById('message').textContent = 'Settings applied. Refresh the application page to apply changes.';
    }
    
    function refreshStatistics() {
      // Try to get telemetry data from storage
      let telemetryData = [];
      try {
        telemetryData = JSON.parse(localStorage.getItem('supabase_telemetry') || '[]');
      } catch(e) {
        console.error('Error parsing telemetry data:', e);
      }
      
      if (telemetryData.length === 0) {
        document.getElementById('errorsByTable').innerHTML = '<p>No telemetry data available</p>';
        document.getElementById('errorsBySeverity').innerHTML = '<p>No telemetry data available</p>';
        document.getElementById('recentErrors').innerHTML = '<p>No telemetry data available</p>';
        return;
      }
      
      // Process errors by table
      const errorsByTable = {};
      const errorsBySeverity = {};
      const recentErrors = [];
      
      telemetryData.forEach(item => {
        if (item.errorMessage) {
          // Count by table
          if (item.tableName) {
            errorsByTable[item.tableName] = (errorsByTable[item.tableName] || 0) + 1;
          }
          
          // Count by severity
          errorsBySeverity[item.severity] = (errorsBySeverity[item.severity] || 0) + 1;
          
          // Add to recent errors
          recentErrors.push(item);
        }
      });
      
      // Sort recent errors by timestamp (newest first)
      recentErrors.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
      
      // Generate HTML for errorsByTable
      let errorsByTableHtml = '<ul>';
      for (const table in errorsByTable) {
        errorsByTableHtml += `<li><strong>${table}</strong>: ${errorsByTable[table]} errors</li>`;
      }
      errorsByTableHtml += '</ul>';
      
      // Generate HTML for errorsBySeverity
      let errorsBySeverityHtml = '<ul>';
      for (const severity in errorsBySeverity) {
        errorsBySeverityHtml += `<li><strong>${severity}</strong>: ${errorsBySeverity[severity]} errors</li>`;
      }
      errorsBySeverityHtml += '</ul>';
      
      // Generate HTML for recentErrors (show most recent 5)
      let recentErrorsHtml = '<ul>';
      recentErrors.slice(0, 5).forEach(error => {
        const date = new Date(error.timestamp).toLocaleString();
        recentErrorsHtml += `<li><strong>${date}</strong>: ${error.operation} - ${error.errorMessage}</li>`;
      });
      recentErrorsHtml += '</ul>';
      
      // Update the DOM
      document.getElementById('errorsByTable').innerHTML = errorsByTableHtml;
      document.getElementById('errorsBySeverity').innerHTML = errorsBySeverityHtml;
      document.getElementById('recentErrors').innerHTML = recentErrorsHtml;
    }
    
    function clearTelemetry() {
      localStorage.removeItem('supabase_telemetry');
      document.getElementById('errorsByTable').innerHTML = '<p>No telemetry data available</p>';
      document.getElementById('errorsBySeverity').innerHTML = '<p>No telemetry data available</p>';
      document.getElementById('recentErrors').innerHTML = '<p>No telemetry data available</p>';
      document.getElementById('message').textContent = 'Telemetry data cleared';
    }
    
    function resetSupabaseClient() {
      try {
        // Try to call the resetSupabaseClient function from the parent window
        if (window.opener && window.opener.resetSupabaseClient) {
          window.opener.resetSupabaseClient();
          document.getElementById('message').textContent = 'Supabase client reset successfully';
        } else {
          document.getElementById('message').textContent = 'Cannot reset Supabase client from this window. Please refresh the application.';
        }
      } catch (e) {
        document.getElementById('message').textContent = 'Error resetting Supabase client: ' + e.message;
      }
    }
    
    function refreshApplication() {
      try {
        if (window.opener) {
          window.opener.location.reload();
          document.getElementById('message').textContent = 'Application refreshed';
        } else {
          document.getElementById('message').textContent = 'Cannot refresh application from this window.';
        }
      } catch(e) {
        document.getElementById('message').textContent = 'Error refreshing application: ' + e.message;
      }
    }
    
    // Initialize
    checkStatus();
    refreshStatistics();
  </script>
</body>
</html>
