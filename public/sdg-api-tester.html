<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SDG API Tester</title>
  <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
  <style>
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3 { color: #008761; }
    h2 { margin-top: 2rem; border-bottom: 1px solid #e2e8f0; padding-bottom: 0.5rem; }
    
    pre {
      background-color: #f4f4f4;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
    }
    
    .grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
    }
    
    @media (max-width: 768px) {
      .grid {
        grid-template-columns: 1fr;
      }
    }
    
    .panel {
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      padding: 16px;
    }
    
    button, .button {
      background-color: #008761;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      margin-top: 10px;
      display: inline-block;
      text-decoration: none;
    }
    
    button:hover, .button:hover {
      background-color: #006a4e;
    }
    
    .form-group {
      margin-bottom: 15px;
    }
    
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    
    input[type="text"], 
    select, 
    textarea {
      width: 100%;
      padding: 8px;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-family: inherit;
      font-size: inherit;
    }
    
    textarea {
      height: 100px;
    }
    
    .actions {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
    }
    
    .tab-buttons {
      display: flex;
      gap: 5px;
      margin-bottom: 20px;
      border-bottom: 1px solid #e2e8f0;
      padding-bottom: 5px;
    }
    
    .tab-button {
      background-color: #f3f4f6;
      border: 1px solid #e2e8f0;
      border-radius: 4px 4px 0 0;
      padding: 8px 16px;
      cursor: pointer;
    }
    
    .tab-button.active {
      background-color: #008761;
      color: white;
      border-color: #008761;
    }
    
    .tab-content {
      display: none;
    }
    
    .tab-content.active {
      display: block;
    }
    
    .status {
      padding: 10px;
      border-radius: 4px;
      margin-top: 10px;
    }
    
    .status.success {
      background-color: #d1fae5;
      color: #065f46;
      border: 1px solid #34d399;
    }
    
    .status.error {
      background-color: #fee2e2;
      color: #991b1b;
      border: 1px solid #f87171;
    }
    
    .status.warning {
      background-color: #fef3c7;
      color: #92400e;
      border: 1px solid #fbbf24;
    }
    
    .status.info {
      background-color: #dbeafe;
      color: #1e40af;
      border: 1px solid #60a5fa;
    }
    
    .top-actions {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
      flex-wrap: wrap;
    }
    
    .help-text {
      margin-top: 4px;
      font-size: 0.9em;
      color: #666;
    }
    
    .badge {
      display: inline-block;
      padding: 2px 8px;
      background-color: #e2e8f0;
      border-radius: 9999px;
      font-size: 0.75rem;
      margin-right: 5px;
    }
    
    #header {
      display: flex;
      align-items: center;
    }
    
    #header svg {
      margin-right: 15px;
      height: 40px;
      width: 40px;
    }
    
    #resultsContainer {
      max-height: 600px;
      overflow-y: auto;
    }
  </style>
</head>
<body>
  <div id="header">
    <svg width="40" height="40" viewBox="0 0 109 113" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0627L99.1935 40.0627C107.384 40.0627 111.952 49.5228 106.859 55.9374L63.7076 110.284Z" fill="url(#paint0_linear)"/>
      <path d="M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0627L99.1935 40.0627C107.384 40.0627 111.952 49.5228 106.859 55.9374L63.7076 110.284Z" fill="url(#paint1_linear)" fill-opacity="0.2"/>
      <path d="M45.317 2.07103C48.1765 -1.53037 53.9745 0.442937 54.0434 5.041L54.4849 72.2922H9.83113C1.64038 72.2922 -2.92775 62.8321 2.1655 56.4175L45.317 2.07103Z" fill="#008761"/>
      <defs>
      <linearGradient id="paint0_linear" x1="53.9738" y1="54.974" x2="94.1635" y2="71.8295" gradientUnits="userSpaceOnUse">
      <stop stop-color="#005e44"/>
      <stop offset="1" stop-color="#008761"/>
      </linearGradient>
      <linearGradient id="paint1_linear" x1="36.1558" y1="30.578" x2="54.4844" y2="106.178" gradientUnits="userSpaceOnUse">
      <stop stop-color="white"/>
      <stop offset="1" stop-color="white" stop-opacity="0"/>
      </linearGradient>
      </defs>
    </svg>
    <h1>SDG API Tester</h1>
  </div>
  
  <div class="top-actions">
    <div>
      <a href="check-supabase-connection.html" class="button">Check Connection</a>
      <a href="fix-env-variables.html" class="button">Fix Environment Variables</a>
    </div>
    <div>
      <button id="loadCredentialsBtn">Load Credentials</button>
      <button id="saveCredentialsBtn">Save Credentials</button>
    </div>
  </div>
  
  <div class="grid">
    <div class="panel">
      <h2>Configuration</h2>
      
      <div class="form-group">
        <label for="supabaseUrl">Supabase URL</label>
        <input type="text" id="supabaseUrl" placeholder="https://your-project-id.supabase.co">
      </div>
      
      <div class="form-group">
        <label for="secretKey">Edge Function Secret</label>
        <input type="text" id="secretKey" placeholder="Enter your edge function secret">
      </div>
      
      <div class="tab-buttons">
        <button class="tab-button active" data-tab="debugTab">Debug</button>
        <button class="tab-button" data-tab="testTab">Test</button>
        <button class="tab-button" data-tab="goalsTab">Goals</button>
        <button class="tab-button" data-tab="rankingsTab">Rankings</button>
      </div>
      
      <div id="debugTab" class="tab-content active">
        <h3>Debug Edge Function</h3>
        <p>Test the debug Edge Function to verify environment setup and authentication.</p>
        <button id="testDebugBtn">Call Debug Function</button>
      </div>
      
      <div id="testTab" class="tab-content">
        <h3>Test SDG Edge Function</h3>
        <p>Test the SDG ETL Edge Function with a simple request.</p>
        <button id="testSdgBtn">Test SDG Function</button>
      </div>
      
      <div id="goalsTab" class="tab-content">
        <h3>Get SDG Goals</h3>
        <p>Fetch, process and store SDG goals information.</p>
        
        <div class="form-group">
          <label for="goalsLang">Language</label>
          <select id="goalsLang">
            <option value="en">English</option>
            <option value="ar">Arabic</option>
          </select>
        </div>
        
        <button id="fetchGoalsBtn">Fetch Goals</button>
      </div>
      
      <div id="rankingsTab" class="tab-content">
        <h3>Calculate Rankings</h3>
        <p>Calculate rankings for indicators based on SDG data.</p>
        
        <div class="form-group">
          <label for="indicatorCode">Indicator Code (Optional)</label>
          <input type="text" id="indicatorCode" placeholder="e.g., 1.1.1">
          <div class="help-text">Leave empty to calculate for all indicators</div>
        </div>
        
        <div class="form-group">
          <label for="rankingYear">Year</label>
          <input type="text" id="rankingYear" placeholder="e.g., 2023">
        </div>
        
        <div class="form-group">
          <label>
            <input type="checkbox" id="calcBenchmarks">
            Calculate benchmarks
          </label>
        </div>
        
        <button id="calculateRankingsBtn">Calculate Rankings</button>
      </div>
    </div>
    
    <div class="panel">
      <h2>Results</h2>
      <div id="statusContainer"></div>
      <div id="resultsContainer">
        <pre id="results">No results yet. Use the controls on the left to interact with the SDG API.</pre>
      </div>
    </div>
  </div>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Elements
      const supabaseUrlInput = document.getElementById('supabaseUrl');
      const secretKeyInput = document.getElementById('secretKey');
      const resultsContainer = document.getElementById('results');
      const statusContainer = document.getElementById('statusContainer');
      
      // Tab Switching
      document.querySelectorAll('.tab-button').forEach(button => {
        button.addEventListener('click', () => {
          // Remove active class from all tabs
          document.querySelectorAll('.tab-button').forEach(btn => {
            btn.classList.remove('active');
          });
          document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
          });
          
          // Add active class to current tab
          button.classList.add('active');
          document.getElementById(button.dataset.tab).classList.add('active');
        });
      });
      
      // Load credentials from localStorage
      document.getElementById('loadCredentialsBtn').addEventListener('click', () => {
        supabaseUrlInput.value = localStorage.getItem('VITE_SUPABASE_URL') || '';
        secretKeyInput.value = localStorage.getItem('VITE_EDGE_FUNCTION_SECRET') || '';
        showStatus('Credentials loaded from localStorage', 'info');
      });
      
      // Save credentials to localStorage
      document.getElementById('saveCredentialsBtn').addEventListener('click', () => {
        localStorage.setItem('VITE_SUPABASE_URL', supabaseUrlInput.value);
        localStorage.setItem('VITE_EDGE_FUNCTION_SECRET', secretKeyInput.value);
        showStatus('Credentials saved to localStorage', 'success');
      });
      
      // Test Debug Function
      document.getElementById('testDebugBtn').addEventListener('click', async () => {
        try {
          showStatus('Testing debug function...', 'info');
          const response = await callFunction('debug', {});
          displayResults(response);
          showStatus('Debug function call successful', 'success');
        } catch (error) {
          showStatus(`Error: ${error.message}`, 'error');
          displayResults({ error: error.message });
        }
      });
      
      // Test SDG Function
      document.getElementById('testSdgBtn').addEventListener('click', async () => {
        try {
          showStatus('Testing SDG ETL function...', 'info');
          const response = await callFunction('sdg-etl', { action: 'test' });
          displayResults(response);
          showStatus('SDG function call successful', 'success');
        } catch (error) {
          showStatus(`Error: ${error.message}`, 'error');
          displayResults({ error: error.message });
        }
      });
      
      // Fetch Goals
      document.getElementById('fetchGoalsBtn').addEventListener('click', async () => {
        try {
          const lang = document.getElementById('goalsLang').value;
          showStatus(`Fetching goals in ${lang}...`, 'info');
          
          const response = await callFunction('sdg-etl', { 
            action: 'goals',
            lang: lang
          });
          
          displayResults(response);
          showStatus('Goals fetched successfully', 'success');
        } catch (error) {
          showStatus(`Error: ${error.message}`, 'error');
          displayResults({ error: error.message });
        }
      });
      
      // Calculate Rankings
      document.getElementById('calculateRankingsBtn').addEventListener('click', async () => {
        try {
          const indicatorCode = document.getElementById('indicatorCode').value;
          const year = document.getElementById('rankingYear').value || new Date().getFullYear() - 1;
          const calcBenchmarks = document.getElementById('calcBenchmarks').checked;
          
          showStatus(`Calculating rankings${indicatorCode ? ' for ' + indicatorCode : ''}...`, 'info');
          
          const params = { 
            action: 'rankings',
            year: parseInt(year),
            calculateBenchmarks: calcBenchmarks
          };
          
          if (indicatorCode) {
            params.indicatorCode = indicatorCode;
          }
          
          const response = await callFunction('sdg-etl', params);
          displayResults(response);
          showStatus('Rankings calculated successfully', 'success');
        } catch (error) {
          showStatus(`Error: ${error.message}`, 'error');
          displayResults({ error: error.message });
        }
      });
      
      // Helper function to call Edge Functions
      async function callFunction(functionName, body) {
        const url = document.getElementById('supabaseUrl').value;
        const secret = document.getElementById('secretKey').value;
        
        if (!url || !secret) {
          throw new Error('Please enter Supabase URL and Edge Function Secret');
        }
        
        try {
          const response = await fetch(`${url}/functions/v1/${functionName}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${secret}`
            },
            body: JSON.stringify(body)
          });
          
          if (!response.ok) {
            const errorBody = await response.text();
            let errorMsg;
            try {
              const errorJson = JSON.parse(errorBody);
              errorMsg = errorJson.error || errorJson.message || `${response.status}: ${response.statusText}`;
            } catch {
              errorMsg = errorBody || `${response.status}: ${response.statusText}`;
            }
            throw new Error(errorMsg);
          }
          
          return await response.json();
        } catch (error) {
          throw error;
        }
      }
      
      // Display results in the results container
      function displayResults(data) {
        resultsContainer.textContent = JSON.stringify(data, null, 2);
      }
      
      // Show status message
      function showStatus(message, type) {
        statusContainer.innerHTML = `<div class="status ${type}">${message}</div>`;
      }
      
      // Initialize with saved values
      if (localStorage.getItem('VITE_SUPABASE_URL')) {
        supabaseUrlInput.value = localStorage.getItem('VITE_SUPABASE_URL');
      }
      
      if (localStorage.getItem('VITE_EDGE_FUNCTION_SECRET')) {
        secretKeyInput.value = localStorage.getItem('VITE_EDGE_FUNCTION_SECRET');
      }
      
      // Set default year to last year
      document.getElementById('rankingYear').value = new Date().getFullYear() - 1;
    });
  </script>
</body>
</html>
